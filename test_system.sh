#!/bin/bash

# 期货系统完整测试脚本
# 包含数据生产者、Kafka作业和结果验证

echo "======== 期货系统完整测试脚本 ========"

# 默认配置
ENVIRONMENT="local"
KAFKA_SERVER="localhost:9092"
TEST_DURATION=30  # 测试持续时间（秒）
MAX_RECORDS=20    # 每种数据类型的最大记录数

# 解析参数
while [[ $# -gt 0 ]]; do
    case $1 in
        --env)
            ENVIRONMENT="$2"
            shift 2
            ;;
        --kafka-server)
            KAFKA_SERVER="$2"
            shift 2
            ;;
        --duration)
            TEST_DURATION="$2"
            shift 2
            ;;
        --max-records)
            MAX_RECORDS="$2"
            shift 2
            ;;
        --help)
            echo "使用方法: $0 [选项]"
            echo "选项:"
            echo "  --env ENV              环境 (local/server, 默认: local)"
            echo "  --kafka-server SERVER  Kafka服务器 (默认: localhost:9092)"
            echo "  --duration SECONDS     测试持续时间 (默认: 30)"
            echo "  --max-records NUM      每种数据最大记录数 (默认: 20)"
            echo "  --help                 显示帮助信息"
            exit 0
            ;;
        *)
            echo "未知选项: $1"
            echo "使用 --help 查看帮助信息"
            exit 1
            ;;
    esac
done

# 根据环境调整配置
if [ "$ENVIRONMENT" = "server" ]; then
    if [ "$KAFKA_SERVER" = "localhost:9092" ]; then
        KAFKA_SERVER="************:8087"
    fi
fi

echo "测试配置:"
echo "  环境: $ENVIRONMENT"
echo "  Kafka服务器: $KAFKA_SERVER"
echo "  测试持续时间: ${TEST_DURATION}秒"
echo "  最大记录数: $MAX_RECORDS"
echo ""

# 检查依赖
echo "检查测试环境..."

# 检查Python环境
if ! command -v python3 &> /dev/null; then
    echo "错误: 未找到python3"
    exit 1
fi

# 检查Java环境
if ! command -v java &> /dev/null; then
    echo "错误: 未找到java"
    exit 1
fi

# 检查Maven
if ! command -v mvn &> /dev/null; then
    echo "错误: 未找到maven"
    exit 1
fi

# 检查Python依赖
python3 -c "import kafka" 2>/dev/null
if [ $? -ne 0 ]; then
    echo "安装Python Kafka依赖..."
    pip3 install kafka-python==2.0.2
    if [ $? -ne 0 ]; then
        echo "错误: 无法安装kafka-python，请手动安装"
        exit 1
    fi
fi

echo "环境检查通过"
echo ""

# 编译Java项目
echo "编译项目..."
mvn clean compile -q
if [ $? -ne 0 ]; then
    echo "编译失败！"
    exit 1
fi
echo "项目编译成功"
echo ""

# 创建日志目录
mkdir -p logs

# 启动Kafka作业（后台运行）
echo "启动Kafka订单簿重建作业..."
nohup java -cp "target/classes:$(mvn dependency:build-classpath -q -Dmdep.outputFile=/dev/stdout)" \
     com.futures.job.FuturesOrderBookKafkaJob "$ENVIRONMENT" \
     > logs/kafka_job_output.log 2>&1 &

KAFKA_JOB_PID=$!
echo "Kafka作业已启动 (PID: $KAFKA_JOB_PID)"

# 等待作业启动
echo "等待Kafka作业初始化..."
sleep 10

# 启动数据生产者
echo "启动数据生产者，发送测试数据..."
python3 kafka_producer.py \
    --kafka-server "$KAFKA_SERVER" \
    --max-records "$MAX_RECORDS" \
    --delay-ms 500 \
    > logs/producer_output.log 2>&1 &

PRODUCER_PID=$!
echo "数据生产者已启动 (PID: $PRODUCER_PID)"

# 监控测试进度
echo ""
echo "测试进行中..."
echo "监控日志文件:"
echo "  Kafka作业日志: logs/kafka_job_output.log"
echo "  生产者日志: logs/producer_output.log"
echo ""

# 显示实时日志（前几行）
show_logs() {
    echo "=== Kafka作业输出 (最新10行) ==="
    tail -10 logs/kafka_job_output.log 2>/dev/null || echo "暂无日志输出"
    echo ""
    echo "=== 生产者输出 (最新5行) ==="
    tail -5 logs/producer_output.log 2>/dev/null || echo "暂无日志输出"
    echo ""
}

# 等待测试完成
for i in $(seq 1 $TEST_DURATION); do
    sleep 1
    if [ $((i % 10)) -eq 0 ]; then
        echo "测试进度: $i/${TEST_DURATION}秒"
        show_logs
    fi
done

echo ""
echo "测试时间结束，正在清理..."

# 清理进程
cleanup() {
    echo "停止测试进程..."
    if kill -0 $PRODUCER_PID 2>/dev/null; then
        kill $PRODUCER_PID
        echo "生产者进程已停止"
    fi
    
    if kill -0 $KAFKA_JOB_PID 2>/dev/null; then
        kill $KAFKA_JOB_PID
        echo "Kafka作业进程已停止"
    fi
}

# 设置清理陷阱
trap cleanup EXIT

# 等待生产者完成
wait $PRODUCER_PID 2>/dev/null
echo "数据生产者已完成"

# 额外等待，让Kafka作业处理完剩余消息
echo "等待Kafka作业处理完成..."
sleep 5

# 显示最终结果
echo ""
echo "======== 测试结果摘要 ========"

# 分析Kafka作业日志
if [ -f "logs/kafka_job_output.log" ]; then
    echo "Kafka作业结果:"
    
    # 统计订单簿快照数量
    snapshot_count=$(grep -c "全量订单簿" logs/kafka_job_output.log 2>/dev/null || echo "0")
    echo "  生成订单簿快照: $snapshot_count 次"
    
    # 统计PnL计算结果
    pnl_count=$(grep -c "PnL结果" logs/kafka_job_output.log 2>/dev/null || echo "0")
    echo "  PnL计算结果: $pnl_count 条"
    
    # 统计异常交易
    anomaly_count=$(grep -c "异常交易" logs/kafka_job_output.log 2>/dev/null || echo "0")
    echo "  检测到异常交易: $anomaly_count 条"
    
    # 显示最后几条关键日志
    echo ""
    echo "最新的订单簿快照 (最后1条):"
    grep "全量订单簿" logs/kafka_job_output.log | tail -1 || echo "  无快照输出"
    
    echo ""
    echo "最新的PnL结果 (最后3条):"
    grep "PnL结果" logs/kafka_job_output.log | tail -3 || echo "  无PnL结果"
    
else
    echo "未找到Kafka作业日志文件"
fi

echo ""

# 分析生产者日志
if [ -f "logs/producer_output.log" ]; then
    echo "数据生产者结果:"
    
    # 统计发送的消息数量
    single_leg_sent=$(grep "单腿订单发送完成" logs/producer_output.log | tail -1 | grep -o "成功 [0-9]*" | grep -o "[0-9]*" 2>/dev/null || echo "0")
    combo_sent=$(grep "组合订单发送完成" logs/producer_output.log | tail -1 | grep -o "成功 [0-9]*" | grep -o "[0-9]*" 2>/dev/null || echo "0")
    trade_sent=$(grep "交易数据发送完成" logs/producer_output.log | tail -1 | grep -o "成功 [0-9]*" | grep -o "[0-9]*" 2>/dev/null || echo "0")
    
    echo "  发送单腿订单: $single_leg_sent 条"
    echo "  发送组合订单: $combo_sent 条"
    echo "  发送交易数据: $trade_sent 条"
    
    total_sent=$((single_leg_sent + combo_sent + trade_sent))
    echo "  总计发送: $total_sent 条消息"
    
else
    echo "未找到生产者日志文件"
fi

echo ""
echo "======== 测试完成 ========"
echo "详细日志文件位于 logs/ 目录"
echo "如需查看完整日志:"
echo "  cat logs/kafka_job_output.log"
echo "  cat logs/producer_output.log"

# 提供后续建议
echo ""
echo "后续测试建议:"
echo "1. 调整 --max-records 参数测试更多数据"
echo "2. 调整 --duration 参数进行更长时间的测试"
echo "3. 使用 --env server 测试服务器环境"
echo "4. 监控系统资源使用情况"
