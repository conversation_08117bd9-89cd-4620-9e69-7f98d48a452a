# 期货订单簿与盈亏分析系统逻辑梳理

本文档根据用户需求和现有代码 `FuturesOrderBookKafkaJob.java`，对整个期货订单簿与盈亏分析系统的逻辑进行梳理和详细解析。

## 一、用户需求

> 我需要每0.5s生成全量订单簿，并且获取每天的会员盈亏情况，其中比较重要的字段在data的csv文件中，ord_sts状态为0时表示全部成交，为3时表示未成交在队列中，1表示已成交在队列中，5表示已撤销，一般委托可能表示为3->1->0,3->5,3->0,交易数据同一个trd_nbr有两条数据，表示买卖，trd_type的"2"表示组合委托成交；组合委托可能有多轮，同时组合委托的两腿都要考虑，每次都实时虚拟挂-单，但是由于这样会对订单簿产生影响，所以采用基础层和虚拟层，单腿挂在基础层，组合委托挂在虚拟层。

## 二、系统整体架构分析

根据您的需求和现有代码，这个系统是基于 **Apache Flink** 构建的实时流处理系统，旨在处理期货市场的订单和交易数据，以实现两个核心目标：

1.  **实时构建全量订单簿 (Order Book)**
2.  **实时计算会员盈亏 (PnL)**

下面是整个系统逻辑的详细分解：

### 1. 系统整体架构

系统遵循典型的流处理架构：**数据源 (Source) -> 数据处理 (Transformation) -> 数据输出 (Sink)**。

-   **数据源 (Source)**:
    -   系统从 **Kafka** 的三个不同主题 (Topic) 中消费实时数据：
        -   `single_leg_orders`: 单腿委托订单数据。
        -   `combination_orders`: 组合委托订单数据。
        -   `trades`: 交易成交数据。
    -   这些数据流都设置了**Watermark**（水位线），用于处理乱序事件和定义事件时间窗口，确保时间驱动的计算（如每日盈亏）的准确性。

-   **数据处理 (Transformation)**: 这是系统的核心，包含了一系列复杂的逻辑步骤。
    -   **数据解析**: 从 Kafka 消费的原始 JSON 字符串被解析为结构化的 Java 对象（`SingleLegOrderEvent`, `CombinationOrderEvent`, `TradeEvent`）。
    -   **组合订单拆分**: 组合订单（如跨期套利）本身不直接进入订单簿。系统通过 `CombinationOrderSplitter` 函数，将其拆分为两个或多个独立的单腿订单（`CombinationOrderLegInfo`）。这是实现“组合委托的两腿都要考虑”的关键步骤。
    -   **订单流合并**: 拆分后的组合订单腿与原始的单腿订单流合并（`union`），形成一个统一的、全量的订单事件流。
    -   **订单簿重建**:
        -   这是最核心的功能，由 `OrderBookReconstructionFunction` 实现。
        -   所有订单事件按 **合约代码 (`contract_cde`)** 进行分组（`keyBy`）。
        -   对于每个合约，Flink 会维护一个状态（State），该状态就是一个完整的买卖订单簿（Bid/Ask Taker）。
        -   当新的订单事件到达时，该函数会根据 **订单状态 (`ord_sts`)** 更新订单簿。
    -   **盈亏(PnL)计算**:
        -   由 `PnLCalculationFunction` 实现，处理的是 `trades` 数据流。
        -   数据流按 **会员号 (`settle_memb_memb_cde`)** 分组。
        -   对于每个会员，函数会维护其 **持仓 (`Position`)** 状态。
        -   当一笔交易数据 (`TradeEvent`) 到达时，系统会更新持仓并计算 **已实现盈亏 (Realized PnL)**，同时通过侧输出流输出 **每日持仓盈亏 (Mark-to-Market PnL)** 和 **异常交易**。
    -   **全局快照聚合**:
        -   各个合约的订单簿（`OrderBookSnapshot`）被发送到一个 **并行度为1** 的 `GlobalSnapshotAggregator` 算子，聚合成一个单一的、完整的市场全量订单簿视图。

-   **数据输出 (Sink)**:
    -   **全量订单簿**: 聚合后的全局订单簿快照被打印到控制台。
    -   **PnL 结果**: 每个会员的 PnL 计算结果被格式化后打印。
    -   **监控信息**: 异常交易和每日持仓盈亏等重要的监控信息也会被打印出来。

### 2. 针对具体需求的实现方案

1.  **每 0.5 秒生成全量订单簿**:
    -   **当前实现**: 目前的 `GlobalSnapshotAggregator` 是事件驱动的。
    -   **改进方案**: 要实现定时输出，需要修改 `GlobalSnapshotAggregator`。可以在该函数中注册一个**处理时间定时器 (Processing Time Timer)**，每 500 毫秒触发一次，定时将当前聚合的最新全局订单簿快照向下游发送。

2.  **获取每天的会员盈亏**:
    -   这部分逻辑已经在 `PnLCalculationFunction` 中实现。它通过事件时间处理，能够按天聚合和计算盈亏。`mtmPnlStream` 侧输出流正是用于输出每日的持仓盈亏。

3.  **组合委托与虚拟层**:
    -   **当前实现**: 系统将组合委托拆分成的单腿直接并入了主订单流。
    -   **“基础层+虚拟层”方案**: 这是一个很好的优化思路。可以在 `OrderBookReconstructionFunction` 中维护**两个订单簿状态**：`baseOrderBook`（基础层）和 `virtualOrderBook`（虚拟层）。根据订单类型（`SingleLegOrderEvent` 或 `CombinationOrderLegInfo`）更新不同的订单簿，从而实现分层。

---

## 三、订单簿实现逻辑详解 (代码级)

> **用户提问:** 你能对照代码给我讲解订单簿实现的逻辑吗？越详细越好

订单簿的实现逻辑是一个精巧的数据处理流水线，将离散的订单事件流，实时地转换成一个完整的市场深度快照。

### 第 1 步：数据源准备与订单流统一

从 Kafka 接收 `single_leg_orders` 和 `combination_orders` 数据，并解析为 `DataStream<SingleLegOrderEvent>` 和 `DataStream<CombinationOrderEvent>`。

```java
// 解析单腿订单
DataStream<SingleLegOrderEvent> singleLegStream = ...;
// 解析组合订单
DataStream<CombinationOrderEvent> combStream = ...;
```

### 第 2 步：拆分组合订单为独立的腿

使用 `CombinationOrderSplitter` 将组合订单流 `combStream` 拆分为两个独立的订单腿流。这是通过 **侧输出 (Side Output)** 实现的。

```java
// 拆分组合订单
SingleOutputStreamOperator<Object> combOrderSplitterStream = combStream
        .process(new CombinationOrderSplitter())
        .name("拆分组合订单");

// 从侧输出获取拆分后的两条腿
DataStream<CombinationOrderSplitter.CombinationOrderLegInfo> leg1Stream =
        combOrderSplitterStream.getSideOutput(CombinationOrderSplitter.LEG1_OUTPUT_TAG);
DataStream<CombinationOrderSplitter.CombinationOrderLegInfo> leg2Stream =
        combOrderSplitterStream.getSideOutput(CombinationOrderSplitter.LEG2_OUTPUT_TAG);
```
`CombinationOrderSplitter` 内部接收一个组合订单，生成两个 `CombinationOrderLegInfo` 对象，并通过侧输出发送，而不是主输出。

### 第 3 步：合并所有订单事件

将原始单腿订单流和拆分后的两个组合单腿流合并成一个统一的 `DataStream<Object>`。

```java
// 合并所有订单事件
DataStream<Object> allOrderEvents = singleLegStream.<Object>map(order -> order)
        .union(leg1Stream.map(leg -> (Object) leg))
        .union(leg2Stream.map(leg -> (Object) leg));
```
`allOrderEvents` 现在包含了所有需要进入订单簿的原子性订单操作。

### 第 4 步：按合约代码分区 (KeyBy)

为了对每个合约独立维护订单簿，必须按合约代码对合并后的流进行分区。

```java
DataStream<OrderBookSnapshot> orderBookStream = allOrderEvents
        .keyBy(FuturesOrderBookKafkaJob::extractContractCode) // <-- 核心分区操作
        .process(new OrderBookReconstructionFunction())
        .name("订单簿重建");

// Helper function to get contract code from different types
private static String extractContractCode(Object order) {
    if (order instanceof SingleLegOrderEvent) {
        return ((SingleLegOrderEvent) order).getContract_cde();
    } else if (order instanceof CombinationOrderSplitter.CombinationOrderLegInfo) {
        return ((CombinationOrderSplitter.CombinationOrderLegInfo) order).getCurrentLegContract();
    }
    return "UNKNOWN_CONTRACT";
}
```
`keyBy` 确保了相同合约的所有订单事件都会被发送到同一个 Flink 任务实例上处理。

### 第 5 步：订单簿重建 (`OrderBookReconstructionFunction`)

这是订单簿构建的“大脑”，在每个分区内独立执行。

1.  **状态定义**: 在 `open()` 方法中，定义 Flink 的 `MapState` 来存储买卖订单簿，例如 `MapState<Price, SortedMap<Timestamp, Order>> bids` 和 `asks`，实现价格优先、时间优先。
2.  **处理每个订单 (`processElement`)**: 当一个订单事件到达时，根据其订单状态 `ord_sts` 更新订单簿状态：
    -   **`ord_sts == 3` (新订单)**: 加入到 `bids` 或 `asks` 状态中。
    -   **`ord_sts == 1` (部分成交)**: 更新订单的剩余数量 `rmn_vol`。
    -   **`ord_sts == 0` (全成交) 或 `5` (撤销)**: 从订单簿中移除该订单。
3.  **生成快照**: 每次更新后，根据当前状态生成一个 `OrderBookSnapshot` 对象并发送到下游。

### 第 6 步：全局聚合与输出

将所有单个合约的订单簿快照聚合成一个全量订单簿。

```java
DataStream<String> globalSnapshotStream = orderBookStream
        .keyBy(snapshot -> "GLOBAL_KEY") // 把所有快照都发往同一个实例
        .process(new FuturesOrderBookJob.GlobalSnapshotAggregator())
        .setParallelism(1) // 强制使用单并行度
        .name("全局快照聚合器");

globalSnapshotStream.print("全量订单簿");
```
`GlobalSnapshotAggregator` 内部维护一个 `MapState<String, OrderBookSnapshot>`，不断更新并输出完整的市场视图。

### 总结

订单簿的实现逻辑是一个精巧的数据处理流水线：
**接收原始订单 -> 拆分组合单 -> 合并成统一流 -> 按合约分区 -> 在各分区内独立维护和更新订单簿状态 -> 将各合约快照聚合 -> 输出最终的全量视图**。

这个架构充分利用了 Flink 的状态管理和分区机制，实现了高效、可扩展的实时订单簿重建。
