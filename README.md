# 期货全量订单簿重建系统

基于Apache Flink的期货订单簿实时重建系统，支持单腿订单和组合订单的订单簿重建以及PnL计算。

## 快速开始

### 一键调试（推荐）
```bash
# 1. 快速编译运行
./debug.sh -r

# 2. 检查系统状态
./debug.sh -s

# 3. 生成离线部署包
./debug.sh -d

# 4. 查看帮助
./debug.sh -h
```

### 本地环境调试
```bash
# 1. 编译项目
mvn clean compile

# 2. 运行主程序（推荐）
java --add-opens java.base/java.util=ALL-UNNAMED \
     --add-opens java.base/java.lang=ALL-UNNAMED \
     -cp target/classes:$(mvn dependency:build-classpath -q -Dmdep.outputFile=/dev/stdout) \
     com.futures.job.FuturesOrderBookJob

# 3. 或运行Kafka版本
java --add-opens java.base/java.util=ALL-UNNAMED \
     --add-opens java.base/java.lang=ALL-UNNAMED \
     -cp target/classes:$(mvn dependency:build-classpath -q -Dmdep.outputFile=/dev/stdout) \
     com.futures.job.FuturesOrderBookKafkaJob
```

### 离线服务器部署

#### 方法一：全依赖打包（推荐）
```bash
# 1. 本地打包所有依赖
mvn clean package

# 2. 创建部署包
mkdir futures-deployment
cp target/futures-orderbook-rebuild-1.0.0.jar futures-deployment/
cp -r data futures-deployment/
cp -r logs futures-deployment/
cp run_local.sh futures-deployment/

# 3. 打包上传
tar -czf futures-deployment.tar.gz futures-deployment/

# 4. 服务器端解压运行
tar -xzf futures-deployment.tar.gz
cd futures-deployment
java --add-opens java.base/java.util=ALL-UNNAMED \
     --add-opens java.base/java.lang=ALL-UNNAMED \
     -jar futures-orderbook-rebuild-1.0.0.jar
```

#### 方法二：依赖分离打包
```bash
# 1. 创建lib目录并复制依赖
mvn dependency:copy-dependencies -DoutputDirectory=lib

# 2. 编译项目（不打包依赖）
mvn clean compile

# 3. 创建部署目录结构
mkdir -p futures-deployment/{lib,data,logs,conf}
cp -r target/classes/* futures-deployment/
cp -r lib/* futures-deployment/lib/
cp -r data futures-deployment/
cp -r logs futures-deployment/

# 4. 创建启动脚本
cat > futures-deployment/start.sh << 'EOF'
#!/bin/bash
JAVA_OPTS="--add-opens java.base/java.util=ALL-UNNAMED --add-opens java.base/java.lang=ALL-UNNAMED"
CLASSPATH=".:lib/*"
java $JAVA_OPTS -cp $CLASSPATH com.futures.job.FuturesOrderBookJob
EOF

chmod +x futures-deployment/start.sh

# 5. 打包上传
tar -czf futures-deployment.tar.gz futures-deployment/

# 6. 服务器端运行
tar -xzf futures-deployment.tar.gz
cd futures-deployment
./start.sh
```

## 常用调试命令

### 开发调试
```bash
# 快速编译运行
mvn clean compile && \
java --add-opens java.base/java.util=ALL-UNNAMED \
     --add-opens java.base/java.lang=ALL-UNNAMED \
     -cp target/classes:$(mvn dependency:build-classpath -q -Dmdep.outputFile=/dev/stdout) \
     com.futures.job.FuturesOrderBookJob

# 查看依赖树
mvn dependency:tree

# 查看有效POM
mvn help:effective-pom

# 跳过测试编译
mvn clean compile -DskipTests

# 运行指定测试
mvn test -Dtest=OrderBookLogicTest
```

### 日志调试
```bash
# 启用详细日志
java -Dlog4j.configurationFile=log4j2.xml \
     -Dflink.log.level=DEBUG \
     --add-opens java.base/java.util=ALL-UNNAMED \
     --add-opens java.base/java.lang=ALL-UNNAMED \
     -cp target/classes:$(mvn dependency:build-classpath -q -Dmdep.outputFile=/dev/stdout) \
     com.futures.job.FuturesOrderBookJob

# 重定向输出到文件
java --add-opens java.base/java.util=ALL-UNNAMED \
     --add-opens java.base/java.lang=ALL-UNNAMED \
     -cp target/classes:$(mvn dependency:build-classpath -q -Dmdep.outputFile=/dev/stdout) \
     com.futures.job.FuturesOrderBookJob > logs/output.log 2>&1 &

# 实时查看日志
tail -f logs/output.log
```

### 性能调优
```bash
# JVM性能调优
java -Xms2g -Xmx4g \
     -XX:+UseG1GC \
     -XX:MaxGCPauseMillis=100 \
     -XX:+PrintGCDetails \
     -XX:+PrintGCTimeStamps \
     --add-opens java.base/java.util=ALL-UNNAMED \
     --add-opens java.base/java.lang=ALL-UNNAMED \
     -cp target/classes:$(mvn dependency:build-classpath -q -Dmdep.outputFile=/dev/stdout) \
     com.futures.job.FuturesOrderBookJob

# 启用JMX监控
java -Dcom.sun.management.jmxremote \
     -Dcom.sun.management.jmxremote.port=9999 \
     -Dcom.sun.management.jmxremote.authenticate=false \
     -Dcom.sun.management.jmxremote.ssl=false \
     --add-opens java.base/java.util=ALL-UNNAMED \
     --add-opens java.base/java.lang=ALL-UNNAMED \
     -cp target/classes:$(mvn dependency:build-classpath -q -Dmdep.outputFile=/dev/stdout) \
     com.futures.job.FuturesOrderBookJob
```

### 环境检查
```bash
# 检查Java版本
java -version

# 检查Maven版本  
mvn -version

# 检查项目状态
mvn validate

# 检查编译是否成功
ls -la target/classes/com/futures/job/

# 检查依赖是否下载完整
mvn dependency:resolve

# 验证数据文件
ls -la data/*.json
head -3 data/single_leg_orders.json
```

## 项目结构

```
src/main/java/com/futures/
├── job/                    # 主要作业类
│   └── FuturesOrderBookJob.java    # 主程序入口
├── function/              # 核心处理函数
│   ├── OrderBookReconstructionFunction.java  # 订单簿重建
│   ├── CombinationOrderSplitter.java         # 组合订单拆分
│   └── PnLCalculationFunction.java           # PnL计算
└── pojo/                  # 数据对象
    ├── SingleLegOrderEvent.java      # 单腿订单事件
    ├── CombinationOrderEvent.java    # 组合订单事件  
    ├── TradeEvent.java               # 交易事件
    ├── OrderBookSnapshot.java        # 订单簿快照
    ├── PnLResult.java               # PnL结果
    └── Position.java                # 持仓信息
```

## 核心功能

1. **订单数据解析**: 支持单腿订单、组合订单和交易数据的JSON格式解析
2. **订单簿重建**: 实时重建各合约的买卖盘订单簿
3. **组合订单处理**: 自动拆分组合订单为单腿订单，支持虚拟层计算
4. **PnL计算**: 基于交易数据计算各结算会员的损益
5. **全局快照**: 定期生成全局订单簿快照

## 运行方式

### 本地开发运行
```bash
# 推荐方式：编译后运行
mvn clean compile && \
java --add-opens java.base/java.util=ALL-UNNAMED \
     --add-opens java.base/java.lang=ALL-UNNAMED \
     -cp target/classes:$(mvn dependency:build-classpath -q -Dmdep.outputFile=/dev/stdout) \
     com.futures.job.FuturesOrderBookJob
```

### 打包部署运行
```bash
# Shade插件打包（所有依赖打包进JAR）
mvn clean package

# 直接运行Fat JAR
java --add-opens java.base/java.util=ALL-UNNAMED \
     --add-opens java.base/java.lang=ALL-UNNAMED \
     -jar target/futures-orderbook-rebuild-1.0.0.jar
```

## 离线开发与部署

### 方案一：离线运行包（仅运行）
此方案用于在服务器上直接运行，不能修改代码。

```bash
# 1. 本地创建部署包
./create_deployment_package.sh

# 2. 上传并解压
# scp futures-deployment.tar.gz user@server:/path/
# tar -xzf futures-deployment.tar.gz
# cd futures-deployment

# 3. 运行
./start.sh
```

### 方案二：离线开发包（可修改、编译、运行）
此方案用于在没有网络的服务器上进行代码修改和重新编译。

```bash
# 1. 本地创建离线开发包
./create_dev_package.sh

# 2. 上传并解压
# scp futures-dev-offline.tar.gz user@server:/path/
# tar -xzf futures-dev-offline.tar.gz
# cd futures-dev-offline

# 3. 修改代码
vim src/main/java/com/futures/job/FuturesOrderBookJob.java

# 4. 离线编译
./build.sh

# 5. 运行新编译的程序
./run.sh
```

## 常用命令速查

### 开发调试命令
```bash
# 快速命令（使用调试脚本）
./debug.sh -r          # 编译运行
./debug.sh -k          # 运行Kafka版本  
./debug.sh -s          # 检查状态
./debug.sh -l          # 查看日志
./debug.sh -d          # 生成部署包
./debug.sh --clean     # 清理文件
```

## 数据格式

### 输入数据
- `data/single_leg_orders.json`: 单腿订单数据
- `data/combination_orders.json`: 组合订单数据  
- `data/trades.json`: 交易数据

### 输出结果
- **全量订单簿**: 显示各合约的实时买卖盘
- **PnL结果**: 显示各结算会员的损益情况

## 技术特点

- **流式处理**: 基于Apache Flink实现实时数据处理
- **状态管理**: 使用Flink状态后端管理订单状态
- **容错处理**: 内置异常处理和错误恢复机制
- **高性能**: 支持高并发、低延迟的订单簿重建

## 配置说明

- **快照间隔**: 默认500ms生成一次订单簿快照
- **状态TTL**: 订单状态24小时后自动过期
- **并行度**: 本地调试使用单并行度，生产环境可调整

## 重构内容

本次重构主要完成了以下优化：

1. **代码简化**: 删除了冗余的注释和无用代码
2. **结构优化**: 移除了未使用的模块（fault、validation、metric）
3. **类名简化**: 重命名主类为更直观的名称
4. **中文化**: 将关键信息本地化为中文
5. **依赖清理**: 优化了Maven配置和依赖管理

系统现在更加简洁、高效，便于维护和扩展。
