#!/bin/bash

# 期货全量订单簿重建系统 - 运行脚本

echo "启动期货全量订单簿重建系统..."

# 检查Java版本
if ! command -v java &> /dev/null; then
    echo "错误: 未找到Java，请安装Java 11或更高版本"
    exit 1
fi

# 编译项目
echo "正在编译项目..."
mvn clean compile -q

if [ $? -ne 0 ]; then
    echo "错误: 编译失败"
    exit 1
fi

# 构建classpath
echo "构建类路径..."
mvn dependency:build-classpath -q -Dmdep.outputFile=/tmp/cp.txt > /dev/null 2>&1
CLASSPATH="target/classes:$(cat /tmp/cp.txt)"

# 运行程序
echo "启动程序..."
java --add-opens java.base/java.util=ALL-UNNAMED \
     --add-opens java.base/java.lang=ALL-UNNAMED \
     -cp "$CLASSPATH" \
     com.futures.job.FuturesOrderBookJob

echo "程序执行完成"
