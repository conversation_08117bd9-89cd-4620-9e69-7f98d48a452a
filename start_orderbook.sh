#!/bin/bash

# 期货订单簿系统启动脚本
# 支持本地测试和服务器环境

echo "======== 期货订单簿重建系统启动脚本 ========"

# 检查参数
if [ $# -eq 0 ]; then
    echo "使用方法:"
    echo "  $0 local    - 本地测试环境 (localhost:9092)"
    echo "  $0 server   - 服务器环境 (************:8087)"
    echo ""
    echo "默认使用本地测试环境..."
    ENVIRONMENT="local"
else
    ENVIRONMENT=$1
fi

# 根据环境配置
if [ "$ENVIRONMENT" = "server" ] || [ "$ENVIRONMENT" = "production" ]; then
    echo "配置: 服务器环境"
    KAFKA_SERVER="************:8087"
    KAFKA_GROUP="orderbook-production-group"
    MAX_RECORDS=500
else
    echo "配置: 本地测试环境"
    KAFKA_SERVER="localhost:9092"
    KAFKA_GROUP="orderbook-local-test-group"
    MAX_RECORDS=50
    ENVIRONMENT="local"
fi

echo "Kafka服务器: $KAFKA_SERVER"
echo "消费者组: $KAFKA_GROUP"
echo "最大记录数: $MAX_RECORDS"
echo ""

# 编译项目
echo "编译项目..."
mvn clean compile -q
if [ $? -ne 0 ]; then
    echo "编译失败！"
    exit 1
fi
echo "编译成功"

# 创建日志目录
echo "创建日志目录..."
mkdir -p logs
if [ $? -eq 0 ]; then
    echo "日志目录创建成功: $(pwd)/logs"
else
    echo "日志目录创建失败"
    exit 1
fi

# 检查是否需要启动本地Kafka（仅在本地环境）
if [ "$ENVIRONMENT" = "local" ]; then
    echo ""
    echo "本地测试环境检查："
    echo "请确保已启动本地Kafka服务："
    echo "1. 启动Zookeeper: bin/zookeeper-server-start.sh config/zookeeper.properties"
    echo "2. 启动Kafka: bin/kafka-server-start.sh config/server.properties"
    echo ""
    read -p "Kafka服务已启动？继续运行作业？(y/n): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        echo "取消运行"
        exit 1
    fi
fi

# 创建Kafka主题（如果不存在）
echo "创建Kafka主题..."
create_topic() {
    local topic=$1
    echo "检查/创建主题: $topic"
    
    if [ "$ENVIRONMENT" = "local" ]; then
        # 本地Kafka主题创建（假设Kafka安装在标准位置）
        if command -v kafka-topics.sh &> /dev/null; then
            kafka-topics.sh --create --topic $topic --bootstrap-server $KAFKA_SERVER --partitions 1 --replication-factor 1 2>/dev/null || echo "主题 $topic 可能已存在"
        else
            echo "警告: 未找到kafka-topics.sh，请手动创建主题 $topic"
        fi
    else
        echo "服务器环境: 假设主题 $topic 已存在"
    fi
}

create_topic "singleleg_order_data_event"
create_topic "cmb_order_data_event"
create_topic "trade_data_event"

echo ""
echo "启动Flink作业..."
echo "环境: $ENVIRONMENT"
echo "按 Ctrl+C 停止作业"
echo ""

# 运行Flink作业
java --add-opens=java.base/java.util=ALL-UNNAMED \
     --add-opens=java.base/java.lang=ALL-UNNAMED \
     -cp "target/classes:$(mvn dependency:build-classpath -q -Dmdep.outputFile=/dev/stdout)" \
     com.futures.job.FuturesOrderBookKafkaJob "$ENVIRONMENT"
