import pandas as pd
import json
import random

def process_trades(df):
    trades_list = []
    for index, row in df.iterrows():
        trade_json = {
            "_seq": random.randint(100000000, 200000000),
            "_eno": random.randint(10000000, 20000000),
            "trd_dt": row["trd_dt"].replace('/', '-'),
            "trd_nbr": str(row["trd_nbr"]),
            "b_s_tag": row["b_s_tag"],
            "ord_nbr": str(row["ord_nbr"]),
            "memb_cde": "0110",
            "trd_cde": str(random.randint(10000000, 20000000)),
            "trd_role": "1",
            "memb_fund_acctnbr": str(random.randint(700000000000, 800000000000)),
            "uniq_trd_nbr": str(random.randint(100000000, 200000000)),
            "contpty_uniq_trd_nbr": "",
            "ord_vol": "",
            "contpty_ord_vol": "",
            "ord_type": "",
            "contpty_s_tag": "",
            "contpty_ord_nbr": "",
            "contpty_memb_cde": "",
            "contpty_trd_cde": "",
            "contpty_ocpos_type": "",
            "contpty_unfy_opnacct_cde": "",
            "contpty_fcomp_unfy_cde": "",
            "intv_pasv_tag": "",
            "ord_prc": "",
            "opp_ord_prc": "",
            "ord_tm": "",
            "opp_ord_tm": "",
            "contract_cde": row["contract_cde"],
            "specu_hedg_tag": "S",
            "ord_prc_cndt": "2",
            "ocpos_type": str(row["ocpos_type"]),
            "trd_prc": float(row["trd_prc"]),
            "trd_vol": int(row["trd_vol"]),
            "trd_type": str(row["trd_type"]),
            "seat_nbr": str(random.randint(700000000000000, 800000000000000)),
            "local_ord_nbr": str(random.randint(70000, 80000)),
            "settle_memb_memb_cde": str(row["settle_memb_memb_cde"]),
            "trd_tm": row["trd_tm"],
            "ocr_dt": row["trd_dt"].replace('/', '-'),
            "trd_tm_millisec": row["trd_tm_millisec"],
            "trd_tm_microsec": 0,
            "highfrequ_ord_tag": "0",
            "exch_cde": "D",
            "unfy_opnacct_cde": str(random.randint(40000000, 50000000)),
            "fcomp_unfy_cde": "0158",
            "settle_memb_unfy_cde": "1051",
            "comm_cde": row["contract_cde"][:2] if len(row["contract_cde"]) > 2 else "P",
            "futrs_opt_tag": "F",
            "c_p_tag": "N",
            "exec_prc": "-1",
            "ulyg_contract_cde": row["contract_cde"],
            "rcod_nbr": random.randint(100000000, 200000000)
        }
        trades_list.append(trade_json)
    return trades_list

def process_combination_orders(df):
    combination_orders_list = []
    for index, row in df.iterrows():
        combination_order_json = {
            "_seq": random.randint(100000000, 200000000),
            "_eno": random.randint(400000, 500000),
            "trd_dt": row["trd_dt"].replace('/', '-'),
            "ord_nbr": str(row["ord_nbr"]),
            "memb_cde": "0160",
            "trd_cde": str(random.randint(4000000, 5000000)),
            "seat_nbr": "01602004",
            "contract_cde": f"SPD_{row['leg_1_contract_cde']}_{row['leg_2_contract_cde']}",
            "ord_prc_cndt": "2",
            "b_s_tag": row["b_s_tag"],
            "ocpos_type": str(row["ocpos_type"]),
            "specu_hedg_tag": "S",
            "ord_prc": float(row["trd_prc"]),
            "ord_vol": float(row["trd_vol"] + row["rmn_vol"]),
            "vldprd_type": "3",
            "trd_vol_type": "3",
            "min_trd_vol": 0.0,
            "trig_cndt": "1",
            "trig_prc": 0.0,
            "forcclos_resn": "0",
            "local_ord_nbr": str(random.randint(900000000, 1000000000)),
            "ord_src": "0",
            "ord_sts": str(row["ord_sts"]),
            "ord_type": "3",
            "trd_vol": float(row["trd_vol"]),
            "rmn_vol": float(row["rmn_vol"]),
            "ord_dt": row["trd_dt"].replace('/', '-'),
            "ord_tm": row["trd_tm"],
            "actv_tm": "",
            "susp_tm": "",
            "repel_tm": "",
            "final_alt_seat_nbr": "",
            "settle_memb_memb_cde": str(row["settle_memb_memb_cde"]),
            "final_alt_tm": "09:00:00",
            "ocr_dt": row["trd_dt"].replace('/', '-'),
            "ord_tm_millisec": row["trd_tm_millisec"],
            "ord_tm_microsec": 0,
            "rcnt_renew_tm_millisec": 260,
            "rcnt_renew_tm_microsec": 0,
            "highfrequ_ord_tag": "0",
            "exch_cde": "D",
            "unfy_opnacct_cde": str(random.randint(30000000, 40000000)),
            "fcomp_unfy_cde": "0037",
            "clear_comp_cde": "0037",
            "comm_cde": "SPD",
            "rcod_nbr": random.randint(100000000, 200000000),
            "repel_tm_millisec": 0,
            "repel_tm_microsec": 0,
            "cmb_contract_cde": f"SPD_{row['leg_1_contract_cde']}_{row['leg_2_contract_cde']}",
            "leg_1_contract_cde": row["leg_1_contract_cde"],
            "leg_2_contract_cde": row["leg_2_contract_cde"],
            "leg_1_comm_cde": row['leg_1_contract_cde'][:2] if len(row['leg_1_contract_cde']) > 2 else "PP",
            "leg_2_comm_cde": row['leg_2_contract_cde'][:2] if len(row['leg_2_contract_cde']) > 2 else "PP",
            "cmbord_type": "SPD",
            "futrs_opt_tag": "F",
            "c_p_tag": "N",
            "exec_prc": "-1"
        }
        combination_orders_list.append(combination_order_json)
    return combination_orders_list

def process_single_leg_orders(df):
    single_leg_orders_list = []
    for index, row in df.iterrows():
        single_leg_order_json = {
            "_seq": random.randint(300000000, 400000000),
            "_eno": random.randint(200000000, 300000000),
            "trd_dt": row["trd_dt"].replace('/', '-'),
            "ord_nbr": str(row["ord_nbr"]),
            "memb_cde": "0049",
            "trd_cde": str(random.randint(30000000, 40000000)),
            "seat_nbr": str(random.randint(500000000000000, 600000000000000)),
            "contract_cde": row["contract_cde"],
            "ord_prc_cndt": "2",
            "b_s_tag": row["b_s_tag"],
            "ocpos_type": str(row["ocpos_type"]),
            "specu_hedg_tag": "S",
            "ord_prc": float(row["trd_prc"]),
            "ord_vol": float(row["trd_vol"] + row["rmn_vol"]),
            "vldprd_type": "2",
            "trd_vol_type": "3",
            "min_trd_vol": 0.0,
            "trig_cndt": "1",
            "trig_prc": 0.0,
            "forcclos_resn": "0",
            "local_ord_nbr": str(random.randint(1400000, 1500000)),
            "ord_src": "0",
            "ord_sts": str(row["ord_sts"]),
            "ord_type": "0",
            "trd_vol": float(row["trd_vol"]),
            "rmn_vol": float(row["rmn_vol"]),
            "ord_dt": row["trd_dt"].replace('/', '-'),
            "ord_tm": row["trd_tm"],
            "actv_tm": "",
            "susp_tm": "",
            "repel_tm": "",
            "final_alt_seat_nbr": "",
            "settle_memb_memb_cde": str(row["settle_memb_memb_cde"]),
            "final_alt_tm": row["trd_tm"],
            "ocr_dt": row["trd_dt"].replace('/', '-'),
            "ord_tm_millisec": row["trd_tm_millisec"],
            "ord_tm_microsec": 0,
            "rcnt_renew_tm_millisec": 491,
            "rcnt_renew_tm_microsec": 0,
            "highfrequ_ord_tag": "0",
            "exch_cde": "D",
            "unfy_opnacct_cde": str(random.randint(10000000, 20000000)),
            "fcomp_unfy_cde": "0129",
            "clear_comp_cde": "5239",
            "comm_cde": row["contract_cde"][:1] if "-" not in row["contract_cde"] else row["contract_cde"].split("-")[0][:1],
            "futrs_opt_tag": "O" if "-C-" in row["contract_cde"] or "-P-" in row["contract_cde"] else "F",
            "c_p_tag": "C" if "-C-" in row["contract_cde"] else ("P" if "-P-" in row["contract_cde"] else "N"),
            "exec_prc": row["contract_cde"].split("-")[-1] if "-" in row["contract_cde"] else "-1",
            "ulyg_contract_cde": row["contract_cde"].split("-")[0] if "-" in row["contract_cde"] else row["contract_cde"],
            "rcod_nbr": random.randint(300000000, 400000000),
            "repel_tm_millisec": 0,
            "repel_tm_microsec": 0
        }
        single_leg_orders_list.append(single_leg_order_json)
    return single_leg_orders_list


# Read CSV files
trades_df = pd.read_csv('工作表 1-交易.csv')
combination_orders_df = pd.read_csv('工作表 2-组合委托.csv')
single_leg_orders_df = pd.read_csv('工作表 3-单腿.csv')

# Process dataframes
trades_data = process_trades(trades_df)
combination_orders_data = process_combination_orders(combination_orders_df)
single_leg_orders_data = process_single_leg_orders(single_leg_orders_df)

# Write to separate JSON files
with open('trades.json', 'w', encoding='utf-8') as f:
    json.dump(trades_data, f, ensure_ascii=False, indent=4)

with open('combination_orders.json', 'w', encoding='utf-8') as f:
    json.dump(combination_orders_data, f, ensure_ascii=False, indent=4)

with open('single_leg_orders.json', 'w', encoding='utf-8') as f:
    json.dump(single_leg_orders_data, f, ensure_ascii=False, indent=4)

print("Successfully generated trades.json, combination_orders.json, and single_leg_orders.json")