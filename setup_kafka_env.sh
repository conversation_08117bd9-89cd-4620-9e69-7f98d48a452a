#!/bin/bash

# 期货系统Kafka生产者环境配置脚本
# 用于安装Python依赖和配置环境

echo "======== 期货系统Kafka生产者环境配置 ========"

# 检查Python版本
echo "检查Python版本..."
python3 --version

# 创建虚拟环境（可选）
echo "是否创建Python虚拟环境? (y/n)"
read -r create_venv

if [[ $create_venv =~ ^[Yy]$ ]]; then
    echo "创建虚拟环境..."
    python3 -m venv kafka_env
    source kafka_env/bin/activate
    echo "虚拟环境已激活"
else
    echo "跳过虚拟环境创建"
fi

# 安装依赖包
echo "安装Python依赖包..."
pip3 install --upgrade pip
pip3 install -r requirements.txt

if [ $? -eq 0 ]; then
    echo "依赖包安装成功！"
else
    echo "依赖包安装失败，请检查网络连接和pip配置"
    exit 1
fi

# 给Python脚本添加执行权限
chmod +x kafka_producer.py

echo "======== 环境配置完成 ========"
echo ""
echo "使用说明："
echo "1. 批量发送测试数据（本地）："
echo "   python3 kafka_producer.py --max-records 100"
echo ""
echo "2. 批量发送测试数据（服务器）："
echo "   python3 kafka_producer.py --kafka-server ************:8087 --max-records 100"
echo ""
echo "3. 模拟实时数据发送(5分钟)："
echo "   python3 kafka_producer.py --mode simulation --simulation-duration 5"
echo ""
echo "4. 启动Kafka订单簿作业（本地）："
echo "   ./start_orderbook.sh local"
echo ""
echo "5. 启动Kafka订单簿作业（服务器）："
echo "   ./start_orderbook.sh server"
echo ""
echo "6. 完整系统测试："
echo "   ./test_system.sh --env local --duration 30"
echo ""
echo "7. 查看完整参数说明："
echo "   python3 kafka_producer.py --help"
