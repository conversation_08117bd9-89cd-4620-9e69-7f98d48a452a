#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Kafka主题管理工具
用于清理、创建和管理Kafka主题

Usage:
    python3 kafka_manager.py --clear-all                    # 清理所有主题
    python3 kafka_manager.py --clear-topics combination     # 清理指定主题
    python3 kafka_manager.py --list-topics                  # 列出所有主题
    python3 kafka_manager.py --reset-offsets                # 重置消费者组偏移量

Author: Futures System Team  
Date: 2025-08-20
"""

import subprocess
import sys
import time
import argparse
import logging

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class KafkaManager:
    def __init__(self, kafka_server='localhost:9092'):
        self.kafka_server = kafka_server
        self.topics = {
            'single_leg': 'singleleg_order_data_event',
            'combination': 'cmb_order_data_event', 
            'trade': 'trade_data_event'
        }
        
        # 检查Docker是否运行
        self.check_kafka_running()
    
    def check_kafka_running(self):
        """检查Kafka是否在Docker中运行"""
        try:
            result = subprocess.run(['docker', 'ps'], capture_output=True, text=True)
            if 'kafka' not in result.stdout:
                logger.warning("Kafka容器可能未运行，请先启动Docker Kafka")
        except FileNotFoundError:
            logger.warning("Docker命令未找到，请确保Docker已安装")
    
    def run_kafka_command(self, command, timeout=30):
        """执行Kafka命令"""
        try:
            logger.info(f"执行命令: {' '.join(command)}")
            result = subprocess.run(command, capture_output=True, text=True, timeout=timeout)
            if result.returncode != 0:
                logger.error(f"命令执行失败: {result.stderr}")
                return False
            return True
        except subprocess.TimeoutExpired:
            logger.error(f"命令执行超时: {timeout}秒")
            return False
        except Exception as e:
            logger.error(f"命令执行异常: {e}")
            return False
    
    def clear_all_topics(self):
        """清理所有主题"""
        logger.info("🧹 开始清理所有Kafka主题...")
        
        all_topics = list(self.topics.values())
        return self.clear_topics(all_topics)
    
    def clear_topics(self, topic_list):
        """清理指定主题"""
        if not topic_list:
            logger.warning("没有指定要清理的主题")
            return False
            
        logger.info(f"清理主题: {topic_list}")
        
        # 尝试通过Docker执行Kafka命令
        success = True
        
        for topic in topic_list:
            # 删除主题
            delete_cmd = [
                'docker', 'exec', 'futures-kafka',
                'kafka-topics', '--bootstrap-server', self.kafka_server,
                '--delete', '--topic', topic
            ]
            
            if not self.run_kafka_command(delete_cmd):
                logger.warning(f"删除主题 {topic} 失败（可能不存在）")
            else:
                logger.info(f"✅ 主题 {topic} 删除成功")
        
        # 等待删除完成
        logger.info("等待3秒让删除生效...")
        time.sleep(3)
        
        # 重新创建主题
        for topic in topic_list:
            create_cmd = [
                'docker', 'exec', 'futures-kafka',
                'kafka-topics', '--bootstrap-server', self.kafka_server,
                '--create', '--topic', topic,
                '--partitions', '1',
                '--replication-factor', '1'
            ]
            
            if self.run_kafka_command(create_cmd):
                logger.info(f"✅ 主题 {topic} 重新创建成功")
            else:
                logger.error(f"❌ 主题 {topic} 创建失败")
                success = False
        
        return success
    
    def list_topics(self):
        """列出所有主题"""
        logger.info("📋 列出所有Kafka主题...")
        
        list_cmd = [
            'docker', 'exec', 'futures-kafka',
            'kafka-topics', '--bootstrap-server', self.kafka_server,
            '--list'
        ]
        
        try:
            result = subprocess.run(list_cmd, capture_output=True, text=True)
            if result.returncode == 0:
                topics = result.stdout.strip().split('\n')
                logger.info("当前主题:")
                for topic in topics:
                    if topic.strip():
                        logger.info(f"  - {topic}")
                return topics
            else:
                logger.error(f"列出主题失败: {result.stderr}")
                return []
        except Exception as e:
            logger.error(f"列出主题异常: {e}")
            return []
    
    def reset_consumer_offsets(self, consumer_group='orderbook-local-test-group'):
        """重置消费者组偏移量到最新"""
        logger.info(f"🔄 重置消费者组偏移量: {consumer_group}")
        
        for topic_name, topic in self.topics.items():
            reset_cmd = [
                'docker', 'exec', 'futures-kafka',
                'kafka-consumer-groups', '--bootstrap-server', self.kafka_server,
                '--group', consumer_group,
                '--reset-offsets', '--to-latest',
                '--topic', topic,
                '--execute'
            ]
            
            if self.run_kafka_command(reset_cmd):
                logger.info(f"✅ {topic_name} 偏移量重置成功")
            else:
                logger.warning(f"⚠️  {topic_name} 偏移量重置失败（可能消费者组不存在）")
    
    def describe_consumer_groups(self):
        """描述消费者组状态"""
        logger.info("📊 消费者组状态:")
        
        describe_cmd = [
            'docker', 'exec', 'futures-kafka',
            'kafka-consumer-groups', '--bootstrap-server', self.kafka_server,
            '--describe', '--group', 'orderbook-local-test-group'
        ]
        
        try:
            result = subprocess.run(describe_cmd, capture_output=True, text=True)
            if result.returncode == 0:
                print(result.stdout)
            else:
                logger.warning("消费者组不存在或无权限查看")
        except Exception as e:
            logger.error(f"查看消费者组异常: {e}")

def main():
    parser = argparse.ArgumentParser(description='Kafka主题管理工具')
    parser.add_argument('--kafka-server', default='localhost:9092',
                        help='Kafka服务器地址')
    parser.add_argument('--clear-all', action='store_true',
                        help='清理所有主题')
    parser.add_argument('--clear-topics', nargs='*',
                        choices=['single_leg', 'combination', 'trade'],
                        help='清理指定主题')
    parser.add_argument('--list-topics', action='store_true',
                        help='列出所有主题')
    parser.add_argument('--reset-offsets', action='store_true',
                        help='重置消费者组偏移量到最新')
    parser.add_argument('--describe-groups', action='store_true',
                        help='描述消费者组状态')
    
    args = parser.parse_args()
    
    if not any([args.clear_all, args.clear_topics, args.list_topics, 
                args.reset_offsets, args.describe_groups]):
        parser.print_help()
        return
    
    manager = KafkaManager(args.kafka_server)
    
    try:
        if args.clear_all:
            manager.clear_all_topics()
            
        if args.clear_topics:
            topics_to_clear = [manager.topics[topic] for topic in args.clear_topics 
                             if topic in manager.topics]
            manager.clear_topics(topics_to_clear)
            
        if args.list_topics:
            manager.list_topics()
            
        if args.reset_offsets:
            manager.reset_consumer_offsets()
            
        if args.describe_groups:
            manager.describe_consumer_groups()
            
    except KeyboardInterrupt:
        logger.info("操作被用户中断")
    except Exception as e:
        logger.error(f"操作失败: {e}")

if __name__ == "__main__":
    main()
