#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
期货系统Kafka数据生产者
用于将本地JSON数据发送到Kafka，验证FuturesOrderBookKafkaJob的正确性

Author: Futures System Team
Date: 2025-08-19
"""

import json
import time
import logging
import argparse
from typing import List, Dict, Any
from datetime import datetime, timedelta
from kafka import KafkaProducer
from kafka.admin import KafkaAdminClient, NewTopic
from kafka.errors import KafkaError
import os
import sys

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('/tmp/kafka_producer.log'),
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger(__name__)


class FuturesKafkaProducer:
    """期货系统Kafka生产者"""
    
    def __init__(self, bootstrap_servers: str = "localhost:9092"):
        """
        初始化Kafka生产者
        
        Args:
            bootstrap_servers: Kafka集群地址（默认本地测试）
        """
        self.bootstrap_servers = bootstrap_servers
        self.topics = {
            'single_leg': 'singleleg_order_data_event',
            'combination': 'cmb_order_data_event',
            'trade': 'trade_data_event'
        }
        
        # Kafka生产者配置
        self.producer_config = {
            'bootstrap_servers': [bootstrap_servers],
            'key_serializer': lambda k: k.encode('utf-8') if k else None,
            'value_serializer': lambda v: json.dumps(v, ensure_ascii=False).encode('utf-8'),
            'acks': 1,  # 等待leader确认
            'retries': 3,  # 重试次数
            'batch_size': 16384,  # 批次大小
            'linger_ms': 10,  # 等待时间
            'buffer_memory': 33554432,  # 缓冲区大小
        }
        
        self.producer = None
        self._connect()
    
    def _connect(self):
        """连接到Kafka"""
        try:
            self.producer = KafkaProducer(**self.producer_config)
            logger.info(f"成功连接到Kafka集群: {self.bootstrap_servers}")
        except Exception as e:
            logger.error(f"连接Kafka失败: {e}")
            raise
    
    def clear_kafka_topics(self, topics_to_clear=None):
        """
        清理Kafka主题中的所有消息
        通过删除并重新创建主题来实现快速清理
        
        Args:
            topics_to_clear: 要清理的主题列表，如果为None则清理所有主题
        """
        if topics_to_clear is None:
            topics_to_clear = list(self.topics.values())
        
        try:
            # 创建Kafka管理客户端
            admin_client = KafkaAdminClient(
                bootstrap_servers=[self.bootstrap_servers],
                client_id='kafka_cleaner'
            )
            
            logger.info(f"开始清理Kafka主题: {topics_to_clear}")
            
            # 删除主题
            admin_client.delete_topics(topics_to_clear, timeout_ms=30000)
            logger.info("主题删除成功，等待3秒...")
            time.sleep(3)
            
            # 重新创建主题
            new_topics = [
                NewTopic(name=topic, num_partitions=1, replication_factor=1)
                for topic in topics_to_clear
            ]
            admin_client.create_topics(new_topics, timeout_ms=30000)
            logger.info("主题重新创建成功")
            
            admin_client.close()
            
        except Exception as e:
            logger.warning(f"清理主题失败（可能主题不存在）: {e}")
            # 即使清理失败也继续执行，可能是主题本来就不存在
    
    def load_json_data(self, file_path: str) -> List[Dict[Any, Any]]:
        """
        加载JSON数据文件
        
        Args:
            file_path: JSON文件路径
            
        Returns:
            数据列表
        """
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
            logger.info(f"成功加载JSON文件: {file_path}, 记录数: {len(data)}")
            return data
        except Exception as e:
            logger.error(f"加载JSON文件失败 {file_path}: {e}")
            return []
    
    def send_single_leg_orders(self, data: List[Dict[Any, Any]], delay_ms: int = 100) -> int:
        """
        发送单腿订单数据
        
        Args:
            data: 单腿订单数据列表
            delay_ms: 发送间隔（毫秒）
            
        Returns:
            发送成功的消息数量
        """
        topic = self.topics['single_leg']
        success_count = 0
        
        logger.info(f"开始发送单腿订单数据到Topic: {topic}")
        
        for i, record in enumerate(data):
            try:
                # 使用订单号作为key，确保同一订单的消息顺序
                key = record.get('ord_nbr', str(i))
                
                # 添加事件时间戳（如果没有的话）
                if 'event_timestamp' not in record:
                    record['event_timestamp'] = int(time.time() * 1000)
                
                # 发送消息
                future = self.producer.send(topic, key=key, value=record)
                future.get(timeout=10)  # 等待确认
                
                success_count += 1
                
                if (i + 1) % 100 == 0:
                    logger.info(f"已发送单腿订单: {i + 1}/{len(data)}")
                
                # 控制发送速率
                if delay_ms > 0:
                    time.sleep(delay_ms / 1000.0)
                    
            except KafkaError as e:
                logger.error(f"发送单腿订单失败 {i}: {e}")
            except Exception as e:
                logger.error(f"发送单腿订单异常 {i}: {e}")
        
        logger.info(f"单腿订单发送完成: 成功 {success_count}/{len(data)}")
        return success_count
    
    def send_combination_orders(self, data: List[Dict[Any, Any]], delay_ms: int = 100) -> int:
        """
        发送组合订单数据
        
        Args:
            data: 组合订单数据列表
            delay_ms: 发送间隔（毫秒）
            
        Returns:
            发送成功的消息数量
        """
        topic = self.topics['combination']
        success_count = 0
        
        logger.info(f"开始发送组合订单数据到Topic: {topic}")
        
        for i, record in enumerate(data):
            try:
                # 使用订单号作为key
                key = record.get('ord_nbr', str(i))
                
                # 添加事件时间戳
                if 'event_timestamp' not in record:
                    record['event_timestamp'] = int(time.time() * 1000)
                
                # 发送消息
                future = self.producer.send(topic, key=key, value=record)
                future.get(timeout=10)
                
                success_count += 1
                
                if (i + 1) % 50 == 0:
                    logger.info(f"已发送组合订单: {i + 1}/{len(data)}")
                
                # 控制发送速率
                if delay_ms > 0:
                    time.sleep(delay_ms / 1000.0)
                    
            except KafkaError as e:
                logger.error(f"发送组合订单失败 {i}: {e}")
            except Exception as e:
                logger.error(f"发送组合订单异常 {i}: {e}")
        
        logger.info(f"组合订单发送完成: 成功 {success_count}/{len(data)}")
        return success_count
    
    def send_trades(self, data: List[Dict[Any, Any]], delay_ms: int = 50) -> int:
        """
        发送交易数据
        
        Args:
            data: 交易数据列表
            delay_ms: 发送间隔（毫秒）
            
        Returns:
            发送成功的消息数量
        """
        topic = self.topics['trade']
        success_count = 0
        
        logger.info(f"开始发送交易数据到Topic: {topic}")
        
        for i, record in enumerate(data):
            try:
                # 使用交易号作为key
                key = record.get('trd_nbr', str(i))
                
                # 添加事件时间戳
                if 'event_timestamp' not in record:
                    record['event_timestamp'] = int(time.time() * 1000)
                
                # 发送消息
                future = self.producer.send(topic, key=key, value=record)
                future.get(timeout=10)
                
                success_count += 1
                
                if (i + 1) % 100 == 0:
                    logger.info(f"已发送交易数据: {i + 1}/{len(data)}")
                
                # 控制发送速率（交易数据发送更频繁）
                if delay_ms > 0:
                    time.sleep(delay_ms / 1000.0)
                    
            except KafkaError as e:
                logger.error(f"发送交易数据失败 {i}: {e}")
            except Exception as e:
                logger.error(f"发送交易数据异常 {i}: {e}")
        
        logger.info(f"交易数据发送完成: 成功 {success_count}/{len(data)}")
        return success_count
    
    def send_all_data(self, data_dir: str = "./data", delay_ms: int = 100, max_records: int = None):
        """
        发送所有数据
        
        Args:
            data_dir: 数据目录
            delay_ms: 发送间隔（毫秒）
            max_records: 每种数据类型的最大记录数（用于测试）
        """
        logger.info("======== 开始发送期货系统测试数据 ========")
        
        # 数据文件映射
        data_files = {
            'single_leg_orders.json': self.send_single_leg_orders,
            'combination_orders.json': self.send_combination_orders,
            'trades.json': self.send_trades
        }
        
        total_sent = 0
        
        for filename, sender_func in data_files.items():
            file_path = os.path.join(data_dir, filename)
            
            if not os.path.exists(file_path):
                logger.warning(f"数据文件不存在: {file_path}")
                continue
            
            # 加载数据
            data = self.load_json_data(file_path)
            if not data:
                continue
            
            # 限制记录数（用于测试）
            if max_records and len(data) > max_records:
                data = data[:max_records]
                logger.info(f"限制发送记录数为: {max_records}")
            
            # 发送数据
            sent_count = sender_func(data, delay_ms)
            total_sent += sent_count
            
            logger.info(f"完成发送 {filename}: {sent_count} 条记录")
            
            # 短暂休息，避免过载
            time.sleep(1)
        
        logger.info(f"======== 数据发送完成，总计: {total_sent} 条记录 ========")
    
    def send_simulation_data(self, duration_minutes: int = 5, records_per_minute: int = 60):
        """
        发送模拟实时数据
        
        Args:
            duration_minutes: 发送持续时间（分钟）
            records_per_minute: 每分钟发送的记录数
        """
        logger.info(f"开始模拟实时数据发送，持续 {duration_minutes} 分钟，每分钟 {records_per_minute} 条记录")
        
        # 加载基础数据作为模板
        single_leg_data = self.load_json_data("./data/single_leg_orders.json")
        trade_data = self.load_json_data("./data/trades.json")
        
        if not single_leg_data or not trade_data:
            logger.error("无法加载基础数据，模拟发送失败")
            return
        
        end_time = time.time() + duration_minutes * 60
        interval = 60.0 / records_per_minute  # 发送间隔
        
        sent_count = 0
        
        while time.time() < end_time:
            try:
                # 随机选择一条记录作为模板
                import random
                
                if random.random() < 0.7:  # 70%概率发送单腿订单
                    template = random.choice(single_leg_data).copy()
                    template['ord_nbr'] = f"SIM_{int(time.time())}_{sent_count}"
                    template['event_timestamp'] = int(time.time() * 1000)
                    
                    future = self.producer.send(self.topics['single_leg'], 
                                              key=template['ord_nbr'], 
                                              value=template)
                    future.get(timeout=5)
                    
                else:  # 30%概率发送交易数据
                    template = random.choice(trade_data).copy()
                    template['trd_nbr'] = f"SIM_TRD_{int(time.time())}_{sent_count}"
                    template['event_timestamp'] = int(time.time() * 1000)
                    
                    future = self.producer.send(self.topics['trade'], 
                                              key=template['trd_nbr'], 
                                              value=template)
                    future.get(timeout=5)
                
                sent_count += 1
                
                if sent_count % 100 == 0:
                    logger.info(f"模拟数据已发送: {sent_count} 条")
                
                time.sleep(interval)
                
            except Exception as e:
                logger.error(f"模拟发送失败: {e}")
                time.sleep(1)
        
        logger.info(f"模拟发送完成，总计: {sent_count} 条记录")
    
    def close(self):
        """关闭生产者"""
        if self.producer:
            self.producer.flush()
            self.producer.close()
            logger.info("Kafka生产者已关闭")


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='期货系统Kafka数据生产者')
    parser.add_argument('--kafka-server', default='localhost:9092',
                        help='Kafka服务器地址 (默认: localhost:9092, 服务器: ************:8087)')
    parser.add_argument('--data-dir', default='./data',
                        help='数据文件目录 (默认: ./data)')
    parser.add_argument('--delay-ms', type=int, default=100,
                        help='消息发送间隔毫秒数 (默认: 100)')
    parser.add_argument('--max-records', type=int,
                        help='每种数据类型的最大发送记录数 (用于测试)')
    parser.add_argument('--mode', choices=['batch', 'simulation'], default='batch',
                        help='发送模式: batch(批量发送) 或 simulation(模拟实时) (默认: batch)')
    parser.add_argument('--simulation-duration', type=int, default=5,
                        help='模拟模式持续时间(分钟) (默认: 5)')
    parser.add_argument('--simulation-rate', type=int, default=60,
                        help='模拟模式每分钟发送记录数 (默认: 60)')
    parser.add_argument('--clear-topics', action='store_true',
                        help='发送数据前清理所有Kafka主题（避免读取旧数据）')
    parser.add_argument('--topics', nargs='*', 
                        choices=['single_leg', 'combination', 'trade'],
                        help='指定要发送的数据类型 (默认: 全部)')
    
    args = parser.parse_args()
    
    producer = None
    try:
        # 创建生产者
        producer = FuturesKafkaProducer(args.kafka_server)
        
        # 如果指定了清理主题选项
        if args.clear_topics:
            print("🧹 清理Kafka主题中...")
            if args.topics:
                # 只清理指定的主题
                topics_to_clear = [producer.topics[topic] for topic in args.topics if topic in producer.topics]
                producer.clear_kafka_topics(topics_to_clear)
            else:
                # 清理所有主题
                producer.clear_kafka_topics()
            print("✅ 主题清理完成")
        
        if args.mode == 'batch':
            # 批量发送模式
            producer.send_all_data(
                data_dir=args.data_dir,
                delay_ms=args.delay_ms,
                max_records=args.max_records
            )
        else:
            # 模拟实时发送模式
            producer.send_simulation_data(
                duration_minutes=args.simulation_duration,
                records_per_minute=args.simulation_rate
            )
    
    except KeyboardInterrupt:
        logger.info("用户中断发送")
    except Exception as e:
        logger.error(f"发送过程中出现错误: {e}")
    finally:
        if producer:
            producer.close()


if __name__ == "__main__":
    main()
