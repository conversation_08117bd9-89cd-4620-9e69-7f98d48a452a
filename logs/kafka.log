2025-08-20 14:33:38.137 [main] WARN  org.apache.flink.connector.kafka.source.KafkaSourceBuilder - Property auto.offset.reset is provided but will be overridden from earliest to earliest
2025-08-20 14:33:38.154 [main] WARN  org.apache.flink.connector.kafka.source.KafkaSourceBuilder - Property auto.offset.reset is provided but will be overridden from earliest to earliest
2025-08-20 14:33:38.155 [main] WARN  org.apache.flink.connector.kafka.source.KafkaSourceBuilder - Property auto.offset.reset is provided but will be overridden from earliest to earliest
2025-08-20 14:33:39.106 [SourceCoordinator-Source: Kafka-交易] WARN  org.apache.kafka.clients.admin.AdminClientConfig - These configurations '[key.deserializer, value.deserializer, max.poll.records, client.id.prefix, group.id, partition.discovery.interval.ms, heartbeat.interval.ms, enable.auto.commit, fetch.max.wait.ms, fetch.min.bytes, session.timeout.ms, auto.offset.reset]' were supplied but are not used yet.
2025-08-20 14:33:39.106 [SourceCoordinator-Source: Kafka-组合订单] WARN  org.apache.kafka.clients.admin.AdminClientConfig - These configurations '[key.deserializer, value.deserializer, max.poll.records, client.id.prefix, group.id, partition.discovery.interval.ms, heartbeat.interval.ms, enable.auto.commit, fetch.max.wait.ms, fetch.min.bytes, session.timeout.ms, auto.offset.reset]' were supplied but are not used yet.
2025-08-20 14:33:39.107 [SourceCoordinator-Source: Kafka-单腿订单] WARN  org.apache.kafka.clients.admin.AdminClientConfig - These configurations '[key.deserializer, value.deserializer, max.poll.records, client.id.prefix, group.id, partition.discovery.interval.ms, heartbeat.interval.ms, enable.auto.commit, fetch.max.wait.ms, fetch.min.bytes, session.timeout.ms, auto.offset.reset]' were supplied but are not used yet.
2025-08-20 14:33:39.108 [SourceCoordinator-Source: Kafka-组合订单] INFO  org.apache.flink.connector.kafka.source.enumerator.KafkaSourceEnumerator - Starting the KafkaSourceEnumerator for consumer group orderbook-local-test-group without periodic partition discovery.
2025-08-20 14:33:39.108 [SourceCoordinator-Source: Kafka-交易] WARN  org.apache.kafka.common.utils.AppInfoParser - Error registering AppInfo mbean
javax.management.InstanceAlreadyExistsException: kafka.admin.client:type=app-info,id=orderbook-local-test-group-enumerator-admin-client
	at com.sun.jmx.mbeanserver.Repository.addMBean(Repository.java:436) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerWithRepository(DefaultMBeanServerInterceptor.java:1865) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerDynamicMBean(DefaultMBeanServerInterceptor.java:960) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerObject(DefaultMBeanServerInterceptor.java:895) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerMBean(DefaultMBeanServerInterceptor.java:320) ~[?:?]
	at com.sun.jmx.mbeanserver.JmxMBeanServer.registerMBean(JmxMBeanServer.java:523) ~[?:?]
	at org.apache.kafka.common.utils.AppInfoParser.registerAppInfo(AppInfoParser.java:64) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.admin.KafkaAdminClient.<init>(KafkaAdminClient.java:602) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.admin.KafkaAdminClient.createInternal(KafkaAdminClient.java:544) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.admin.KafkaAdminClient.createInternal(KafkaAdminClient.java:488) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.admin.Admin.create(Admin.java:134) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.admin.AdminClient.create(AdminClient.java:39) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.flink.connector.kafka.source.enumerator.KafkaSourceEnumerator.getKafkaAdminClient(KafkaSourceEnumerator.java:441) ~[flink-connector-kafka-3.4.0-1.20.jar:3.4.0-1.20]
	at org.apache.flink.connector.kafka.source.enumerator.KafkaSourceEnumerator.start(KafkaSourceEnumerator.java:164) ~[flink-connector-kafka-3.4.0-1.20.jar:3.4.0-1.20]
	at org.apache.flink.runtime.source.coordinator.SourceCoordinator.lambda$start$1(SourceCoordinator.java:244) ~[flink-runtime-1.20.0.jar:1.20.0]
	at org.apache.flink.runtime.source.coordinator.SourceCoordinator.lambda$runInEventLoop$10(SourceCoordinator.java:533) ~[flink-runtime-1.20.0.jar:1.20.0]
	at org.apache.flink.util.ThrowableCatchingRunnable.run(ThrowableCatchingRunnable.java:40) [flink-core-1.20.0.jar:1.20.0]
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:539) [?:?]
	at java.util.concurrent.FutureTask.run(FutureTask.java:264) [?:?]
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:304) [?:?]
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136) [?:?]
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635) [?:?]
	at java.lang.Thread.run(Thread.java:840) [?:?]
2025-08-20 14:33:39.110 [SourceCoordinator-Source: Kafka-交易] INFO  org.apache.flink.connector.kafka.source.enumerator.KafkaSourceEnumerator - Starting the KafkaSourceEnumerator for consumer group orderbook-local-test-group without periodic partition discovery.
2025-08-20 14:33:39.110 [SourceCoordinator-Source: Kafka-单腿订单] WARN  org.apache.kafka.common.utils.AppInfoParser - Error registering AppInfo mbean
javax.management.InstanceAlreadyExistsException: kafka.admin.client:type=app-info,id=orderbook-local-test-group-enumerator-admin-client
	at com.sun.jmx.mbeanserver.Repository.addMBean(Repository.java:436) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerWithRepository(DefaultMBeanServerInterceptor.java:1865) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerDynamicMBean(DefaultMBeanServerInterceptor.java:960) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerObject(DefaultMBeanServerInterceptor.java:895) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerMBean(DefaultMBeanServerInterceptor.java:320) ~[?:?]
	at com.sun.jmx.mbeanserver.JmxMBeanServer.registerMBean(JmxMBeanServer.java:523) ~[?:?]
	at org.apache.kafka.common.utils.AppInfoParser.registerAppInfo(AppInfoParser.java:64) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.admin.KafkaAdminClient.<init>(KafkaAdminClient.java:602) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.admin.KafkaAdminClient.createInternal(KafkaAdminClient.java:544) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.admin.KafkaAdminClient.createInternal(KafkaAdminClient.java:488) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.admin.Admin.create(Admin.java:134) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.admin.AdminClient.create(AdminClient.java:39) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.flink.connector.kafka.source.enumerator.KafkaSourceEnumerator.getKafkaAdminClient(KafkaSourceEnumerator.java:441) ~[flink-connector-kafka-3.4.0-1.20.jar:3.4.0-1.20]
	at org.apache.flink.connector.kafka.source.enumerator.KafkaSourceEnumerator.start(KafkaSourceEnumerator.java:164) ~[flink-connector-kafka-3.4.0-1.20.jar:3.4.0-1.20]
	at org.apache.flink.runtime.source.coordinator.SourceCoordinator.lambda$start$1(SourceCoordinator.java:244) ~[flink-runtime-1.20.0.jar:1.20.0]
	at org.apache.flink.runtime.source.coordinator.SourceCoordinator.lambda$runInEventLoop$10(SourceCoordinator.java:533) ~[flink-runtime-1.20.0.jar:1.20.0]
	at org.apache.flink.util.ThrowableCatchingRunnable.run(ThrowableCatchingRunnable.java:40) [flink-core-1.20.0.jar:1.20.0]
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:539) [?:?]
	at java.util.concurrent.FutureTask.run(FutureTask.java:264) [?:?]
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:304) [?:?]
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136) [?:?]
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635) [?:?]
	at java.lang.Thread.run(Thread.java:840) [?:?]
2025-08-20 14:33:39.111 [SourceCoordinator-Source: Kafka-单腿订单] INFO  org.apache.flink.connector.kafka.source.enumerator.KafkaSourceEnumerator - Starting the KafkaSourceEnumerator for consumer group orderbook-local-test-group without periodic partition discovery.
2025-08-20 14:33:39.235 [SourceCoordinator-Source: Kafka-交易] INFO  org.apache.flink.connector.kafka.source.enumerator.KafkaSourceEnumerator - Discovered new partitions: [trade_data_event-0]
2025-08-20 14:33:39.235 [SourceCoordinator-Source: Kafka-组合订单] INFO  org.apache.flink.connector.kafka.source.enumerator.KafkaSourceEnumerator - Discovered new partitions: [cmb_order_data_event-0]
2025-08-20 14:33:39.235 [SourceCoordinator-Source: Kafka-单腿订单] INFO  org.apache.flink.connector.kafka.source.enumerator.KafkaSourceEnumerator - Discovered new partitions: [singleleg_order_data_event-0]
2025-08-20 14:33:39.237 [SourceCoordinator-Source: Kafka-组合订单] INFO  org.apache.flink.connector.kafka.source.enumerator.KafkaSourceEnumerator - Assigning splits to readers {0=[[Partition: cmb_order_data_event-0, StartingOffset: -2, StoppingOffset: -9223372036854775808]]}
2025-08-20 14:33:39.238 [SourceCoordinator-Source: Kafka-交易] INFO  org.apache.flink.connector.kafka.source.enumerator.KafkaSourceEnumerator - Assigning splits to readers {0=[[Partition: trade_data_event-0, StartingOffset: -2, StoppingOffset: -9223372036854775808]]}
2025-08-20 14:33:39.238 [SourceCoordinator-Source: Kafka-单腿订单] INFO  org.apache.flink.connector.kafka.source.enumerator.KafkaSourceEnumerator - Assigning splits to readers {0=[[Partition: singleleg_order_data_event-0, StartingOffset: -2, StoppingOffset: -9223372036854775808]]}
2025-08-20 14:33:39.266 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] WARN  org.apache.kafka.clients.consumer.ConsumerConfig - These configurations '[client.id.prefix, partition.discovery.interval.ms]' were supplied but are not used yet.
2025-08-20 14:33:39.276 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] WARN  org.apache.kafka.clients.consumer.ConsumerConfig - These configurations '[client.id.prefix, partition.discovery.interval.ms]' were supplied but are not used yet.
2025-08-20 14:33:39.276 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] WARN  org.apache.kafka.clients.consumer.ConsumerConfig - These configurations '[client.id.prefix, partition.discovery.interval.ms]' were supplied but are not used yet.
2025-08-20 14:33:39.276 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] WARN  org.apache.kafka.common.utils.AppInfoParser - Error registering AppInfo mbean
javax.management.InstanceAlreadyExistsException: kafka.consumer:type=app-info,id=orderbook-local-test-group-0
	at com.sun.jmx.mbeanserver.Repository.addMBean(Repository.java:436) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerWithRepository(DefaultMBeanServerInterceptor.java:1865) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerDynamicMBean(DefaultMBeanServerInterceptor.java:960) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerObject(DefaultMBeanServerInterceptor.java:895) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerMBean(DefaultMBeanServerInterceptor.java:320) ~[?:?]
	at com.sun.jmx.mbeanserver.JmxMBeanServer.registerMBean(JmxMBeanServer.java:523) ~[?:?]
	at org.apache.kafka.common.utils.AppInfoParser.registerAppInfo(AppInfoParser.java:64) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.consumer.KafkaConsumer.<init>(KafkaConsumer.java:821) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.consumer.KafkaConsumer.<init>(KafkaConsumer.java:665) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.consumer.KafkaConsumer.<init>(KafkaConsumer.java:646) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.consumer.KafkaConsumer.<init>(KafkaConsumer.java:626) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.flink.connector.kafka.source.reader.KafkaPartitionSplitReader.<init>(KafkaPartitionSplitReader.java:97) ~[flink-connector-kafka-3.4.0-1.20.jar:3.4.0-1.20]
	at org.apache.flink.connector.kafka.source.KafkaSource.lambda$createReader$1(KafkaSource.java:185) ~[flink-connector-kafka-3.4.0-1.20.jar:3.4.0-1.20]
	at org.apache.flink.connector.base.source.reader.fetcher.SplitFetcherManager.createSplitFetcher(SplitFetcherManager.java:259) ~[flink-connector-base-1.20.0.jar:1.20.0]
	at org.apache.flink.connector.base.source.reader.fetcher.SingleThreadFetcherManager.addSplits(SingleThreadFetcherManager.java:148) ~[flink-connector-base-1.20.0.jar:1.20.0]
	at org.apache.flink.connector.base.source.reader.SourceReaderBase.addSplits(SourceReaderBase.java:315) ~[flink-connector-base-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.api.operators.SourceOperator.handleAddSplitsEvent(SourceOperator.java:606) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.api.operators.SourceOperator.handleOperatorEvent(SourceOperator.java:575) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.OperatorEventDispatcherImpl.dispatchEventToHandlers(OperatorEventDispatcherImpl.java:72) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.RegularOperatorChain.dispatchOperatorEvent(RegularOperatorChain.java:80) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.StreamTask.lambda$dispatchOperatorEvent$24(StreamTask.java:1609) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.StreamTaskActionExecutor$1.runThrowing(StreamTaskActionExecutor.java:50) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.mailbox.Mail.run(Mail.java:101) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.mailbox.MailboxProcessor.runMail(MailboxProcessor.java:414) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.mailbox.MailboxProcessor.processMailsWhenDefaultActionUnavailable(MailboxProcessor.java:383) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.mailbox.MailboxProcessor.processMail(MailboxProcessor.java:368) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.mailbox.MailboxProcessor.runMailboxLoop(MailboxProcessor.java:229) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.StreamTask.runMailboxLoop(StreamTask.java:973) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.StreamTask.invoke(StreamTask.java:917) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.runtime.taskmanager.Task.runWithSystemExitMonitoring(Task.java:970) [flink-runtime-1.20.0.jar:1.20.0]
	at org.apache.flink.runtime.taskmanager.Task.restoreAndInvoke(Task.java:949) [flink-runtime-1.20.0.jar:1.20.0]
	at org.apache.flink.runtime.taskmanager.Task.doRun(Task.java:763) [flink-runtime-1.20.0.jar:1.20.0]
	at org.apache.flink.runtime.taskmanager.Task.run(Task.java:575) [flink-runtime-1.20.0.jar:1.20.0]
	at java.lang.Thread.run(Thread.java:840) [?:?]
2025-08-20 14:33:39.277 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] WARN  org.apache.kafka.common.utils.AppInfoParser - Error registering AppInfo mbean
javax.management.InstanceAlreadyExistsException: kafka.consumer:type=app-info,id=orderbook-local-test-group-0
	at com.sun.jmx.mbeanserver.Repository.addMBean(Repository.java:436) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerWithRepository(DefaultMBeanServerInterceptor.java:1865) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerDynamicMBean(DefaultMBeanServerInterceptor.java:960) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerObject(DefaultMBeanServerInterceptor.java:895) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerMBean(DefaultMBeanServerInterceptor.java:320) ~[?:?]
	at com.sun.jmx.mbeanserver.JmxMBeanServer.registerMBean(JmxMBeanServer.java:523) ~[?:?]
	at org.apache.kafka.common.utils.AppInfoParser.registerAppInfo(AppInfoParser.java:64) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.consumer.KafkaConsumer.<init>(KafkaConsumer.java:821) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.consumer.KafkaConsumer.<init>(KafkaConsumer.java:665) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.consumer.KafkaConsumer.<init>(KafkaConsumer.java:646) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.consumer.KafkaConsumer.<init>(KafkaConsumer.java:626) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.flink.connector.kafka.source.reader.KafkaPartitionSplitReader.<init>(KafkaPartitionSplitReader.java:97) ~[flink-connector-kafka-3.4.0-1.20.jar:3.4.0-1.20]
	at org.apache.flink.connector.kafka.source.KafkaSource.lambda$createReader$1(KafkaSource.java:185) ~[flink-connector-kafka-3.4.0-1.20.jar:3.4.0-1.20]
	at org.apache.flink.connector.base.source.reader.fetcher.SplitFetcherManager.createSplitFetcher(SplitFetcherManager.java:259) ~[flink-connector-base-1.20.0.jar:1.20.0]
	at org.apache.flink.connector.base.source.reader.fetcher.SingleThreadFetcherManager.addSplits(SingleThreadFetcherManager.java:148) ~[flink-connector-base-1.20.0.jar:1.20.0]
	at org.apache.flink.connector.base.source.reader.SourceReaderBase.addSplits(SourceReaderBase.java:315) ~[flink-connector-base-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.api.operators.SourceOperator.handleAddSplitsEvent(SourceOperator.java:606) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.api.operators.SourceOperator.handleOperatorEvent(SourceOperator.java:575) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.OperatorEventDispatcherImpl.dispatchEventToHandlers(OperatorEventDispatcherImpl.java:72) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.RegularOperatorChain.dispatchOperatorEvent(RegularOperatorChain.java:80) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.StreamTask.lambda$dispatchOperatorEvent$24(StreamTask.java:1609) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.StreamTaskActionExecutor$1.runThrowing(StreamTaskActionExecutor.java:50) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.mailbox.Mail.run(Mail.java:101) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.mailbox.MailboxProcessor.runMail(MailboxProcessor.java:414) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.mailbox.MailboxProcessor.processMailsWhenDefaultActionUnavailable(MailboxProcessor.java:383) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.mailbox.MailboxProcessor.processMail(MailboxProcessor.java:368) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.mailbox.MailboxProcessor.runMailboxLoop(MailboxProcessor.java:229) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.StreamTask.runMailboxLoop(StreamTask.java:973) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.StreamTask.invoke(StreamTask.java:917) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.runtime.taskmanager.Task.runWithSystemExitMonitoring(Task.java:970) [flink-runtime-1.20.0.jar:1.20.0]
	at org.apache.flink.runtime.taskmanager.Task.restoreAndInvoke(Task.java:949) [flink-runtime-1.20.0.jar:1.20.0]
	at org.apache.flink.runtime.taskmanager.Task.doRun(Task.java:763) [flink-runtime-1.20.0.jar:1.20.0]
	at org.apache.flink.runtime.taskmanager.Task.run(Task.java:575) [flink-runtime-1.20.0.jar:1.20.0]
	at java.lang.Thread.run(Thread.java:840) [?:?]
2025-08-20 14:36:43.567 [main] WARN  org.apache.flink.connector.kafka.source.KafkaSourceBuilder - Property auto.offset.reset is provided but will be overridden from earliest to earliest
2025-08-20 14:36:43.585 [main] WARN  org.apache.flink.connector.kafka.source.KafkaSourceBuilder - Property auto.offset.reset is provided but will be overridden from earliest to earliest
2025-08-20 14:36:43.585 [main] WARN  org.apache.flink.connector.kafka.source.KafkaSourceBuilder - Property auto.offset.reset is provided but will be overridden from earliest to earliest
2025-08-20 14:36:44.332 [SourceCoordinator-Source: Kafka-单腿订单] WARN  org.apache.kafka.clients.admin.AdminClientConfig - These configurations '[key.deserializer, value.deserializer, max.poll.records, client.id.prefix, group.id, partition.discovery.interval.ms, heartbeat.interval.ms, enable.auto.commit, fetch.max.wait.ms, fetch.min.bytes, session.timeout.ms, auto.offset.reset]' were supplied but are not used yet.
2025-08-20 14:36:44.332 [SourceCoordinator-Source: Kafka-交易] WARN  org.apache.kafka.clients.admin.AdminClientConfig - These configurations '[key.deserializer, value.deserializer, max.poll.records, client.id.prefix, group.id, partition.discovery.interval.ms, heartbeat.interval.ms, enable.auto.commit, fetch.max.wait.ms, fetch.min.bytes, session.timeout.ms, auto.offset.reset]' were supplied but are not used yet.
2025-08-20 14:36:44.332 [SourceCoordinator-Source: Kafka-组合订单] WARN  org.apache.kafka.clients.admin.AdminClientConfig - These configurations '[key.deserializer, value.deserializer, max.poll.records, client.id.prefix, group.id, partition.discovery.interval.ms, heartbeat.interval.ms, enable.auto.commit, fetch.max.wait.ms, fetch.min.bytes, session.timeout.ms, auto.offset.reset]' were supplied but are not used yet.
2025-08-20 14:36:44.334 [SourceCoordinator-Source: Kafka-单腿订单] INFO  org.apache.flink.connector.kafka.source.enumerator.KafkaSourceEnumerator - Starting the KafkaSourceEnumerator for consumer group orderbook-local-test-group without periodic partition discovery.
2025-08-20 14:36:44.334 [SourceCoordinator-Source: Kafka-交易] WARN  org.apache.kafka.common.utils.AppInfoParser - Error registering AppInfo mbean
javax.management.InstanceAlreadyExistsException: kafka.admin.client:type=app-info,id=orderbook-local-test-group-enumerator-admin-client
	at com.sun.jmx.mbeanserver.Repository.addMBean(Repository.java:436) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerWithRepository(DefaultMBeanServerInterceptor.java:1865) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerDynamicMBean(DefaultMBeanServerInterceptor.java:960) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerObject(DefaultMBeanServerInterceptor.java:895) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerMBean(DefaultMBeanServerInterceptor.java:320) ~[?:?]
	at com.sun.jmx.mbeanserver.JmxMBeanServer.registerMBean(JmxMBeanServer.java:523) ~[?:?]
	at org.apache.kafka.common.utils.AppInfoParser.registerAppInfo(AppInfoParser.java:64) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.admin.KafkaAdminClient.<init>(KafkaAdminClient.java:602) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.admin.KafkaAdminClient.createInternal(KafkaAdminClient.java:544) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.admin.KafkaAdminClient.createInternal(KafkaAdminClient.java:488) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.admin.Admin.create(Admin.java:134) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.admin.AdminClient.create(AdminClient.java:39) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.flink.connector.kafka.source.enumerator.KafkaSourceEnumerator.getKafkaAdminClient(KafkaSourceEnumerator.java:441) ~[flink-connector-kafka-3.4.0-1.20.jar:3.4.0-1.20]
	at org.apache.flink.connector.kafka.source.enumerator.KafkaSourceEnumerator.start(KafkaSourceEnumerator.java:164) ~[flink-connector-kafka-3.4.0-1.20.jar:3.4.0-1.20]
	at org.apache.flink.runtime.source.coordinator.SourceCoordinator.lambda$start$1(SourceCoordinator.java:244) ~[flink-runtime-1.20.0.jar:1.20.0]
	at org.apache.flink.runtime.source.coordinator.SourceCoordinator.lambda$runInEventLoop$10(SourceCoordinator.java:533) ~[flink-runtime-1.20.0.jar:1.20.0]
	at org.apache.flink.util.ThrowableCatchingRunnable.run(ThrowableCatchingRunnable.java:40) [flink-core-1.20.0.jar:1.20.0]
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:539) [?:?]
	at java.util.concurrent.FutureTask.run(FutureTask.java:264) [?:?]
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:304) [?:?]
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136) [?:?]
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635) [?:?]
	at java.lang.Thread.run(Thread.java:840) [?:?]
2025-08-20 14:36:44.337 [SourceCoordinator-Source: Kafka-组合订单] WARN  org.apache.kafka.common.utils.AppInfoParser - Error registering AppInfo mbean
javax.management.InstanceAlreadyExistsException: kafka.admin.client:type=app-info,id=orderbook-local-test-group-enumerator-admin-client
	at com.sun.jmx.mbeanserver.Repository.addMBean(Repository.java:436) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerWithRepository(DefaultMBeanServerInterceptor.java:1865) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerDynamicMBean(DefaultMBeanServerInterceptor.java:960) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerObject(DefaultMBeanServerInterceptor.java:895) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerMBean(DefaultMBeanServerInterceptor.java:320) ~[?:?]
	at com.sun.jmx.mbeanserver.JmxMBeanServer.registerMBean(JmxMBeanServer.java:523) ~[?:?]
	at org.apache.kafka.common.utils.AppInfoParser.registerAppInfo(AppInfoParser.java:64) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.admin.KafkaAdminClient.<init>(KafkaAdminClient.java:602) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.admin.KafkaAdminClient.createInternal(KafkaAdminClient.java:544) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.admin.KafkaAdminClient.createInternal(KafkaAdminClient.java:488) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.admin.Admin.create(Admin.java:134) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.admin.AdminClient.create(AdminClient.java:39) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.flink.connector.kafka.source.enumerator.KafkaSourceEnumerator.getKafkaAdminClient(KafkaSourceEnumerator.java:441) ~[flink-connector-kafka-3.4.0-1.20.jar:3.4.0-1.20]
	at org.apache.flink.connector.kafka.source.enumerator.KafkaSourceEnumerator.start(KafkaSourceEnumerator.java:164) ~[flink-connector-kafka-3.4.0-1.20.jar:3.4.0-1.20]
	at org.apache.flink.runtime.source.coordinator.SourceCoordinator.lambda$start$1(SourceCoordinator.java:244) ~[flink-runtime-1.20.0.jar:1.20.0]
	at org.apache.flink.runtime.source.coordinator.SourceCoordinator.lambda$runInEventLoop$10(SourceCoordinator.java:533) ~[flink-runtime-1.20.0.jar:1.20.0]
	at org.apache.flink.util.ThrowableCatchingRunnable.run(ThrowableCatchingRunnable.java:40) [flink-core-1.20.0.jar:1.20.0]
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:539) [?:?]
	at java.util.concurrent.FutureTask.run(FutureTask.java:264) [?:?]
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:304) [?:?]
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136) [?:?]
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635) [?:?]
	at java.lang.Thread.run(Thread.java:840) [?:?]
2025-08-20 14:36:44.337 [SourceCoordinator-Source: Kafka-交易] INFO  org.apache.flink.connector.kafka.source.enumerator.KafkaSourceEnumerator - Starting the KafkaSourceEnumerator for consumer group orderbook-local-test-group without periodic partition discovery.
2025-08-20 14:36:44.337 [SourceCoordinator-Source: Kafka-组合订单] INFO  org.apache.flink.connector.kafka.source.enumerator.KafkaSourceEnumerator - Starting the KafkaSourceEnumerator for consumer group orderbook-local-test-group without periodic partition discovery.
2025-08-20 14:36:44.444 [SourceCoordinator-Source: Kafka-组合订单] INFO  org.apache.flink.connector.kafka.source.enumerator.KafkaSourceEnumerator - Discovered new partitions: [cmb_order_data_event-0]
2025-08-20 14:36:44.444 [SourceCoordinator-Source: Kafka-交易] INFO  org.apache.flink.connector.kafka.source.enumerator.KafkaSourceEnumerator - Discovered new partitions: [trade_data_event-0]
2025-08-20 14:36:44.444 [SourceCoordinator-Source: Kafka-单腿订单] INFO  org.apache.flink.connector.kafka.source.enumerator.KafkaSourceEnumerator - Discovered new partitions: [singleleg_order_data_event-0]
2025-08-20 14:36:44.463 [SourceCoordinator-Source: Kafka-组合订单] INFO  org.apache.flink.connector.kafka.source.enumerator.KafkaSourceEnumerator - Assigning splits to readers {0=[[Partition: cmb_order_data_event-0, StartingOffset: -2, StoppingOffset: -9223372036854775808]]}
2025-08-20 14:36:44.464 [SourceCoordinator-Source: Kafka-单腿订单] INFO  org.apache.flink.connector.kafka.source.enumerator.KafkaSourceEnumerator - Assigning splits to readers {0=[[Partition: singleleg_order_data_event-0, StartingOffset: -2, StoppingOffset: -9223372036854775808]]}
2025-08-20 14:36:44.464 [SourceCoordinator-Source: Kafka-交易] INFO  org.apache.flink.connector.kafka.source.enumerator.KafkaSourceEnumerator - Assigning splits to readers {0=[[Partition: trade_data_event-0, StartingOffset: -2, StoppingOffset: -9223372036854775808]]}
2025-08-20 14:36:44.497 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] WARN  org.apache.kafka.clients.consumer.ConsumerConfig - These configurations '[client.id.prefix, partition.discovery.interval.ms]' were supplied but are not used yet.
2025-08-20 14:36:44.497 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] WARN  org.apache.kafka.clients.consumer.ConsumerConfig - These configurations '[client.id.prefix, partition.discovery.interval.ms]' were supplied but are not used yet.
2025-08-20 14:36:44.497 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] WARN  org.apache.kafka.clients.consumer.ConsumerConfig - These configurations '[client.id.prefix, partition.discovery.interval.ms]' were supplied but are not used yet.
2025-08-20 14:36:44.497 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] WARN  org.apache.kafka.common.utils.AppInfoParser - Error registering AppInfo mbean
javax.management.InstanceAlreadyExistsException: kafka.consumer:type=app-info,id=orderbook-local-test-group-0
	at com.sun.jmx.mbeanserver.Repository.addMBean(Repository.java:436) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerWithRepository(DefaultMBeanServerInterceptor.java:1865) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerDynamicMBean(DefaultMBeanServerInterceptor.java:960) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerObject(DefaultMBeanServerInterceptor.java:895) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerMBean(DefaultMBeanServerInterceptor.java:320) ~[?:?]
	at com.sun.jmx.mbeanserver.JmxMBeanServer.registerMBean(JmxMBeanServer.java:523) ~[?:?]
	at org.apache.kafka.common.utils.AppInfoParser.registerAppInfo(AppInfoParser.java:64) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.consumer.KafkaConsumer.<init>(KafkaConsumer.java:821) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.consumer.KafkaConsumer.<init>(KafkaConsumer.java:665) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.consumer.KafkaConsumer.<init>(KafkaConsumer.java:646) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.consumer.KafkaConsumer.<init>(KafkaConsumer.java:626) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.flink.connector.kafka.source.reader.KafkaPartitionSplitReader.<init>(KafkaPartitionSplitReader.java:97) ~[flink-connector-kafka-3.4.0-1.20.jar:3.4.0-1.20]
	at org.apache.flink.connector.kafka.source.KafkaSource.lambda$createReader$1(KafkaSource.java:185) ~[flink-connector-kafka-3.4.0-1.20.jar:3.4.0-1.20]
	at org.apache.flink.connector.base.source.reader.fetcher.SplitFetcherManager.createSplitFetcher(SplitFetcherManager.java:259) ~[flink-connector-base-1.20.0.jar:1.20.0]
	at org.apache.flink.connector.base.source.reader.fetcher.SingleThreadFetcherManager.addSplits(SingleThreadFetcherManager.java:148) ~[flink-connector-base-1.20.0.jar:1.20.0]
	at org.apache.flink.connector.base.source.reader.SourceReaderBase.addSplits(SourceReaderBase.java:315) ~[flink-connector-base-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.api.operators.SourceOperator.handleAddSplitsEvent(SourceOperator.java:606) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.api.operators.SourceOperator.handleOperatorEvent(SourceOperator.java:575) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.OperatorEventDispatcherImpl.dispatchEventToHandlers(OperatorEventDispatcherImpl.java:72) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.RegularOperatorChain.dispatchOperatorEvent(RegularOperatorChain.java:80) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.StreamTask.lambda$dispatchOperatorEvent$24(StreamTask.java:1609) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.StreamTaskActionExecutor$1.runThrowing(StreamTaskActionExecutor.java:50) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.mailbox.Mail.run(Mail.java:101) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.mailbox.MailboxProcessor.runMail(MailboxProcessor.java:414) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.mailbox.MailboxProcessor.processMailsWhenDefaultActionUnavailable(MailboxProcessor.java:383) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.mailbox.MailboxProcessor.processMail(MailboxProcessor.java:368) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.mailbox.MailboxProcessor.runMailboxLoop(MailboxProcessor.java:229) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.StreamTask.runMailboxLoop(StreamTask.java:973) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.StreamTask.invoke(StreamTask.java:917) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.runtime.taskmanager.Task.runWithSystemExitMonitoring(Task.java:970) [flink-runtime-1.20.0.jar:1.20.0]
	at org.apache.flink.runtime.taskmanager.Task.restoreAndInvoke(Task.java:949) [flink-runtime-1.20.0.jar:1.20.0]
	at org.apache.flink.runtime.taskmanager.Task.doRun(Task.java:763) [flink-runtime-1.20.0.jar:1.20.0]
	at org.apache.flink.runtime.taskmanager.Task.run(Task.java:575) [flink-runtime-1.20.0.jar:1.20.0]
	at java.lang.Thread.run(Thread.java:840) [?:?]
2025-08-20 14:36:44.497 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] WARN  org.apache.kafka.common.utils.AppInfoParser - Error registering AppInfo mbean
javax.management.InstanceAlreadyExistsException: kafka.consumer:type=app-info,id=orderbook-local-test-group-0
	at com.sun.jmx.mbeanserver.Repository.addMBean(Repository.java:436) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerWithRepository(DefaultMBeanServerInterceptor.java:1865) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerDynamicMBean(DefaultMBeanServerInterceptor.java:960) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerObject(DefaultMBeanServerInterceptor.java:895) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerMBean(DefaultMBeanServerInterceptor.java:320) ~[?:?]
	at com.sun.jmx.mbeanserver.JmxMBeanServer.registerMBean(JmxMBeanServer.java:523) ~[?:?]
	at org.apache.kafka.common.utils.AppInfoParser.registerAppInfo(AppInfoParser.java:64) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.consumer.KafkaConsumer.<init>(KafkaConsumer.java:821) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.consumer.KafkaConsumer.<init>(KafkaConsumer.java:665) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.consumer.KafkaConsumer.<init>(KafkaConsumer.java:646) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.consumer.KafkaConsumer.<init>(KafkaConsumer.java:626) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.flink.connector.kafka.source.reader.KafkaPartitionSplitReader.<init>(KafkaPartitionSplitReader.java:97) ~[flink-connector-kafka-3.4.0-1.20.jar:3.4.0-1.20]
	at org.apache.flink.connector.kafka.source.KafkaSource.lambda$createReader$1(KafkaSource.java:185) ~[flink-connector-kafka-3.4.0-1.20.jar:3.4.0-1.20]
	at org.apache.flink.connector.base.source.reader.fetcher.SplitFetcherManager.createSplitFetcher(SplitFetcherManager.java:259) ~[flink-connector-base-1.20.0.jar:1.20.0]
	at org.apache.flink.connector.base.source.reader.fetcher.SingleThreadFetcherManager.addSplits(SingleThreadFetcherManager.java:148) ~[flink-connector-base-1.20.0.jar:1.20.0]
	at org.apache.flink.connector.base.source.reader.SourceReaderBase.addSplits(SourceReaderBase.java:315) ~[flink-connector-base-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.api.operators.SourceOperator.handleAddSplitsEvent(SourceOperator.java:606) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.api.operators.SourceOperator.handleOperatorEvent(SourceOperator.java:575) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.OperatorEventDispatcherImpl.dispatchEventToHandlers(OperatorEventDispatcherImpl.java:72) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.RegularOperatorChain.dispatchOperatorEvent(RegularOperatorChain.java:80) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.StreamTask.lambda$dispatchOperatorEvent$24(StreamTask.java:1609) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.StreamTaskActionExecutor$1.runThrowing(StreamTaskActionExecutor.java:50) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.mailbox.Mail.run(Mail.java:101) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.mailbox.MailboxProcessor.runMail(MailboxProcessor.java:414) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.mailbox.MailboxProcessor.processMailsWhenDefaultActionUnavailable(MailboxProcessor.java:383) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.mailbox.MailboxProcessor.processMail(MailboxProcessor.java:368) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.mailbox.MailboxProcessor.runMailboxLoop(MailboxProcessor.java:229) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.StreamTask.runMailboxLoop(StreamTask.java:973) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.StreamTask.invoke(StreamTask.java:917) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.runtime.taskmanager.Task.runWithSystemExitMonitoring(Task.java:970) [flink-runtime-1.20.0.jar:1.20.0]
	at org.apache.flink.runtime.taskmanager.Task.restoreAndInvoke(Task.java:949) [flink-runtime-1.20.0.jar:1.20.0]
	at org.apache.flink.runtime.taskmanager.Task.doRun(Task.java:763) [flink-runtime-1.20.0.jar:1.20.0]
	at org.apache.flink.runtime.taskmanager.Task.run(Task.java:575) [flink-runtime-1.20.0.jar:1.20.0]
	at java.lang.Thread.run(Thread.java:840) [?:?]
2025-08-20 14:39:22.481 [main] WARN  org.apache.flink.connector.kafka.source.KafkaSourceBuilder - Property auto.offset.reset is provided but will be overridden from earliest to earliest
2025-08-20 14:39:22.502 [main] WARN  org.apache.flink.connector.kafka.source.KafkaSourceBuilder - Property auto.offset.reset is provided but will be overridden from earliest to earliest
2025-08-20 14:39:22.503 [main] WARN  org.apache.flink.connector.kafka.source.KafkaSourceBuilder - Property auto.offset.reset is provided but will be overridden from earliest to earliest
2025-08-20 14:39:23.247 [SourceCoordinator-Source: Kafka-交易] WARN  org.apache.kafka.clients.admin.AdminClientConfig - These configurations '[key.deserializer, value.deserializer, max.poll.records, client.id.prefix, group.id, partition.discovery.interval.ms, heartbeat.interval.ms, enable.auto.commit, fetch.max.wait.ms, fetch.min.bytes, session.timeout.ms, auto.offset.reset]' were supplied but are not used yet.
2025-08-20 14:39:23.247 [SourceCoordinator-Source: Kafka-单腿订单] WARN  org.apache.kafka.clients.admin.AdminClientConfig - These configurations '[key.deserializer, value.deserializer, max.poll.records, client.id.prefix, group.id, partition.discovery.interval.ms, heartbeat.interval.ms, enable.auto.commit, fetch.max.wait.ms, fetch.min.bytes, session.timeout.ms, auto.offset.reset]' were supplied but are not used yet.
2025-08-20 14:39:23.247 [SourceCoordinator-Source: Kafka-组合订单] WARN  org.apache.kafka.clients.admin.AdminClientConfig - These configurations '[key.deserializer, value.deserializer, max.poll.records, client.id.prefix, group.id, partition.discovery.interval.ms, heartbeat.interval.ms, enable.auto.commit, fetch.max.wait.ms, fetch.min.bytes, session.timeout.ms, auto.offset.reset]' were supplied but are not used yet.
2025-08-20 14:39:23.250 [SourceCoordinator-Source: Kafka-单腿订单] INFO  org.apache.flink.connector.kafka.source.enumerator.KafkaSourceEnumerator - Starting the KafkaSourceEnumerator for consumer group orderbook-local-test-group without periodic partition discovery.
2025-08-20 14:39:23.250 [SourceCoordinator-Source: Kafka-组合订单] WARN  org.apache.kafka.common.utils.AppInfoParser - Error registering AppInfo mbean
javax.management.InstanceAlreadyExistsException: kafka.admin.client:type=app-info,id=orderbook-local-test-group-enumerator-admin-client
	at com.sun.jmx.mbeanserver.Repository.addMBean(Repository.java:436) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerWithRepository(DefaultMBeanServerInterceptor.java:1865) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerDynamicMBean(DefaultMBeanServerInterceptor.java:960) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerObject(DefaultMBeanServerInterceptor.java:895) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerMBean(DefaultMBeanServerInterceptor.java:320) ~[?:?]
	at com.sun.jmx.mbeanserver.JmxMBeanServer.registerMBean(JmxMBeanServer.java:523) ~[?:?]
	at org.apache.kafka.common.utils.AppInfoParser.registerAppInfo(AppInfoParser.java:64) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.admin.KafkaAdminClient.<init>(KafkaAdminClient.java:602) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.admin.KafkaAdminClient.createInternal(KafkaAdminClient.java:544) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.admin.KafkaAdminClient.createInternal(KafkaAdminClient.java:488) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.admin.Admin.create(Admin.java:134) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.admin.AdminClient.create(AdminClient.java:39) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.flink.connector.kafka.source.enumerator.KafkaSourceEnumerator.getKafkaAdminClient(KafkaSourceEnumerator.java:441) ~[flink-connector-kafka-3.4.0-1.20.jar:3.4.0-1.20]
	at org.apache.flink.connector.kafka.source.enumerator.KafkaSourceEnumerator.start(KafkaSourceEnumerator.java:164) ~[flink-connector-kafka-3.4.0-1.20.jar:3.4.0-1.20]
	at org.apache.flink.runtime.source.coordinator.SourceCoordinator.lambda$start$1(SourceCoordinator.java:244) ~[flink-runtime-1.20.0.jar:1.20.0]
	at org.apache.flink.runtime.source.coordinator.SourceCoordinator.lambda$runInEventLoop$10(SourceCoordinator.java:533) ~[flink-runtime-1.20.0.jar:1.20.0]
	at org.apache.flink.util.ThrowableCatchingRunnable.run(ThrowableCatchingRunnable.java:40) [flink-core-1.20.0.jar:1.20.0]
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:539) [?:?]
	at java.util.concurrent.FutureTask.run(FutureTask.java:264) [?:?]
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:304) [?:?]
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136) [?:?]
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635) [?:?]
	at java.lang.Thread.run(Thread.java:840) [?:?]
2025-08-20 14:39:23.256 [SourceCoordinator-Source: Kafka-组合订单] INFO  org.apache.flink.connector.kafka.source.enumerator.KafkaSourceEnumerator - Starting the KafkaSourceEnumerator for consumer group orderbook-local-test-group without periodic partition discovery.
2025-08-20 14:39:23.256 [SourceCoordinator-Source: Kafka-交易] WARN  org.apache.kafka.common.utils.AppInfoParser - Error registering AppInfo mbean
javax.management.InstanceAlreadyExistsException: kafka.admin.client:type=app-info,id=orderbook-local-test-group-enumerator-admin-client
	at com.sun.jmx.mbeanserver.Repository.addMBean(Repository.java:436) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerWithRepository(DefaultMBeanServerInterceptor.java:1865) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerDynamicMBean(DefaultMBeanServerInterceptor.java:960) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerObject(DefaultMBeanServerInterceptor.java:895) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerMBean(DefaultMBeanServerInterceptor.java:320) ~[?:?]
	at com.sun.jmx.mbeanserver.JmxMBeanServer.registerMBean(JmxMBeanServer.java:523) ~[?:?]
	at org.apache.kafka.common.utils.AppInfoParser.registerAppInfo(AppInfoParser.java:64) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.admin.KafkaAdminClient.<init>(KafkaAdminClient.java:602) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.admin.KafkaAdminClient.createInternal(KafkaAdminClient.java:544) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.admin.KafkaAdminClient.createInternal(KafkaAdminClient.java:488) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.admin.Admin.create(Admin.java:134) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.admin.AdminClient.create(AdminClient.java:39) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.flink.connector.kafka.source.enumerator.KafkaSourceEnumerator.getKafkaAdminClient(KafkaSourceEnumerator.java:441) ~[flink-connector-kafka-3.4.0-1.20.jar:3.4.0-1.20]
	at org.apache.flink.connector.kafka.source.enumerator.KafkaSourceEnumerator.start(KafkaSourceEnumerator.java:164) ~[flink-connector-kafka-3.4.0-1.20.jar:3.4.0-1.20]
	at org.apache.flink.runtime.source.coordinator.SourceCoordinator.lambda$start$1(SourceCoordinator.java:244) ~[flink-runtime-1.20.0.jar:1.20.0]
	at org.apache.flink.runtime.source.coordinator.SourceCoordinator.lambda$runInEventLoop$10(SourceCoordinator.java:533) ~[flink-runtime-1.20.0.jar:1.20.0]
	at org.apache.flink.util.ThrowableCatchingRunnable.run(ThrowableCatchingRunnable.java:40) [flink-core-1.20.0.jar:1.20.0]
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:539) [?:?]
	at java.util.concurrent.FutureTask.run(FutureTask.java:264) [?:?]
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:304) [?:?]
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136) [?:?]
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635) [?:?]
	at java.lang.Thread.run(Thread.java:840) [?:?]
2025-08-20 14:39:23.257 [SourceCoordinator-Source: Kafka-交易] INFO  org.apache.flink.connector.kafka.source.enumerator.KafkaSourceEnumerator - Starting the KafkaSourceEnumerator for consumer group orderbook-local-test-group without periodic partition discovery.
2025-08-20 14:39:23.538 [SourceCoordinator-Source: Kafka-组合订单] INFO  org.apache.flink.connector.kafka.source.enumerator.KafkaSourceEnumerator - Discovered new partitions: [cmb_order_data_event-0]
2025-08-20 14:39:23.538 [SourceCoordinator-Source: Kafka-单腿订单] INFO  org.apache.flink.connector.kafka.source.enumerator.KafkaSourceEnumerator - Discovered new partitions: [singleleg_order_data_event-0]
2025-08-20 14:39:23.538 [SourceCoordinator-Source: Kafka-交易] INFO  org.apache.flink.connector.kafka.source.enumerator.KafkaSourceEnumerator - Discovered new partitions: [trade_data_event-0]
2025-08-20 14:39:23.540 [SourceCoordinator-Source: Kafka-单腿订单] INFO  org.apache.flink.connector.kafka.source.enumerator.KafkaSourceEnumerator - Assigning splits to readers {0=[[Partition: singleleg_order_data_event-0, StartingOffset: -2, StoppingOffset: -9223372036854775808]]}
2025-08-20 14:39:23.540 [SourceCoordinator-Source: Kafka-组合订单] INFO  org.apache.flink.connector.kafka.source.enumerator.KafkaSourceEnumerator - Assigning splits to readers {0=[[Partition: cmb_order_data_event-0, StartingOffset: -2, StoppingOffset: -9223372036854775808]]}
2025-08-20 14:39:23.540 [SourceCoordinator-Source: Kafka-交易] INFO  org.apache.flink.connector.kafka.source.enumerator.KafkaSourceEnumerator - Assigning splits to readers {0=[[Partition: trade_data_event-0, StartingOffset: -2, StoppingOffset: -9223372036854775808]]}
2025-08-20 14:39:23.569 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] WARN  org.apache.kafka.clients.consumer.ConsumerConfig - These configurations '[client.id.prefix, partition.discovery.interval.ms]' were supplied but are not used yet.
2025-08-20 14:39:23.570 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] WARN  org.apache.kafka.clients.consumer.ConsumerConfig - These configurations '[client.id.prefix, partition.discovery.interval.ms]' were supplied but are not used yet.
2025-08-20 14:39:23.570 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] WARN  org.apache.kafka.clients.consumer.ConsumerConfig - These configurations '[client.id.prefix, partition.discovery.interval.ms]' were supplied but are not used yet.
2025-08-20 14:39:23.570 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] WARN  org.apache.kafka.common.utils.AppInfoParser - Error registering AppInfo mbean
javax.management.InstanceAlreadyExistsException: kafka.consumer:type=app-info,id=orderbook-local-test-group-0
	at com.sun.jmx.mbeanserver.Repository.addMBean(Repository.java:436) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerWithRepository(DefaultMBeanServerInterceptor.java:1865) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerDynamicMBean(DefaultMBeanServerInterceptor.java:960) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerObject(DefaultMBeanServerInterceptor.java:895) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerMBean(DefaultMBeanServerInterceptor.java:320) ~[?:?]
	at com.sun.jmx.mbeanserver.JmxMBeanServer.registerMBean(JmxMBeanServer.java:523) ~[?:?]
	at org.apache.kafka.common.utils.AppInfoParser.registerAppInfo(AppInfoParser.java:64) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.consumer.KafkaConsumer.<init>(KafkaConsumer.java:821) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.consumer.KafkaConsumer.<init>(KafkaConsumer.java:665) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.consumer.KafkaConsumer.<init>(KafkaConsumer.java:646) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.consumer.KafkaConsumer.<init>(KafkaConsumer.java:626) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.flink.connector.kafka.source.reader.KafkaPartitionSplitReader.<init>(KafkaPartitionSplitReader.java:97) ~[flink-connector-kafka-3.4.0-1.20.jar:3.4.0-1.20]
	at org.apache.flink.connector.kafka.source.KafkaSource.lambda$createReader$1(KafkaSource.java:185) ~[flink-connector-kafka-3.4.0-1.20.jar:3.4.0-1.20]
	at org.apache.flink.connector.base.source.reader.fetcher.SplitFetcherManager.createSplitFetcher(SplitFetcherManager.java:259) ~[flink-connector-base-1.20.0.jar:1.20.0]
	at org.apache.flink.connector.base.source.reader.fetcher.SingleThreadFetcherManager.addSplits(SingleThreadFetcherManager.java:148) ~[flink-connector-base-1.20.0.jar:1.20.0]
	at org.apache.flink.connector.base.source.reader.SourceReaderBase.addSplits(SourceReaderBase.java:315) ~[flink-connector-base-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.api.operators.SourceOperator.handleAddSplitsEvent(SourceOperator.java:606) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.api.operators.SourceOperator.handleOperatorEvent(SourceOperator.java:575) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.OperatorEventDispatcherImpl.dispatchEventToHandlers(OperatorEventDispatcherImpl.java:72) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.RegularOperatorChain.dispatchOperatorEvent(RegularOperatorChain.java:80) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.StreamTask.lambda$dispatchOperatorEvent$24(StreamTask.java:1609) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.StreamTaskActionExecutor$1.runThrowing(StreamTaskActionExecutor.java:50) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.mailbox.Mail.run(Mail.java:101) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.mailbox.MailboxProcessor.runMail(MailboxProcessor.java:414) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.mailbox.MailboxProcessor.processMailsWhenDefaultActionUnavailable(MailboxProcessor.java:383) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.mailbox.MailboxProcessor.processMail(MailboxProcessor.java:368) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.mailbox.MailboxProcessor.runMailboxLoop(MailboxProcessor.java:229) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.StreamTask.runMailboxLoop(StreamTask.java:973) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.StreamTask.invoke(StreamTask.java:917) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.runtime.taskmanager.Task.runWithSystemExitMonitoring(Task.java:970) [flink-runtime-1.20.0.jar:1.20.0]
	at org.apache.flink.runtime.taskmanager.Task.restoreAndInvoke(Task.java:949) [flink-runtime-1.20.0.jar:1.20.0]
	at org.apache.flink.runtime.taskmanager.Task.doRun(Task.java:763) [flink-runtime-1.20.0.jar:1.20.0]
	at org.apache.flink.runtime.taskmanager.Task.run(Task.java:575) [flink-runtime-1.20.0.jar:1.20.0]
	at java.lang.Thread.run(Thread.java:840) [?:?]
2025-08-20 14:39:23.570 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] WARN  org.apache.kafka.common.utils.AppInfoParser - Error registering AppInfo mbean
javax.management.InstanceAlreadyExistsException: kafka.consumer:type=app-info,id=orderbook-local-test-group-0
	at com.sun.jmx.mbeanserver.Repository.addMBean(Repository.java:436) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerWithRepository(DefaultMBeanServerInterceptor.java:1865) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerDynamicMBean(DefaultMBeanServerInterceptor.java:960) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerObject(DefaultMBeanServerInterceptor.java:895) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerMBean(DefaultMBeanServerInterceptor.java:320) ~[?:?]
	at com.sun.jmx.mbeanserver.JmxMBeanServer.registerMBean(JmxMBeanServer.java:523) ~[?:?]
	at org.apache.kafka.common.utils.AppInfoParser.registerAppInfo(AppInfoParser.java:64) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.consumer.KafkaConsumer.<init>(KafkaConsumer.java:821) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.consumer.KafkaConsumer.<init>(KafkaConsumer.java:665) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.consumer.KafkaConsumer.<init>(KafkaConsumer.java:646) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.consumer.KafkaConsumer.<init>(KafkaConsumer.java:626) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.flink.connector.kafka.source.reader.KafkaPartitionSplitReader.<init>(KafkaPartitionSplitReader.java:97) ~[flink-connector-kafka-3.4.0-1.20.jar:3.4.0-1.20]
	at org.apache.flink.connector.kafka.source.KafkaSource.lambda$createReader$1(KafkaSource.java:185) ~[flink-connector-kafka-3.4.0-1.20.jar:3.4.0-1.20]
	at org.apache.flink.connector.base.source.reader.fetcher.SplitFetcherManager.createSplitFetcher(SplitFetcherManager.java:259) ~[flink-connector-base-1.20.0.jar:1.20.0]
	at org.apache.flink.connector.base.source.reader.fetcher.SingleThreadFetcherManager.addSplits(SingleThreadFetcherManager.java:148) ~[flink-connector-base-1.20.0.jar:1.20.0]
	at org.apache.flink.connector.base.source.reader.SourceReaderBase.addSplits(SourceReaderBase.java:315) ~[flink-connector-base-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.api.operators.SourceOperator.handleAddSplitsEvent(SourceOperator.java:606) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.api.operators.SourceOperator.handleOperatorEvent(SourceOperator.java:575) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.OperatorEventDispatcherImpl.dispatchEventToHandlers(OperatorEventDispatcherImpl.java:72) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.RegularOperatorChain.dispatchOperatorEvent(RegularOperatorChain.java:80) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.StreamTask.lambda$dispatchOperatorEvent$24(StreamTask.java:1609) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.StreamTaskActionExecutor$1.runThrowing(StreamTaskActionExecutor.java:50) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.mailbox.Mail.run(Mail.java:101) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.mailbox.MailboxProcessor.runMail(MailboxProcessor.java:414) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.mailbox.MailboxProcessor.processMailsWhenDefaultActionUnavailable(MailboxProcessor.java:383) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.mailbox.MailboxProcessor.processMail(MailboxProcessor.java:368) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.mailbox.MailboxProcessor.runMailboxLoop(MailboxProcessor.java:229) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.StreamTask.runMailboxLoop(StreamTask.java:973) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.StreamTask.invoke(StreamTask.java:917) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.runtime.taskmanager.Task.runWithSystemExitMonitoring(Task.java:970) [flink-runtime-1.20.0.jar:1.20.0]
	at org.apache.flink.runtime.taskmanager.Task.restoreAndInvoke(Task.java:949) [flink-runtime-1.20.0.jar:1.20.0]
	at org.apache.flink.runtime.taskmanager.Task.doRun(Task.java:763) [flink-runtime-1.20.0.jar:1.20.0]
	at org.apache.flink.runtime.taskmanager.Task.run(Task.java:575) [flink-runtime-1.20.0.jar:1.20.0]
	at java.lang.Thread.run(Thread.java:840) [?:?]
2025-08-20 14:46:26.623 [main] WARN  org.apache.flink.connector.kafka.source.KafkaSourceBuilder - Property auto.offset.reset is provided but will be overridden from earliest to earliest
2025-08-20 14:46:26.642 [main] WARN  org.apache.flink.connector.kafka.source.KafkaSourceBuilder - Property auto.offset.reset is provided but will be overridden from earliest to earliest
2025-08-20 14:46:26.643 [main] WARN  org.apache.flink.connector.kafka.source.KafkaSourceBuilder - Property auto.offset.reset is provided but will be overridden from earliest to earliest
2025-08-20 14:46:27.410 [SourceCoordinator-Source: Kafka-组合订单] WARN  org.apache.kafka.clients.admin.AdminClientConfig - These configurations '[key.deserializer, value.deserializer, max.poll.records, client.id.prefix, group.id, partition.discovery.interval.ms, heartbeat.interval.ms, enable.auto.commit, fetch.max.wait.ms, fetch.min.bytes, session.timeout.ms, auto.offset.reset]' were supplied but are not used yet.
2025-08-20 14:46:27.410 [SourceCoordinator-Source: Kafka-交易] WARN  org.apache.kafka.clients.admin.AdminClientConfig - These configurations '[key.deserializer, value.deserializer, max.poll.records, client.id.prefix, group.id, partition.discovery.interval.ms, heartbeat.interval.ms, enable.auto.commit, fetch.max.wait.ms, fetch.min.bytes, session.timeout.ms, auto.offset.reset]' were supplied but are not used yet.
2025-08-20 14:46:27.410 [SourceCoordinator-Source: Kafka-单腿订单] WARN  org.apache.kafka.clients.admin.AdminClientConfig - These configurations '[key.deserializer, value.deserializer, max.poll.records, client.id.prefix, group.id, partition.discovery.interval.ms, heartbeat.interval.ms, enable.auto.commit, fetch.max.wait.ms, fetch.min.bytes, session.timeout.ms, auto.offset.reset]' were supplied but are not used yet.
2025-08-20 14:46:27.411 [SourceCoordinator-Source: Kafka-组合订单] INFO  org.apache.flink.connector.kafka.source.enumerator.KafkaSourceEnumerator - Starting the KafkaSourceEnumerator for consumer group orderbook-local-test-group without periodic partition discovery.
2025-08-20 14:46:27.411 [SourceCoordinator-Source: Kafka-单腿订单] WARN  org.apache.kafka.common.utils.AppInfoParser - Error registering AppInfo mbean
javax.management.InstanceAlreadyExistsException: kafka.admin.client:type=app-info,id=orderbook-local-test-group-enumerator-admin-client
	at com.sun.jmx.mbeanserver.Repository.addMBean(Repository.java:436) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerWithRepository(DefaultMBeanServerInterceptor.java:1865) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerDynamicMBean(DefaultMBeanServerInterceptor.java:960) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerObject(DefaultMBeanServerInterceptor.java:895) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerMBean(DefaultMBeanServerInterceptor.java:320) ~[?:?]
	at com.sun.jmx.mbeanserver.JmxMBeanServer.registerMBean(JmxMBeanServer.java:523) ~[?:?]
	at org.apache.kafka.common.utils.AppInfoParser.registerAppInfo(AppInfoParser.java:64) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.admin.KafkaAdminClient.<init>(KafkaAdminClient.java:602) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.admin.KafkaAdminClient.createInternal(KafkaAdminClient.java:544) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.admin.KafkaAdminClient.createInternal(KafkaAdminClient.java:488) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.admin.Admin.create(Admin.java:134) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.admin.AdminClient.create(AdminClient.java:39) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.flink.connector.kafka.source.enumerator.KafkaSourceEnumerator.getKafkaAdminClient(KafkaSourceEnumerator.java:441) ~[flink-connector-kafka-3.4.0-1.20.jar:3.4.0-1.20]
	at org.apache.flink.connector.kafka.source.enumerator.KafkaSourceEnumerator.start(KafkaSourceEnumerator.java:164) ~[flink-connector-kafka-3.4.0-1.20.jar:3.4.0-1.20]
	at org.apache.flink.runtime.source.coordinator.SourceCoordinator.lambda$start$1(SourceCoordinator.java:244) ~[flink-runtime-1.20.0.jar:1.20.0]
	at org.apache.flink.runtime.source.coordinator.SourceCoordinator.lambda$runInEventLoop$10(SourceCoordinator.java:533) ~[flink-runtime-1.20.0.jar:1.20.0]
	at org.apache.flink.util.ThrowableCatchingRunnable.run(ThrowableCatchingRunnable.java:40) [flink-core-1.20.0.jar:1.20.0]
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:539) [?:?]
	at java.util.concurrent.FutureTask.run(FutureTask.java:264) [?:?]
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:304) [?:?]
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136) [?:?]
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635) [?:?]
	at java.lang.Thread.run(Thread.java:840) [?:?]
2025-08-20 14:46:27.414 [SourceCoordinator-Source: Kafka-单腿订单] INFO  org.apache.flink.connector.kafka.source.enumerator.KafkaSourceEnumerator - Starting the KafkaSourceEnumerator for consumer group orderbook-local-test-group without periodic partition discovery.
2025-08-20 14:46:27.414 [SourceCoordinator-Source: Kafka-交易] WARN  org.apache.kafka.common.utils.AppInfoParser - Error registering AppInfo mbean
javax.management.InstanceAlreadyExistsException: kafka.admin.client:type=app-info,id=orderbook-local-test-group-enumerator-admin-client
	at com.sun.jmx.mbeanserver.Repository.addMBean(Repository.java:436) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerWithRepository(DefaultMBeanServerInterceptor.java:1865) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerDynamicMBean(DefaultMBeanServerInterceptor.java:960) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerObject(DefaultMBeanServerInterceptor.java:895) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerMBean(DefaultMBeanServerInterceptor.java:320) ~[?:?]
	at com.sun.jmx.mbeanserver.JmxMBeanServer.registerMBean(JmxMBeanServer.java:523) ~[?:?]
	at org.apache.kafka.common.utils.AppInfoParser.registerAppInfo(AppInfoParser.java:64) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.admin.KafkaAdminClient.<init>(KafkaAdminClient.java:602) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.admin.KafkaAdminClient.createInternal(KafkaAdminClient.java:544) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.admin.KafkaAdminClient.createInternal(KafkaAdminClient.java:488) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.admin.Admin.create(Admin.java:134) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.admin.AdminClient.create(AdminClient.java:39) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.flink.connector.kafka.source.enumerator.KafkaSourceEnumerator.getKafkaAdminClient(KafkaSourceEnumerator.java:441) ~[flink-connector-kafka-3.4.0-1.20.jar:3.4.0-1.20]
	at org.apache.flink.connector.kafka.source.enumerator.KafkaSourceEnumerator.start(KafkaSourceEnumerator.java:164) ~[flink-connector-kafka-3.4.0-1.20.jar:3.4.0-1.20]
	at org.apache.flink.runtime.source.coordinator.SourceCoordinator.lambda$start$1(SourceCoordinator.java:244) ~[flink-runtime-1.20.0.jar:1.20.0]
	at org.apache.flink.runtime.source.coordinator.SourceCoordinator.lambda$runInEventLoop$10(SourceCoordinator.java:533) ~[flink-runtime-1.20.0.jar:1.20.0]
	at org.apache.flink.util.ThrowableCatchingRunnable.run(ThrowableCatchingRunnable.java:40) [flink-core-1.20.0.jar:1.20.0]
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:539) [?:?]
	at java.util.concurrent.FutureTask.run(FutureTask.java:264) [?:?]
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:304) [?:?]
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136) [?:?]
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635) [?:?]
	at java.lang.Thread.run(Thread.java:840) [?:?]
2025-08-20 14:46:27.414 [SourceCoordinator-Source: Kafka-交易] INFO  org.apache.flink.connector.kafka.source.enumerator.KafkaSourceEnumerator - Starting the KafkaSourceEnumerator for consumer group orderbook-local-test-group without periodic partition discovery.
2025-08-20 14:46:27.515 [SourceCoordinator-Source: Kafka-组合订单] INFO  org.apache.flink.connector.kafka.source.enumerator.KafkaSourceEnumerator - Discovered new partitions: [cmb_order_data_event-0]
2025-08-20 14:46:27.515 [SourceCoordinator-Source: Kafka-单腿订单] INFO  org.apache.flink.connector.kafka.source.enumerator.KafkaSourceEnumerator - Discovered new partitions: [singleleg_order_data_event-0]
2025-08-20 14:46:27.515 [SourceCoordinator-Source: Kafka-交易] INFO  org.apache.flink.connector.kafka.source.enumerator.KafkaSourceEnumerator - Discovered new partitions: [trade_data_event-0]
2025-08-20 14:46:27.535 [SourceCoordinator-Source: Kafka-组合订单] INFO  org.apache.flink.connector.kafka.source.enumerator.KafkaSourceEnumerator - Assigning splits to readers {0=[[Partition: cmb_order_data_event-0, StartingOffset: -2, StoppingOffset: -9223372036854775808]]}
2025-08-20 14:46:27.535 [SourceCoordinator-Source: Kafka-单腿订单] INFO  org.apache.flink.connector.kafka.source.enumerator.KafkaSourceEnumerator - Assigning splits to readers {0=[[Partition: singleleg_order_data_event-0, StartingOffset: -2, StoppingOffset: -9223372036854775808]]}
2025-08-20 14:46:27.535 [SourceCoordinator-Source: Kafka-交易] INFO  org.apache.flink.connector.kafka.source.enumerator.KafkaSourceEnumerator - Assigning splits to readers {0=[[Partition: trade_data_event-0, StartingOffset: -2, StoppingOffset: -9223372036854775808]]}
2025-08-20 14:46:27.563 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] WARN  org.apache.kafka.clients.consumer.ConsumerConfig - These configurations '[client.id.prefix, partition.discovery.interval.ms]' were supplied but are not used yet.
2025-08-20 14:46:27.563 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] WARN  org.apache.kafka.clients.consumer.ConsumerConfig - These configurations '[client.id.prefix, partition.discovery.interval.ms]' were supplied but are not used yet.
2025-08-20 14:46:27.563 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] WARN  org.apache.kafka.clients.consumer.ConsumerConfig - These configurations '[client.id.prefix, partition.discovery.interval.ms]' were supplied but are not used yet.
2025-08-20 14:46:27.563 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] WARN  org.apache.kafka.common.utils.AppInfoParser - Error registering AppInfo mbean
javax.management.InstanceAlreadyExistsException: kafka.consumer:type=app-info,id=orderbook-local-test-group-0
	at com.sun.jmx.mbeanserver.Repository.addMBean(Repository.java:436) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerWithRepository(DefaultMBeanServerInterceptor.java:1865) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerDynamicMBean(DefaultMBeanServerInterceptor.java:960) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerObject(DefaultMBeanServerInterceptor.java:895) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerMBean(DefaultMBeanServerInterceptor.java:320) ~[?:?]
	at com.sun.jmx.mbeanserver.JmxMBeanServer.registerMBean(JmxMBeanServer.java:523) ~[?:?]
	at org.apache.kafka.common.utils.AppInfoParser.registerAppInfo(AppInfoParser.java:64) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.consumer.KafkaConsumer.<init>(KafkaConsumer.java:821) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.consumer.KafkaConsumer.<init>(KafkaConsumer.java:665) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.consumer.KafkaConsumer.<init>(KafkaConsumer.java:646) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.consumer.KafkaConsumer.<init>(KafkaConsumer.java:626) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.flink.connector.kafka.source.reader.KafkaPartitionSplitReader.<init>(KafkaPartitionSplitReader.java:97) ~[flink-connector-kafka-3.4.0-1.20.jar:3.4.0-1.20]
	at org.apache.flink.connector.kafka.source.KafkaSource.lambda$createReader$1(KafkaSource.java:185) ~[flink-connector-kafka-3.4.0-1.20.jar:3.4.0-1.20]
	at org.apache.flink.connector.base.source.reader.fetcher.SplitFetcherManager.createSplitFetcher(SplitFetcherManager.java:259) ~[flink-connector-base-1.20.0.jar:1.20.0]
	at org.apache.flink.connector.base.source.reader.fetcher.SingleThreadFetcherManager.addSplits(SingleThreadFetcherManager.java:148) ~[flink-connector-base-1.20.0.jar:1.20.0]
	at org.apache.flink.connector.base.source.reader.SourceReaderBase.addSplits(SourceReaderBase.java:315) ~[flink-connector-base-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.api.operators.SourceOperator.handleAddSplitsEvent(SourceOperator.java:606) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.api.operators.SourceOperator.handleOperatorEvent(SourceOperator.java:575) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.OperatorEventDispatcherImpl.dispatchEventToHandlers(OperatorEventDispatcherImpl.java:72) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.RegularOperatorChain.dispatchOperatorEvent(RegularOperatorChain.java:80) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.StreamTask.lambda$dispatchOperatorEvent$24(StreamTask.java:1609) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.StreamTaskActionExecutor$1.runThrowing(StreamTaskActionExecutor.java:50) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.mailbox.Mail.run(Mail.java:101) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.mailbox.MailboxProcessor.runMail(MailboxProcessor.java:414) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.mailbox.MailboxProcessor.processMailsWhenDefaultActionUnavailable(MailboxProcessor.java:383) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.mailbox.MailboxProcessor.processMail(MailboxProcessor.java:368) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.mailbox.MailboxProcessor.runMailboxLoop(MailboxProcessor.java:229) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.StreamTask.runMailboxLoop(StreamTask.java:973) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.StreamTask.invoke(StreamTask.java:917) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.runtime.taskmanager.Task.runWithSystemExitMonitoring(Task.java:970) [flink-runtime-1.20.0.jar:1.20.0]
	at org.apache.flink.runtime.taskmanager.Task.restoreAndInvoke(Task.java:949) [flink-runtime-1.20.0.jar:1.20.0]
	at org.apache.flink.runtime.taskmanager.Task.doRun(Task.java:763) [flink-runtime-1.20.0.jar:1.20.0]
	at org.apache.flink.runtime.taskmanager.Task.run(Task.java:575) [flink-runtime-1.20.0.jar:1.20.0]
	at java.lang.Thread.run(Thread.java:840) [?:?]
2025-08-20 14:46:27.564 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] WARN  org.apache.kafka.common.utils.AppInfoParser - Error registering AppInfo mbean
javax.management.InstanceAlreadyExistsException: kafka.consumer:type=app-info,id=orderbook-local-test-group-0
	at com.sun.jmx.mbeanserver.Repository.addMBean(Repository.java:436) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerWithRepository(DefaultMBeanServerInterceptor.java:1865) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerDynamicMBean(DefaultMBeanServerInterceptor.java:960) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerObject(DefaultMBeanServerInterceptor.java:895) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerMBean(DefaultMBeanServerInterceptor.java:320) ~[?:?]
	at com.sun.jmx.mbeanserver.JmxMBeanServer.registerMBean(JmxMBeanServer.java:523) ~[?:?]
	at org.apache.kafka.common.utils.AppInfoParser.registerAppInfo(AppInfoParser.java:64) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.consumer.KafkaConsumer.<init>(KafkaConsumer.java:821) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.consumer.KafkaConsumer.<init>(KafkaConsumer.java:665) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.consumer.KafkaConsumer.<init>(KafkaConsumer.java:646) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.consumer.KafkaConsumer.<init>(KafkaConsumer.java:626) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.flink.connector.kafka.source.reader.KafkaPartitionSplitReader.<init>(KafkaPartitionSplitReader.java:97) ~[flink-connector-kafka-3.4.0-1.20.jar:3.4.0-1.20]
	at org.apache.flink.connector.kafka.source.KafkaSource.lambda$createReader$1(KafkaSource.java:185) ~[flink-connector-kafka-3.4.0-1.20.jar:3.4.0-1.20]
	at org.apache.flink.connector.base.source.reader.fetcher.SplitFetcherManager.createSplitFetcher(SplitFetcherManager.java:259) ~[flink-connector-base-1.20.0.jar:1.20.0]
	at org.apache.flink.connector.base.source.reader.fetcher.SingleThreadFetcherManager.addSplits(SingleThreadFetcherManager.java:148) ~[flink-connector-base-1.20.0.jar:1.20.0]
	at org.apache.flink.connector.base.source.reader.SourceReaderBase.addSplits(SourceReaderBase.java:315) ~[flink-connector-base-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.api.operators.SourceOperator.handleAddSplitsEvent(SourceOperator.java:606) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.api.operators.SourceOperator.handleOperatorEvent(SourceOperator.java:575) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.OperatorEventDispatcherImpl.dispatchEventToHandlers(OperatorEventDispatcherImpl.java:72) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.RegularOperatorChain.dispatchOperatorEvent(RegularOperatorChain.java:80) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.StreamTask.lambda$dispatchOperatorEvent$24(StreamTask.java:1609) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.StreamTaskActionExecutor$1.runThrowing(StreamTaskActionExecutor.java:50) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.mailbox.Mail.run(Mail.java:101) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.mailbox.MailboxProcessor.runMail(MailboxProcessor.java:414) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.mailbox.MailboxProcessor.processMailsWhenDefaultActionUnavailable(MailboxProcessor.java:383) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.mailbox.MailboxProcessor.processMail(MailboxProcessor.java:368) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.mailbox.MailboxProcessor.runMailboxLoop(MailboxProcessor.java:229) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.StreamTask.runMailboxLoop(StreamTask.java:973) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.StreamTask.invoke(StreamTask.java:917) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.runtime.taskmanager.Task.runWithSystemExitMonitoring(Task.java:970) [flink-runtime-1.20.0.jar:1.20.0]
	at org.apache.flink.runtime.taskmanager.Task.restoreAndInvoke(Task.java:949) [flink-runtime-1.20.0.jar:1.20.0]
	at org.apache.flink.runtime.taskmanager.Task.doRun(Task.java:763) [flink-runtime-1.20.0.jar:1.20.0]
	at org.apache.flink.runtime.taskmanager.Task.run(Task.java:575) [flink-runtime-1.20.0.jar:1.20.0]
	at java.lang.Thread.run(Thread.java:840) [?:?]
2025-08-20 14:47:43.994 [main] WARN  org.apache.flink.connector.kafka.source.KafkaSourceBuilder - Property auto.offset.reset is provided but will be overridden from earliest to earliest
2025-08-20 14:47:44.010 [main] WARN  org.apache.flink.connector.kafka.source.KafkaSourceBuilder - Property auto.offset.reset is provided but will be overridden from earliest to earliest
2025-08-20 14:47:44.010 [main] WARN  org.apache.flink.connector.kafka.source.KafkaSourceBuilder - Property auto.offset.reset is provided but will be overridden from earliest to earliest
2025-08-20 14:47:44.758 [SourceCoordinator-Source: Kafka-交易] WARN  org.apache.kafka.clients.admin.AdminClientConfig - These configurations '[key.deserializer, value.deserializer, max.poll.records, client.id.prefix, group.id, partition.discovery.interval.ms, heartbeat.interval.ms, enable.auto.commit, fetch.max.wait.ms, fetch.min.bytes, session.timeout.ms, auto.offset.reset]' were supplied but are not used yet.
2025-08-20 14:47:44.758 [SourceCoordinator-Source: Kafka-组合订单] WARN  org.apache.kafka.clients.admin.AdminClientConfig - These configurations '[key.deserializer, value.deserializer, max.poll.records, client.id.prefix, group.id, partition.discovery.interval.ms, heartbeat.interval.ms, enable.auto.commit, fetch.max.wait.ms, fetch.min.bytes, session.timeout.ms, auto.offset.reset]' were supplied but are not used yet.
2025-08-20 14:47:44.758 [SourceCoordinator-Source: Kafka-单腿订单] WARN  org.apache.kafka.clients.admin.AdminClientConfig - These configurations '[key.deserializer, value.deserializer, max.poll.records, client.id.prefix, group.id, partition.discovery.interval.ms, heartbeat.interval.ms, enable.auto.commit, fetch.max.wait.ms, fetch.min.bytes, session.timeout.ms, auto.offset.reset]' were supplied but are not used yet.
2025-08-20 14:47:44.759 [SourceCoordinator-Source: Kafka-交易] INFO  org.apache.flink.connector.kafka.source.enumerator.KafkaSourceEnumerator - Starting the KafkaSourceEnumerator for consumer group orderbook-local-test-group without periodic partition discovery.
2025-08-20 14:47:44.759 [SourceCoordinator-Source: Kafka-单腿订单] WARN  org.apache.kafka.common.utils.AppInfoParser - Error registering AppInfo mbean
javax.management.InstanceAlreadyExistsException: kafka.admin.client:type=app-info,id=orderbook-local-test-group-enumerator-admin-client
	at com.sun.jmx.mbeanserver.Repository.addMBean(Repository.java:436) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerWithRepository(DefaultMBeanServerInterceptor.java:1865) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerDynamicMBean(DefaultMBeanServerInterceptor.java:960) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerObject(DefaultMBeanServerInterceptor.java:895) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerMBean(DefaultMBeanServerInterceptor.java:320) ~[?:?]
	at com.sun.jmx.mbeanserver.JmxMBeanServer.registerMBean(JmxMBeanServer.java:523) ~[?:?]
	at org.apache.kafka.common.utils.AppInfoParser.registerAppInfo(AppInfoParser.java:64) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.admin.KafkaAdminClient.<init>(KafkaAdminClient.java:602) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.admin.KafkaAdminClient.createInternal(KafkaAdminClient.java:544) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.admin.KafkaAdminClient.createInternal(KafkaAdminClient.java:488) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.admin.Admin.create(Admin.java:134) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.admin.AdminClient.create(AdminClient.java:39) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.flink.connector.kafka.source.enumerator.KafkaSourceEnumerator.getKafkaAdminClient(KafkaSourceEnumerator.java:441) ~[flink-connector-kafka-3.4.0-1.20.jar:3.4.0-1.20]
	at org.apache.flink.connector.kafka.source.enumerator.KafkaSourceEnumerator.start(KafkaSourceEnumerator.java:164) ~[flink-connector-kafka-3.4.0-1.20.jar:3.4.0-1.20]
	at org.apache.flink.runtime.source.coordinator.SourceCoordinator.lambda$start$1(SourceCoordinator.java:244) ~[flink-runtime-1.20.0.jar:1.20.0]
	at org.apache.flink.runtime.source.coordinator.SourceCoordinator.lambda$runInEventLoop$10(SourceCoordinator.java:533) ~[flink-runtime-1.20.0.jar:1.20.0]
	at org.apache.flink.util.ThrowableCatchingRunnable.run(ThrowableCatchingRunnable.java:40) [flink-core-1.20.0.jar:1.20.0]
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:539) [?:?]
	at java.util.concurrent.FutureTask.run(FutureTask.java:264) [?:?]
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:304) [?:?]
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136) [?:?]
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635) [?:?]
	at java.lang.Thread.run(Thread.java:840) [?:?]
2025-08-20 14:47:44.762 [SourceCoordinator-Source: Kafka-单腿订单] INFO  org.apache.flink.connector.kafka.source.enumerator.KafkaSourceEnumerator - Starting the KafkaSourceEnumerator for consumer group orderbook-local-test-group without periodic partition discovery.
2025-08-20 14:47:44.762 [SourceCoordinator-Source: Kafka-组合订单] WARN  org.apache.kafka.common.utils.AppInfoParser - Error registering AppInfo mbean
javax.management.InstanceAlreadyExistsException: kafka.admin.client:type=app-info,id=orderbook-local-test-group-enumerator-admin-client
	at com.sun.jmx.mbeanserver.Repository.addMBean(Repository.java:436) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerWithRepository(DefaultMBeanServerInterceptor.java:1865) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerDynamicMBean(DefaultMBeanServerInterceptor.java:960) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerObject(DefaultMBeanServerInterceptor.java:895) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerMBean(DefaultMBeanServerInterceptor.java:320) ~[?:?]
	at com.sun.jmx.mbeanserver.JmxMBeanServer.registerMBean(JmxMBeanServer.java:523) ~[?:?]
	at org.apache.kafka.common.utils.AppInfoParser.registerAppInfo(AppInfoParser.java:64) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.admin.KafkaAdminClient.<init>(KafkaAdminClient.java:602) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.admin.KafkaAdminClient.createInternal(KafkaAdminClient.java:544) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.admin.KafkaAdminClient.createInternal(KafkaAdminClient.java:488) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.admin.Admin.create(Admin.java:134) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.admin.AdminClient.create(AdminClient.java:39) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.flink.connector.kafka.source.enumerator.KafkaSourceEnumerator.getKafkaAdminClient(KafkaSourceEnumerator.java:441) ~[flink-connector-kafka-3.4.0-1.20.jar:3.4.0-1.20]
	at org.apache.flink.connector.kafka.source.enumerator.KafkaSourceEnumerator.start(KafkaSourceEnumerator.java:164) ~[flink-connector-kafka-3.4.0-1.20.jar:3.4.0-1.20]
	at org.apache.flink.runtime.source.coordinator.SourceCoordinator.lambda$start$1(SourceCoordinator.java:244) ~[flink-runtime-1.20.0.jar:1.20.0]
	at org.apache.flink.runtime.source.coordinator.SourceCoordinator.lambda$runInEventLoop$10(SourceCoordinator.java:533) ~[flink-runtime-1.20.0.jar:1.20.0]
	at org.apache.flink.util.ThrowableCatchingRunnable.run(ThrowableCatchingRunnable.java:40) [flink-core-1.20.0.jar:1.20.0]
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:539) [?:?]
	at java.util.concurrent.FutureTask.run(FutureTask.java:264) [?:?]
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:304) [?:?]
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136) [?:?]
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635) [?:?]
	at java.lang.Thread.run(Thread.java:840) [?:?]
2025-08-20 14:47:44.762 [SourceCoordinator-Source: Kafka-组合订单] INFO  org.apache.flink.connector.kafka.source.enumerator.KafkaSourceEnumerator - Starting the KafkaSourceEnumerator for consumer group orderbook-local-test-group without periodic partition discovery.
2025-08-20 14:47:44.858 [SourceCoordinator-Source: Kafka-交易] INFO  org.apache.flink.connector.kafka.source.enumerator.KafkaSourceEnumerator - Discovered new partitions: [trade_data_event-0]
2025-08-20 14:47:44.858 [SourceCoordinator-Source: Kafka-单腿订单] INFO  org.apache.flink.connector.kafka.source.enumerator.KafkaSourceEnumerator - Discovered new partitions: [singleleg_order_data_event-0]
2025-08-20 14:47:44.858 [SourceCoordinator-Source: Kafka-组合订单] INFO  org.apache.flink.connector.kafka.source.enumerator.KafkaSourceEnumerator - Discovered new partitions: [cmb_order_data_event-0]
2025-08-20 14:47:44.876 [SourceCoordinator-Source: Kafka-单腿订单] INFO  org.apache.flink.connector.kafka.source.enumerator.KafkaSourceEnumerator - Assigning splits to readers {0=[[Partition: singleleg_order_data_event-0, StartingOffset: -2, StoppingOffset: -9223372036854775808]]}
2025-08-20 14:47:44.876 [SourceCoordinator-Source: Kafka-交易] INFO  org.apache.flink.connector.kafka.source.enumerator.KafkaSourceEnumerator - Assigning splits to readers {0=[[Partition: trade_data_event-0, StartingOffset: -2, StoppingOffset: -9223372036854775808]]}
2025-08-20 14:47:44.876 [SourceCoordinator-Source: Kafka-组合订单] INFO  org.apache.flink.connector.kafka.source.enumerator.KafkaSourceEnumerator - Assigning splits to readers {0=[[Partition: cmb_order_data_event-0, StartingOffset: -2, StoppingOffset: -9223372036854775808]]}
2025-08-20 14:47:44.906 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] WARN  org.apache.kafka.clients.consumer.ConsumerConfig - These configurations '[client.id.prefix, partition.discovery.interval.ms]' were supplied but are not used yet.
2025-08-20 14:47:44.906 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] WARN  org.apache.kafka.clients.consumer.ConsumerConfig - These configurations '[client.id.prefix, partition.discovery.interval.ms]' were supplied but are not used yet.
2025-08-20 14:47:44.907 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] WARN  org.apache.kafka.clients.consumer.ConsumerConfig - These configurations '[client.id.prefix, partition.discovery.interval.ms]' were supplied but are not used yet.
2025-08-20 14:47:44.907 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] WARN  org.apache.kafka.common.utils.AppInfoParser - Error registering AppInfo mbean
javax.management.InstanceAlreadyExistsException: kafka.consumer:type=app-info,id=orderbook-local-test-group-0
	at com.sun.jmx.mbeanserver.Repository.addMBean(Repository.java:436) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerWithRepository(DefaultMBeanServerInterceptor.java:1865) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerDynamicMBean(DefaultMBeanServerInterceptor.java:960) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerObject(DefaultMBeanServerInterceptor.java:895) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerMBean(DefaultMBeanServerInterceptor.java:320) ~[?:?]
	at com.sun.jmx.mbeanserver.JmxMBeanServer.registerMBean(JmxMBeanServer.java:523) ~[?:?]
	at org.apache.kafka.common.utils.AppInfoParser.registerAppInfo(AppInfoParser.java:64) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.consumer.KafkaConsumer.<init>(KafkaConsumer.java:821) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.consumer.KafkaConsumer.<init>(KafkaConsumer.java:665) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.consumer.KafkaConsumer.<init>(KafkaConsumer.java:646) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.consumer.KafkaConsumer.<init>(KafkaConsumer.java:626) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.flink.connector.kafka.source.reader.KafkaPartitionSplitReader.<init>(KafkaPartitionSplitReader.java:97) ~[flink-connector-kafka-3.4.0-1.20.jar:3.4.0-1.20]
	at org.apache.flink.connector.kafka.source.KafkaSource.lambda$createReader$1(KafkaSource.java:185) ~[flink-connector-kafka-3.4.0-1.20.jar:3.4.0-1.20]
	at org.apache.flink.connector.base.source.reader.fetcher.SplitFetcherManager.createSplitFetcher(SplitFetcherManager.java:259) ~[flink-connector-base-1.20.0.jar:1.20.0]
	at org.apache.flink.connector.base.source.reader.fetcher.SingleThreadFetcherManager.addSplits(SingleThreadFetcherManager.java:148) ~[flink-connector-base-1.20.0.jar:1.20.0]
	at org.apache.flink.connector.base.source.reader.SourceReaderBase.addSplits(SourceReaderBase.java:315) ~[flink-connector-base-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.api.operators.SourceOperator.handleAddSplitsEvent(SourceOperator.java:606) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.api.operators.SourceOperator.handleOperatorEvent(SourceOperator.java:575) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.OperatorEventDispatcherImpl.dispatchEventToHandlers(OperatorEventDispatcherImpl.java:72) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.RegularOperatorChain.dispatchOperatorEvent(RegularOperatorChain.java:80) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.StreamTask.lambda$dispatchOperatorEvent$24(StreamTask.java:1609) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.StreamTaskActionExecutor$1.runThrowing(StreamTaskActionExecutor.java:50) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.mailbox.Mail.run(Mail.java:101) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.mailbox.MailboxProcessor.runMail(MailboxProcessor.java:414) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.mailbox.MailboxProcessor.processMailsWhenDefaultActionUnavailable(MailboxProcessor.java:383) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.mailbox.MailboxProcessor.processMail(MailboxProcessor.java:368) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.mailbox.MailboxProcessor.runMailboxLoop(MailboxProcessor.java:229) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.StreamTask.runMailboxLoop(StreamTask.java:973) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.StreamTask.invoke(StreamTask.java:917) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.runtime.taskmanager.Task.runWithSystemExitMonitoring(Task.java:970) [flink-runtime-1.20.0.jar:1.20.0]
	at org.apache.flink.runtime.taskmanager.Task.restoreAndInvoke(Task.java:949) [flink-runtime-1.20.0.jar:1.20.0]
	at org.apache.flink.runtime.taskmanager.Task.doRun(Task.java:763) [flink-runtime-1.20.0.jar:1.20.0]
	at org.apache.flink.runtime.taskmanager.Task.run(Task.java:575) [flink-runtime-1.20.0.jar:1.20.0]
	at java.lang.Thread.run(Thread.java:840) [?:?]
2025-08-20 14:47:44.907 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] WARN  org.apache.kafka.common.utils.AppInfoParser - Error registering AppInfo mbean
javax.management.InstanceAlreadyExistsException: kafka.consumer:type=app-info,id=orderbook-local-test-group-0
	at com.sun.jmx.mbeanserver.Repository.addMBean(Repository.java:436) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerWithRepository(DefaultMBeanServerInterceptor.java:1865) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerDynamicMBean(DefaultMBeanServerInterceptor.java:960) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerObject(DefaultMBeanServerInterceptor.java:895) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerMBean(DefaultMBeanServerInterceptor.java:320) ~[?:?]
	at com.sun.jmx.mbeanserver.JmxMBeanServer.registerMBean(JmxMBeanServer.java:523) ~[?:?]
	at org.apache.kafka.common.utils.AppInfoParser.registerAppInfo(AppInfoParser.java:64) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.consumer.KafkaConsumer.<init>(KafkaConsumer.java:821) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.consumer.KafkaConsumer.<init>(KafkaConsumer.java:665) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.consumer.KafkaConsumer.<init>(KafkaConsumer.java:646) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.consumer.KafkaConsumer.<init>(KafkaConsumer.java:626) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.flink.connector.kafka.source.reader.KafkaPartitionSplitReader.<init>(KafkaPartitionSplitReader.java:97) ~[flink-connector-kafka-3.4.0-1.20.jar:3.4.0-1.20]
	at org.apache.flink.connector.kafka.source.KafkaSource.lambda$createReader$1(KafkaSource.java:185) ~[flink-connector-kafka-3.4.0-1.20.jar:3.4.0-1.20]
	at org.apache.flink.connector.base.source.reader.fetcher.SplitFetcherManager.createSplitFetcher(SplitFetcherManager.java:259) ~[flink-connector-base-1.20.0.jar:1.20.0]
	at org.apache.flink.connector.base.source.reader.fetcher.SingleThreadFetcherManager.addSplits(SingleThreadFetcherManager.java:148) ~[flink-connector-base-1.20.0.jar:1.20.0]
	at org.apache.flink.connector.base.source.reader.SourceReaderBase.addSplits(SourceReaderBase.java:315) ~[flink-connector-base-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.api.operators.SourceOperator.handleAddSplitsEvent(SourceOperator.java:606) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.api.operators.SourceOperator.handleOperatorEvent(SourceOperator.java:575) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.OperatorEventDispatcherImpl.dispatchEventToHandlers(OperatorEventDispatcherImpl.java:72) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.RegularOperatorChain.dispatchOperatorEvent(RegularOperatorChain.java:80) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.StreamTask.lambda$dispatchOperatorEvent$24(StreamTask.java:1609) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.StreamTaskActionExecutor$1.runThrowing(StreamTaskActionExecutor.java:50) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.mailbox.Mail.run(Mail.java:101) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.mailbox.MailboxProcessor.runMail(MailboxProcessor.java:414) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.mailbox.MailboxProcessor.processMailsWhenDefaultActionUnavailable(MailboxProcessor.java:383) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.mailbox.MailboxProcessor.processMail(MailboxProcessor.java:368) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.mailbox.MailboxProcessor.runMailboxLoop(MailboxProcessor.java:229) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.StreamTask.runMailboxLoop(StreamTask.java:973) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.StreamTask.invoke(StreamTask.java:917) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.runtime.taskmanager.Task.runWithSystemExitMonitoring(Task.java:970) [flink-runtime-1.20.0.jar:1.20.0]
	at org.apache.flink.runtime.taskmanager.Task.restoreAndInvoke(Task.java:949) [flink-runtime-1.20.0.jar:1.20.0]
	at org.apache.flink.runtime.taskmanager.Task.doRun(Task.java:763) [flink-runtime-1.20.0.jar:1.20.0]
	at org.apache.flink.runtime.taskmanager.Task.run(Task.java:575) [flink-runtime-1.20.0.jar:1.20.0]
	at java.lang.Thread.run(Thread.java:840) [?:?]
2025-08-20 15:15:03.319 [main] WARN  org.apache.flink.connector.kafka.source.KafkaSourceBuilder - Property auto.offset.reset is provided but will be overridden from earliest to earliest
2025-08-20 15:15:03.336 [main] WARN  org.apache.flink.connector.kafka.source.KafkaSourceBuilder - Property auto.offset.reset is provided but will be overridden from earliest to earliest
2025-08-20 15:15:03.337 [main] WARN  org.apache.flink.connector.kafka.source.KafkaSourceBuilder - Property auto.offset.reset is provided but will be overridden from earliest to earliest
2025-08-20 15:15:04.062 [SourceCoordinator-Source: Kafka-交易] WARN  org.apache.kafka.clients.admin.AdminClientConfig - These configurations '[key.deserializer, value.deserializer, max.poll.records, client.id.prefix, group.id, partition.discovery.interval.ms, heartbeat.interval.ms, enable.auto.commit, fetch.max.wait.ms, fetch.min.bytes, session.timeout.ms, auto.offset.reset]' were supplied but are not used yet.
2025-08-20 15:15:04.062 [SourceCoordinator-Source: Kafka-单腿订单] WARN  org.apache.kafka.clients.admin.AdminClientConfig - These configurations '[key.deserializer, value.deserializer, max.poll.records, client.id.prefix, group.id, partition.discovery.interval.ms, heartbeat.interval.ms, enable.auto.commit, fetch.max.wait.ms, fetch.min.bytes, session.timeout.ms, auto.offset.reset]' were supplied but are not used yet.
2025-08-20 15:15:04.062 [SourceCoordinator-Source: Kafka-组合订单] WARN  org.apache.kafka.clients.admin.AdminClientConfig - These configurations '[key.deserializer, value.deserializer, max.poll.records, client.id.prefix, group.id, partition.discovery.interval.ms, heartbeat.interval.ms, enable.auto.commit, fetch.max.wait.ms, fetch.min.bytes, session.timeout.ms, auto.offset.reset]' were supplied but are not used yet.
2025-08-20 15:15:04.063 [SourceCoordinator-Source: Kafka-组合订单] INFO  org.apache.flink.connector.kafka.source.enumerator.KafkaSourceEnumerator - Starting the KafkaSourceEnumerator for consumer group orderbook-local-test-group without periodic partition discovery.
2025-08-20 15:15:04.063 [SourceCoordinator-Source: Kafka-交易] WARN  org.apache.kafka.common.utils.AppInfoParser - Error registering AppInfo mbean
javax.management.InstanceAlreadyExistsException: kafka.admin.client:type=app-info,id=orderbook-local-test-group-enumerator-admin-client
	at com.sun.jmx.mbeanserver.Repository.addMBean(Repository.java:436) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerWithRepository(DefaultMBeanServerInterceptor.java:1865) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerDynamicMBean(DefaultMBeanServerInterceptor.java:960) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerObject(DefaultMBeanServerInterceptor.java:895) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerMBean(DefaultMBeanServerInterceptor.java:320) ~[?:?]
	at com.sun.jmx.mbeanserver.JmxMBeanServer.registerMBean(JmxMBeanServer.java:523) ~[?:?]
	at org.apache.kafka.common.utils.AppInfoParser.registerAppInfo(AppInfoParser.java:64) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.admin.KafkaAdminClient.<init>(KafkaAdminClient.java:602) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.admin.KafkaAdminClient.createInternal(KafkaAdminClient.java:544) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.admin.KafkaAdminClient.createInternal(KafkaAdminClient.java:488) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.admin.Admin.create(Admin.java:134) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.admin.AdminClient.create(AdminClient.java:39) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.flink.connector.kafka.source.enumerator.KafkaSourceEnumerator.getKafkaAdminClient(KafkaSourceEnumerator.java:441) ~[flink-connector-kafka-3.4.0-1.20.jar:3.4.0-1.20]
	at org.apache.flink.connector.kafka.source.enumerator.KafkaSourceEnumerator.start(KafkaSourceEnumerator.java:164) ~[flink-connector-kafka-3.4.0-1.20.jar:3.4.0-1.20]
	at org.apache.flink.runtime.source.coordinator.SourceCoordinator.lambda$start$1(SourceCoordinator.java:244) ~[flink-runtime-1.20.0.jar:1.20.0]
	at org.apache.flink.runtime.source.coordinator.SourceCoordinator.lambda$runInEventLoop$10(SourceCoordinator.java:533) ~[flink-runtime-1.20.0.jar:1.20.0]
	at org.apache.flink.util.ThrowableCatchingRunnable.run(ThrowableCatchingRunnable.java:40) [flink-core-1.20.0.jar:1.20.0]
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:539) [?:?]
	at java.util.concurrent.FutureTask.run(FutureTask.java:264) [?:?]
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:304) [?:?]
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136) [?:?]
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635) [?:?]
	at java.lang.Thread.run(Thread.java:840) [?:?]
2025-08-20 15:15:04.066 [SourceCoordinator-Source: Kafka-交易] INFO  org.apache.flink.connector.kafka.source.enumerator.KafkaSourceEnumerator - Starting the KafkaSourceEnumerator for consumer group orderbook-local-test-group without periodic partition discovery.
2025-08-20 15:15:04.066 [SourceCoordinator-Source: Kafka-单腿订单] WARN  org.apache.kafka.common.utils.AppInfoParser - Error registering AppInfo mbean
javax.management.InstanceAlreadyExistsException: kafka.admin.client:type=app-info,id=orderbook-local-test-group-enumerator-admin-client
	at com.sun.jmx.mbeanserver.Repository.addMBean(Repository.java:436) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerWithRepository(DefaultMBeanServerInterceptor.java:1865) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerDynamicMBean(DefaultMBeanServerInterceptor.java:960) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerObject(DefaultMBeanServerInterceptor.java:895) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerMBean(DefaultMBeanServerInterceptor.java:320) ~[?:?]
	at com.sun.jmx.mbeanserver.JmxMBeanServer.registerMBean(JmxMBeanServer.java:523) ~[?:?]
	at org.apache.kafka.common.utils.AppInfoParser.registerAppInfo(AppInfoParser.java:64) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.admin.KafkaAdminClient.<init>(KafkaAdminClient.java:602) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.admin.KafkaAdminClient.createInternal(KafkaAdminClient.java:544) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.admin.KafkaAdminClient.createInternal(KafkaAdminClient.java:488) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.admin.Admin.create(Admin.java:134) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.admin.AdminClient.create(AdminClient.java:39) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.flink.connector.kafka.source.enumerator.KafkaSourceEnumerator.getKafkaAdminClient(KafkaSourceEnumerator.java:441) ~[flink-connector-kafka-3.4.0-1.20.jar:3.4.0-1.20]
	at org.apache.flink.connector.kafka.source.enumerator.KafkaSourceEnumerator.start(KafkaSourceEnumerator.java:164) ~[flink-connector-kafka-3.4.0-1.20.jar:3.4.0-1.20]
	at org.apache.flink.runtime.source.coordinator.SourceCoordinator.lambda$start$1(SourceCoordinator.java:244) ~[flink-runtime-1.20.0.jar:1.20.0]
	at org.apache.flink.runtime.source.coordinator.SourceCoordinator.lambda$runInEventLoop$10(SourceCoordinator.java:533) ~[flink-runtime-1.20.0.jar:1.20.0]
	at org.apache.flink.util.ThrowableCatchingRunnable.run(ThrowableCatchingRunnable.java:40) [flink-core-1.20.0.jar:1.20.0]
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:539) [?:?]
	at java.util.concurrent.FutureTask.run(FutureTask.java:264) [?:?]
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:304) [?:?]
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136) [?:?]
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635) [?:?]
	at java.lang.Thread.run(Thread.java:840) [?:?]
2025-08-20 15:15:04.066 [SourceCoordinator-Source: Kafka-单腿订单] INFO  org.apache.flink.connector.kafka.source.enumerator.KafkaSourceEnumerator - Starting the KafkaSourceEnumerator for consumer group orderbook-local-test-group without periodic partition discovery.
2025-08-20 15:15:04.184 [SourceCoordinator-Source: Kafka-组合订单] INFO  org.apache.flink.connector.kafka.source.enumerator.KafkaSourceEnumerator - Discovered new partitions: [cmb_order_data_event-0]
2025-08-20 15:15:04.184 [SourceCoordinator-Source: Kafka-交易] INFO  org.apache.flink.connector.kafka.source.enumerator.KafkaSourceEnumerator - Discovered new partitions: [trade_data_event-0]
2025-08-20 15:15:04.184 [SourceCoordinator-Source: Kafka-单腿订单] INFO  org.apache.flink.connector.kafka.source.enumerator.KafkaSourceEnumerator - Discovered new partitions: [singleleg_order_data_event-0]
2025-08-20 15:15:04.208 [SourceCoordinator-Source: Kafka-组合订单] INFO  org.apache.flink.connector.kafka.source.enumerator.KafkaSourceEnumerator - Assigning splits to readers {0=[[Partition: cmb_order_data_event-0, StartingOffset: -2, StoppingOffset: -9223372036854775808]]}
2025-08-20 15:15:04.208 [SourceCoordinator-Source: Kafka-交易] INFO  org.apache.flink.connector.kafka.source.enumerator.KafkaSourceEnumerator - Assigning splits to readers {0=[[Partition: trade_data_event-0, StartingOffset: -2, StoppingOffset: -9223372036854775808]]}
2025-08-20 15:15:04.208 [SourceCoordinator-Source: Kafka-单腿订单] INFO  org.apache.flink.connector.kafka.source.enumerator.KafkaSourceEnumerator - Assigning splits to readers {0=[[Partition: singleleg_order_data_event-0, StartingOffset: -2, StoppingOffset: -9223372036854775808]]}
2025-08-20 15:15:04.239 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] WARN  org.apache.kafka.clients.consumer.ConsumerConfig - These configurations '[client.id.prefix, partition.discovery.interval.ms]' were supplied but are not used yet.
2025-08-20 15:15:04.239 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] WARN  org.apache.kafka.clients.consumer.ConsumerConfig - These configurations '[client.id.prefix, partition.discovery.interval.ms]' were supplied but are not used yet.
2025-08-20 15:15:04.239 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] WARN  org.apache.kafka.clients.consumer.ConsumerConfig - These configurations '[client.id.prefix, partition.discovery.interval.ms]' were supplied but are not used yet.
2025-08-20 15:15:04.240 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] WARN  org.apache.kafka.common.utils.AppInfoParser - Error registering AppInfo mbean
javax.management.InstanceAlreadyExistsException: kafka.consumer:type=app-info,id=orderbook-local-test-group-0
	at com.sun.jmx.mbeanserver.Repository.addMBean(Repository.java:436) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerWithRepository(DefaultMBeanServerInterceptor.java:1865) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerDynamicMBean(DefaultMBeanServerInterceptor.java:960) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerObject(DefaultMBeanServerInterceptor.java:895) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerMBean(DefaultMBeanServerInterceptor.java:320) ~[?:?]
	at com.sun.jmx.mbeanserver.JmxMBeanServer.registerMBean(JmxMBeanServer.java:523) ~[?:?]
	at org.apache.kafka.common.utils.AppInfoParser.registerAppInfo(AppInfoParser.java:64) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.consumer.KafkaConsumer.<init>(KafkaConsumer.java:821) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.consumer.KafkaConsumer.<init>(KafkaConsumer.java:665) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.consumer.KafkaConsumer.<init>(KafkaConsumer.java:646) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.consumer.KafkaConsumer.<init>(KafkaConsumer.java:626) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.flink.connector.kafka.source.reader.KafkaPartitionSplitReader.<init>(KafkaPartitionSplitReader.java:97) ~[flink-connector-kafka-3.4.0-1.20.jar:3.4.0-1.20]
	at org.apache.flink.connector.kafka.source.KafkaSource.lambda$createReader$1(KafkaSource.java:185) ~[flink-connector-kafka-3.4.0-1.20.jar:3.4.0-1.20]
	at org.apache.flink.connector.base.source.reader.fetcher.SplitFetcherManager.createSplitFetcher(SplitFetcherManager.java:259) ~[flink-connector-base-1.20.0.jar:1.20.0]
	at org.apache.flink.connector.base.source.reader.fetcher.SingleThreadFetcherManager.addSplits(SingleThreadFetcherManager.java:148) ~[flink-connector-base-1.20.0.jar:1.20.0]
	at org.apache.flink.connector.base.source.reader.SourceReaderBase.addSplits(SourceReaderBase.java:315) ~[flink-connector-base-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.api.operators.SourceOperator.handleAddSplitsEvent(SourceOperator.java:606) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.api.operators.SourceOperator.handleOperatorEvent(SourceOperator.java:575) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.OperatorEventDispatcherImpl.dispatchEventToHandlers(OperatorEventDispatcherImpl.java:72) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.RegularOperatorChain.dispatchOperatorEvent(RegularOperatorChain.java:80) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.StreamTask.lambda$dispatchOperatorEvent$24(StreamTask.java:1609) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.StreamTaskActionExecutor$1.runThrowing(StreamTaskActionExecutor.java:50) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.mailbox.Mail.run(Mail.java:101) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.mailbox.MailboxProcessor.runMail(MailboxProcessor.java:414) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.mailbox.MailboxProcessor.processMailsWhenDefaultActionUnavailable(MailboxProcessor.java:383) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.mailbox.MailboxProcessor.processMail(MailboxProcessor.java:368) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.mailbox.MailboxProcessor.runMailboxLoop(MailboxProcessor.java:229) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.StreamTask.runMailboxLoop(StreamTask.java:973) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.StreamTask.invoke(StreamTask.java:917) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.runtime.taskmanager.Task.runWithSystemExitMonitoring(Task.java:970) [flink-runtime-1.20.0.jar:1.20.0]
	at org.apache.flink.runtime.taskmanager.Task.restoreAndInvoke(Task.java:949) [flink-runtime-1.20.0.jar:1.20.0]
	at org.apache.flink.runtime.taskmanager.Task.doRun(Task.java:763) [flink-runtime-1.20.0.jar:1.20.0]
	at org.apache.flink.runtime.taskmanager.Task.run(Task.java:575) [flink-runtime-1.20.0.jar:1.20.0]
	at java.lang.Thread.run(Thread.java:840) [?:?]
2025-08-20 15:15:04.240 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] WARN  org.apache.kafka.common.utils.AppInfoParser - Error registering AppInfo mbean
javax.management.InstanceAlreadyExistsException: kafka.consumer:type=app-info,id=orderbook-local-test-group-0
	at com.sun.jmx.mbeanserver.Repository.addMBean(Repository.java:436) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerWithRepository(DefaultMBeanServerInterceptor.java:1865) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerDynamicMBean(DefaultMBeanServerInterceptor.java:960) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerObject(DefaultMBeanServerInterceptor.java:895) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerMBean(DefaultMBeanServerInterceptor.java:320) ~[?:?]
	at com.sun.jmx.mbeanserver.JmxMBeanServer.registerMBean(JmxMBeanServer.java:523) ~[?:?]
	at org.apache.kafka.common.utils.AppInfoParser.registerAppInfo(AppInfoParser.java:64) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.consumer.KafkaConsumer.<init>(KafkaConsumer.java:821) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.consumer.KafkaConsumer.<init>(KafkaConsumer.java:665) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.consumer.KafkaConsumer.<init>(KafkaConsumer.java:646) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.consumer.KafkaConsumer.<init>(KafkaConsumer.java:626) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.flink.connector.kafka.source.reader.KafkaPartitionSplitReader.<init>(KafkaPartitionSplitReader.java:97) ~[flink-connector-kafka-3.4.0-1.20.jar:3.4.0-1.20]
	at org.apache.flink.connector.kafka.source.KafkaSource.lambda$createReader$1(KafkaSource.java:185) ~[flink-connector-kafka-3.4.0-1.20.jar:3.4.0-1.20]
	at org.apache.flink.connector.base.source.reader.fetcher.SplitFetcherManager.createSplitFetcher(SplitFetcherManager.java:259) ~[flink-connector-base-1.20.0.jar:1.20.0]
	at org.apache.flink.connector.base.source.reader.fetcher.SingleThreadFetcherManager.addSplits(SingleThreadFetcherManager.java:148) ~[flink-connector-base-1.20.0.jar:1.20.0]
	at org.apache.flink.connector.base.source.reader.SourceReaderBase.addSplits(SourceReaderBase.java:315) ~[flink-connector-base-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.api.operators.SourceOperator.handleAddSplitsEvent(SourceOperator.java:606) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.api.operators.SourceOperator.handleOperatorEvent(SourceOperator.java:575) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.OperatorEventDispatcherImpl.dispatchEventToHandlers(OperatorEventDispatcherImpl.java:72) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.RegularOperatorChain.dispatchOperatorEvent(RegularOperatorChain.java:80) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.StreamTask.lambda$dispatchOperatorEvent$24(StreamTask.java:1609) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.StreamTaskActionExecutor$1.runThrowing(StreamTaskActionExecutor.java:50) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.mailbox.Mail.run(Mail.java:101) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.mailbox.MailboxProcessor.runMail(MailboxProcessor.java:414) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.mailbox.MailboxProcessor.processMailsWhenDefaultActionUnavailable(MailboxProcessor.java:383) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.mailbox.MailboxProcessor.processMail(MailboxProcessor.java:368) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.mailbox.MailboxProcessor.runMailboxLoop(MailboxProcessor.java:229) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.StreamTask.runMailboxLoop(StreamTask.java:973) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.StreamTask.invoke(StreamTask.java:917) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.runtime.taskmanager.Task.runWithSystemExitMonitoring(Task.java:970) [flink-runtime-1.20.0.jar:1.20.0]
	at org.apache.flink.runtime.taskmanager.Task.restoreAndInvoke(Task.java:949) [flink-runtime-1.20.0.jar:1.20.0]
	at org.apache.flink.runtime.taskmanager.Task.doRun(Task.java:763) [flink-runtime-1.20.0.jar:1.20.0]
	at org.apache.flink.runtime.taskmanager.Task.run(Task.java:575) [flink-runtime-1.20.0.jar:1.20.0]
	at java.lang.Thread.run(Thread.java:840) [?:?]
2025-08-20 15:17:23.899 [main] WARN  org.apache.flink.connector.kafka.source.KafkaSourceBuilder - Property auto.offset.reset is provided but will be overridden from earliest to earliest
2025-08-20 15:17:23.917 [main] WARN  org.apache.flink.connector.kafka.source.KafkaSourceBuilder - Property auto.offset.reset is provided but will be overridden from earliest to earliest
2025-08-20 15:17:23.918 [main] WARN  org.apache.flink.connector.kafka.source.KafkaSourceBuilder - Property auto.offset.reset is provided but will be overridden from earliest to earliest
2025-08-20 15:17:24.664 [SourceCoordinator-Source: Kafka-组合订单] WARN  org.apache.kafka.clients.admin.AdminClientConfig - These configurations '[key.deserializer, value.deserializer, max.poll.records, client.id.prefix, group.id, partition.discovery.interval.ms, heartbeat.interval.ms, enable.auto.commit, fetch.max.wait.ms, fetch.min.bytes, session.timeout.ms, auto.offset.reset]' were supplied but are not used yet.
2025-08-20 15:17:24.664 [SourceCoordinator-Source: Kafka-单腿订单] WARN  org.apache.kafka.clients.admin.AdminClientConfig - These configurations '[key.deserializer, value.deserializer, max.poll.records, client.id.prefix, group.id, partition.discovery.interval.ms, heartbeat.interval.ms, enable.auto.commit, fetch.max.wait.ms, fetch.min.bytes, session.timeout.ms, auto.offset.reset]' were supplied but are not used yet.
2025-08-20 15:17:24.664 [SourceCoordinator-Source: Kafka-交易] WARN  org.apache.kafka.clients.admin.AdminClientConfig - These configurations '[key.deserializer, value.deserializer, max.poll.records, client.id.prefix, group.id, partition.discovery.interval.ms, heartbeat.interval.ms, enable.auto.commit, fetch.max.wait.ms, fetch.min.bytes, session.timeout.ms, auto.offset.reset]' were supplied but are not used yet.
2025-08-20 15:17:24.665 [SourceCoordinator-Source: Kafka-组合订单] INFO  org.apache.flink.connector.kafka.source.enumerator.KafkaSourceEnumerator - Starting the KafkaSourceEnumerator for consumer group orderbook-local-test-group without periodic partition discovery.
2025-08-20 15:17:24.665 [SourceCoordinator-Source: Kafka-单腿订单] WARN  org.apache.kafka.common.utils.AppInfoParser - Error registering AppInfo mbean
javax.management.InstanceAlreadyExistsException: kafka.admin.client:type=app-info,id=orderbook-local-test-group-enumerator-admin-client
	at com.sun.jmx.mbeanserver.Repository.addMBean(Repository.java:436) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerWithRepository(DefaultMBeanServerInterceptor.java:1865) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerDynamicMBean(DefaultMBeanServerInterceptor.java:960) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerObject(DefaultMBeanServerInterceptor.java:895) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerMBean(DefaultMBeanServerInterceptor.java:320) ~[?:?]
	at com.sun.jmx.mbeanserver.JmxMBeanServer.registerMBean(JmxMBeanServer.java:523) ~[?:?]
	at org.apache.kafka.common.utils.AppInfoParser.registerAppInfo(AppInfoParser.java:64) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.admin.KafkaAdminClient.<init>(KafkaAdminClient.java:602) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.admin.KafkaAdminClient.createInternal(KafkaAdminClient.java:544) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.admin.KafkaAdminClient.createInternal(KafkaAdminClient.java:488) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.admin.Admin.create(Admin.java:134) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.admin.AdminClient.create(AdminClient.java:39) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.flink.connector.kafka.source.enumerator.KafkaSourceEnumerator.getKafkaAdminClient(KafkaSourceEnumerator.java:441) ~[flink-connector-kafka-3.4.0-1.20.jar:3.4.0-1.20]
	at org.apache.flink.connector.kafka.source.enumerator.KafkaSourceEnumerator.start(KafkaSourceEnumerator.java:164) ~[flink-connector-kafka-3.4.0-1.20.jar:3.4.0-1.20]
	at org.apache.flink.runtime.source.coordinator.SourceCoordinator.lambda$start$1(SourceCoordinator.java:244) ~[flink-runtime-1.20.0.jar:1.20.0]
	at org.apache.flink.runtime.source.coordinator.SourceCoordinator.lambda$runInEventLoop$10(SourceCoordinator.java:533) ~[flink-runtime-1.20.0.jar:1.20.0]
	at org.apache.flink.util.ThrowableCatchingRunnable.run(ThrowableCatchingRunnable.java:40) [flink-core-1.20.0.jar:1.20.0]
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:539) [?:?]
	at java.util.concurrent.FutureTask.run(FutureTask.java:264) [?:?]
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:304) [?:?]
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136) [?:?]
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635) [?:?]
	at java.lang.Thread.run(Thread.java:840) [?:?]
2025-08-20 15:17:24.668 [SourceCoordinator-Source: Kafka-单腿订单] INFO  org.apache.flink.connector.kafka.source.enumerator.KafkaSourceEnumerator - Starting the KafkaSourceEnumerator for consumer group orderbook-local-test-group without periodic partition discovery.
2025-08-20 15:17:24.668 [SourceCoordinator-Source: Kafka-交易] WARN  org.apache.kafka.common.utils.AppInfoParser - Error registering AppInfo mbean
javax.management.InstanceAlreadyExistsException: kafka.admin.client:type=app-info,id=orderbook-local-test-group-enumerator-admin-client
	at com.sun.jmx.mbeanserver.Repository.addMBean(Repository.java:436) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerWithRepository(DefaultMBeanServerInterceptor.java:1865) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerDynamicMBean(DefaultMBeanServerInterceptor.java:960) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerObject(DefaultMBeanServerInterceptor.java:895) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerMBean(DefaultMBeanServerInterceptor.java:320) ~[?:?]
	at com.sun.jmx.mbeanserver.JmxMBeanServer.registerMBean(JmxMBeanServer.java:523) ~[?:?]
	at org.apache.kafka.common.utils.AppInfoParser.registerAppInfo(AppInfoParser.java:64) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.admin.KafkaAdminClient.<init>(KafkaAdminClient.java:602) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.admin.KafkaAdminClient.createInternal(KafkaAdminClient.java:544) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.admin.KafkaAdminClient.createInternal(KafkaAdminClient.java:488) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.admin.Admin.create(Admin.java:134) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.admin.AdminClient.create(AdminClient.java:39) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.flink.connector.kafka.source.enumerator.KafkaSourceEnumerator.getKafkaAdminClient(KafkaSourceEnumerator.java:441) ~[flink-connector-kafka-3.4.0-1.20.jar:3.4.0-1.20]
	at org.apache.flink.connector.kafka.source.enumerator.KafkaSourceEnumerator.start(KafkaSourceEnumerator.java:164) ~[flink-connector-kafka-3.4.0-1.20.jar:3.4.0-1.20]
	at org.apache.flink.runtime.source.coordinator.SourceCoordinator.lambda$start$1(SourceCoordinator.java:244) ~[flink-runtime-1.20.0.jar:1.20.0]
	at org.apache.flink.runtime.source.coordinator.SourceCoordinator.lambda$runInEventLoop$10(SourceCoordinator.java:533) ~[flink-runtime-1.20.0.jar:1.20.0]
	at org.apache.flink.util.ThrowableCatchingRunnable.run(ThrowableCatchingRunnable.java:40) [flink-core-1.20.0.jar:1.20.0]
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:539) [?:?]
	at java.util.concurrent.FutureTask.run(FutureTask.java:264) [?:?]
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:304) [?:?]
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136) [?:?]
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635) [?:?]
	at java.lang.Thread.run(Thread.java:840) [?:?]
2025-08-20 15:17:24.668 [SourceCoordinator-Source: Kafka-交易] INFO  org.apache.flink.connector.kafka.source.enumerator.KafkaSourceEnumerator - Starting the KafkaSourceEnumerator for consumer group orderbook-local-test-group without periodic partition discovery.
2025-08-20 15:17:24.772 [SourceCoordinator-Source: Kafka-交易] INFO  org.apache.flink.connector.kafka.source.enumerator.KafkaSourceEnumerator - Discovered new partitions: [trade_data_event-0]
2025-08-20 15:17:24.772 [SourceCoordinator-Source: Kafka-组合订单] INFO  org.apache.flink.connector.kafka.source.enumerator.KafkaSourceEnumerator - Discovered new partitions: [cmb_order_data_event-0]
2025-08-20 15:17:24.772 [SourceCoordinator-Source: Kafka-单腿订单] INFO  org.apache.flink.connector.kafka.source.enumerator.KafkaSourceEnumerator - Discovered new partitions: [singleleg_order_data_event-0]
2025-08-20 15:17:24.790 [SourceCoordinator-Source: Kafka-单腿订单] INFO  org.apache.flink.connector.kafka.source.enumerator.KafkaSourceEnumerator - Assigning splits to readers {0=[[Partition: singleleg_order_data_event-0, StartingOffset: -2, StoppingOffset: -9223372036854775808]]}
2025-08-20 15:17:24.790 [SourceCoordinator-Source: Kafka-交易] INFO  org.apache.flink.connector.kafka.source.enumerator.KafkaSourceEnumerator - Assigning splits to readers {0=[[Partition: trade_data_event-0, StartingOffset: -2, StoppingOffset: -9223372036854775808]]}
2025-08-20 15:17:24.790 [SourceCoordinator-Source: Kafka-组合订单] INFO  org.apache.flink.connector.kafka.source.enumerator.KafkaSourceEnumerator - Assigning splits to readers {0=[[Partition: cmb_order_data_event-0, StartingOffset: -2, StoppingOffset: -9223372036854775808]]}
2025-08-20 15:17:24.816 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] WARN  org.apache.kafka.clients.consumer.ConsumerConfig - These configurations '[client.id.prefix, partition.discovery.interval.ms]' were supplied but are not used yet.
2025-08-20 15:17:24.816 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] WARN  org.apache.kafka.clients.consumer.ConsumerConfig - These configurations '[client.id.prefix, partition.discovery.interval.ms]' were supplied but are not used yet.
2025-08-20 15:17:24.816 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] WARN  org.apache.kafka.clients.consumer.ConsumerConfig - These configurations '[client.id.prefix, partition.discovery.interval.ms]' were supplied but are not used yet.
2025-08-20 15:17:24.817 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] WARN  org.apache.kafka.common.utils.AppInfoParser - Error registering AppInfo mbean
javax.management.InstanceAlreadyExistsException: kafka.consumer:type=app-info,id=orderbook-local-test-group-0
	at com.sun.jmx.mbeanserver.Repository.addMBean(Repository.java:436) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerWithRepository(DefaultMBeanServerInterceptor.java:1865) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerDynamicMBean(DefaultMBeanServerInterceptor.java:960) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerObject(DefaultMBeanServerInterceptor.java:895) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerMBean(DefaultMBeanServerInterceptor.java:320) ~[?:?]
	at com.sun.jmx.mbeanserver.JmxMBeanServer.registerMBean(JmxMBeanServer.java:523) ~[?:?]
	at org.apache.kafka.common.utils.AppInfoParser.registerAppInfo(AppInfoParser.java:64) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.consumer.KafkaConsumer.<init>(KafkaConsumer.java:821) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.consumer.KafkaConsumer.<init>(KafkaConsumer.java:665) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.consumer.KafkaConsumer.<init>(KafkaConsumer.java:646) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.consumer.KafkaConsumer.<init>(KafkaConsumer.java:626) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.flink.connector.kafka.source.reader.KafkaPartitionSplitReader.<init>(KafkaPartitionSplitReader.java:97) ~[flink-connector-kafka-3.4.0-1.20.jar:3.4.0-1.20]
	at org.apache.flink.connector.kafka.source.KafkaSource.lambda$createReader$1(KafkaSource.java:185) ~[flink-connector-kafka-3.4.0-1.20.jar:3.4.0-1.20]
	at org.apache.flink.connector.base.source.reader.fetcher.SplitFetcherManager.createSplitFetcher(SplitFetcherManager.java:259) ~[flink-connector-base-1.20.0.jar:1.20.0]
	at org.apache.flink.connector.base.source.reader.fetcher.SingleThreadFetcherManager.addSplits(SingleThreadFetcherManager.java:148) ~[flink-connector-base-1.20.0.jar:1.20.0]
	at org.apache.flink.connector.base.source.reader.SourceReaderBase.addSplits(SourceReaderBase.java:315) ~[flink-connector-base-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.api.operators.SourceOperator.handleAddSplitsEvent(SourceOperator.java:606) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.api.operators.SourceOperator.handleOperatorEvent(SourceOperator.java:575) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.OperatorEventDispatcherImpl.dispatchEventToHandlers(OperatorEventDispatcherImpl.java:72) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.RegularOperatorChain.dispatchOperatorEvent(RegularOperatorChain.java:80) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.StreamTask.lambda$dispatchOperatorEvent$24(StreamTask.java:1609) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.StreamTaskActionExecutor$1.runThrowing(StreamTaskActionExecutor.java:50) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.mailbox.Mail.run(Mail.java:101) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.mailbox.MailboxProcessor.runMail(MailboxProcessor.java:414) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.mailbox.MailboxProcessor.processMailsWhenDefaultActionUnavailable(MailboxProcessor.java:383) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.mailbox.MailboxProcessor.processMail(MailboxProcessor.java:368) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.mailbox.MailboxProcessor.runMailboxLoop(MailboxProcessor.java:229) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.StreamTask.runMailboxLoop(StreamTask.java:973) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.StreamTask.invoke(StreamTask.java:917) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.runtime.taskmanager.Task.runWithSystemExitMonitoring(Task.java:970) [flink-runtime-1.20.0.jar:1.20.0]
	at org.apache.flink.runtime.taskmanager.Task.restoreAndInvoke(Task.java:949) [flink-runtime-1.20.0.jar:1.20.0]
	at org.apache.flink.runtime.taskmanager.Task.doRun(Task.java:763) [flink-runtime-1.20.0.jar:1.20.0]
	at org.apache.flink.runtime.taskmanager.Task.run(Task.java:575) [flink-runtime-1.20.0.jar:1.20.0]
	at java.lang.Thread.run(Thread.java:840) [?:?]
2025-08-20 15:17:24.817 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] WARN  org.apache.kafka.common.utils.AppInfoParser - Error registering AppInfo mbean
javax.management.InstanceAlreadyExistsException: kafka.consumer:type=app-info,id=orderbook-local-test-group-0
	at com.sun.jmx.mbeanserver.Repository.addMBean(Repository.java:436) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerWithRepository(DefaultMBeanServerInterceptor.java:1865) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerDynamicMBean(DefaultMBeanServerInterceptor.java:960) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerObject(DefaultMBeanServerInterceptor.java:895) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerMBean(DefaultMBeanServerInterceptor.java:320) ~[?:?]
	at com.sun.jmx.mbeanserver.JmxMBeanServer.registerMBean(JmxMBeanServer.java:523) ~[?:?]
	at org.apache.kafka.common.utils.AppInfoParser.registerAppInfo(AppInfoParser.java:64) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.consumer.KafkaConsumer.<init>(KafkaConsumer.java:821) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.consumer.KafkaConsumer.<init>(KafkaConsumer.java:665) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.consumer.KafkaConsumer.<init>(KafkaConsumer.java:646) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.consumer.KafkaConsumer.<init>(KafkaConsumer.java:626) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.flink.connector.kafka.source.reader.KafkaPartitionSplitReader.<init>(KafkaPartitionSplitReader.java:97) ~[flink-connector-kafka-3.4.0-1.20.jar:3.4.0-1.20]
	at org.apache.flink.connector.kafka.source.KafkaSource.lambda$createReader$1(KafkaSource.java:185) ~[flink-connector-kafka-3.4.0-1.20.jar:3.4.0-1.20]
	at org.apache.flink.connector.base.source.reader.fetcher.SplitFetcherManager.createSplitFetcher(SplitFetcherManager.java:259) ~[flink-connector-base-1.20.0.jar:1.20.0]
	at org.apache.flink.connector.base.source.reader.fetcher.SingleThreadFetcherManager.addSplits(SingleThreadFetcherManager.java:148) ~[flink-connector-base-1.20.0.jar:1.20.0]
	at org.apache.flink.connector.base.source.reader.SourceReaderBase.addSplits(SourceReaderBase.java:315) ~[flink-connector-base-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.api.operators.SourceOperator.handleAddSplitsEvent(SourceOperator.java:606) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.api.operators.SourceOperator.handleOperatorEvent(SourceOperator.java:575) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.OperatorEventDispatcherImpl.dispatchEventToHandlers(OperatorEventDispatcherImpl.java:72) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.RegularOperatorChain.dispatchOperatorEvent(RegularOperatorChain.java:80) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.StreamTask.lambda$dispatchOperatorEvent$24(StreamTask.java:1609) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.StreamTaskActionExecutor$1.runThrowing(StreamTaskActionExecutor.java:50) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.mailbox.Mail.run(Mail.java:101) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.mailbox.MailboxProcessor.runMail(MailboxProcessor.java:414) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.mailbox.MailboxProcessor.processMailsWhenDefaultActionUnavailable(MailboxProcessor.java:383) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.mailbox.MailboxProcessor.processMail(MailboxProcessor.java:368) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.mailbox.MailboxProcessor.runMailboxLoop(MailboxProcessor.java:229) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.StreamTask.runMailboxLoop(StreamTask.java:973) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.StreamTask.invoke(StreamTask.java:917) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.runtime.taskmanager.Task.runWithSystemExitMonitoring(Task.java:970) [flink-runtime-1.20.0.jar:1.20.0]
	at org.apache.flink.runtime.taskmanager.Task.restoreAndInvoke(Task.java:949) [flink-runtime-1.20.0.jar:1.20.0]
	at org.apache.flink.runtime.taskmanager.Task.doRun(Task.java:763) [flink-runtime-1.20.0.jar:1.20.0]
	at org.apache.flink.runtime.taskmanager.Task.run(Task.java:575) [flink-runtime-1.20.0.jar:1.20.0]
	at java.lang.Thread.run(Thread.java:840) [?:?]
2025-08-20 15:17:59.545 [main] WARN  org.apache.flink.connector.kafka.source.KafkaSourceBuilder - Property auto.offset.reset is provided but will be overridden from earliest to earliest
2025-08-20 15:17:59.560 [main] WARN  org.apache.flink.connector.kafka.source.KafkaSourceBuilder - Property auto.offset.reset is provided but will be overridden from earliest to earliest
2025-08-20 15:17:59.561 [main] WARN  org.apache.flink.connector.kafka.source.KafkaSourceBuilder - Property auto.offset.reset is provided but will be overridden from earliest to earliest
2025-08-20 15:18:00.241 [SourceCoordinator-Source: Kafka-组合订单] WARN  org.apache.kafka.clients.admin.AdminClientConfig - These configurations '[key.deserializer, value.deserializer, max.poll.records, client.id.prefix, group.id, partition.discovery.interval.ms, heartbeat.interval.ms, enable.auto.commit, fetch.max.wait.ms, fetch.min.bytes, session.timeout.ms, auto.offset.reset]' were supplied but are not used yet.
2025-08-20 15:18:00.241 [SourceCoordinator-Source: Kafka-交易] WARN  org.apache.kafka.clients.admin.AdminClientConfig - These configurations '[key.deserializer, value.deserializer, max.poll.records, client.id.prefix, group.id, partition.discovery.interval.ms, heartbeat.interval.ms, enable.auto.commit, fetch.max.wait.ms, fetch.min.bytes, session.timeout.ms, auto.offset.reset]' were supplied but are not used yet.
2025-08-20 15:18:00.241 [SourceCoordinator-Source: Kafka-单腿订单] WARN  org.apache.kafka.clients.admin.AdminClientConfig - These configurations '[key.deserializer, value.deserializer, max.poll.records, client.id.prefix, group.id, partition.discovery.interval.ms, heartbeat.interval.ms, enable.auto.commit, fetch.max.wait.ms, fetch.min.bytes, session.timeout.ms, auto.offset.reset]' were supplied but are not used yet.
2025-08-20 15:18:00.243 [SourceCoordinator-Source: Kafka-组合订单] INFO  org.apache.flink.connector.kafka.source.enumerator.KafkaSourceEnumerator - Starting the KafkaSourceEnumerator for consumer group orderbook-local-test-group without periodic partition discovery.
2025-08-20 15:18:00.243 [SourceCoordinator-Source: Kafka-单腿订单] WARN  org.apache.kafka.common.utils.AppInfoParser - Error registering AppInfo mbean
javax.management.InstanceAlreadyExistsException: kafka.admin.client:type=app-info,id=orderbook-local-test-group-enumerator-admin-client
	at com.sun.jmx.mbeanserver.Repository.addMBean(Repository.java:436) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerWithRepository(DefaultMBeanServerInterceptor.java:1865) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerDynamicMBean(DefaultMBeanServerInterceptor.java:960) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerObject(DefaultMBeanServerInterceptor.java:895) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerMBean(DefaultMBeanServerInterceptor.java:320) ~[?:?]
	at com.sun.jmx.mbeanserver.JmxMBeanServer.registerMBean(JmxMBeanServer.java:523) ~[?:?]
	at org.apache.kafka.common.utils.AppInfoParser.registerAppInfo(AppInfoParser.java:64) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.admin.KafkaAdminClient.<init>(KafkaAdminClient.java:602) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.admin.KafkaAdminClient.createInternal(KafkaAdminClient.java:544) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.admin.KafkaAdminClient.createInternal(KafkaAdminClient.java:488) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.admin.Admin.create(Admin.java:134) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.admin.AdminClient.create(AdminClient.java:39) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.flink.connector.kafka.source.enumerator.KafkaSourceEnumerator.getKafkaAdminClient(KafkaSourceEnumerator.java:441) ~[flink-connector-kafka-3.4.0-1.20.jar:3.4.0-1.20]
	at org.apache.flink.connector.kafka.source.enumerator.KafkaSourceEnumerator.start(KafkaSourceEnumerator.java:164) ~[flink-connector-kafka-3.4.0-1.20.jar:3.4.0-1.20]
	at org.apache.flink.runtime.source.coordinator.SourceCoordinator.lambda$start$1(SourceCoordinator.java:244) ~[flink-runtime-1.20.0.jar:1.20.0]
	at org.apache.flink.runtime.source.coordinator.SourceCoordinator.lambda$runInEventLoop$10(SourceCoordinator.java:533) ~[flink-runtime-1.20.0.jar:1.20.0]
	at org.apache.flink.util.ThrowableCatchingRunnable.run(ThrowableCatchingRunnable.java:40) [flink-core-1.20.0.jar:1.20.0]
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:539) [?:?]
	at java.util.concurrent.FutureTask.run(FutureTask.java:264) [?:?]
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:304) [?:?]
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136) [?:?]
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635) [?:?]
	at java.lang.Thread.run(Thread.java:840) [?:?]
2025-08-20 15:18:00.245 [SourceCoordinator-Source: Kafka-单腿订单] INFO  org.apache.flink.connector.kafka.source.enumerator.KafkaSourceEnumerator - Starting the KafkaSourceEnumerator for consumer group orderbook-local-test-group without periodic partition discovery.
2025-08-20 15:18:00.245 [SourceCoordinator-Source: Kafka-交易] WARN  org.apache.kafka.common.utils.AppInfoParser - Error registering AppInfo mbean
javax.management.InstanceAlreadyExistsException: kafka.admin.client:type=app-info,id=orderbook-local-test-group-enumerator-admin-client
	at com.sun.jmx.mbeanserver.Repository.addMBean(Repository.java:436) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerWithRepository(DefaultMBeanServerInterceptor.java:1865) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerDynamicMBean(DefaultMBeanServerInterceptor.java:960) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerObject(DefaultMBeanServerInterceptor.java:895) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerMBean(DefaultMBeanServerInterceptor.java:320) ~[?:?]
	at com.sun.jmx.mbeanserver.JmxMBeanServer.registerMBean(JmxMBeanServer.java:523) ~[?:?]
	at org.apache.kafka.common.utils.AppInfoParser.registerAppInfo(AppInfoParser.java:64) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.admin.KafkaAdminClient.<init>(KafkaAdminClient.java:602) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.admin.KafkaAdminClient.createInternal(KafkaAdminClient.java:544) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.admin.KafkaAdminClient.createInternal(KafkaAdminClient.java:488) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.admin.Admin.create(Admin.java:134) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.admin.AdminClient.create(AdminClient.java:39) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.flink.connector.kafka.source.enumerator.KafkaSourceEnumerator.getKafkaAdminClient(KafkaSourceEnumerator.java:441) ~[flink-connector-kafka-3.4.0-1.20.jar:3.4.0-1.20]
	at org.apache.flink.connector.kafka.source.enumerator.KafkaSourceEnumerator.start(KafkaSourceEnumerator.java:164) ~[flink-connector-kafka-3.4.0-1.20.jar:3.4.0-1.20]
	at org.apache.flink.runtime.source.coordinator.SourceCoordinator.lambda$start$1(SourceCoordinator.java:244) ~[flink-runtime-1.20.0.jar:1.20.0]
	at org.apache.flink.runtime.source.coordinator.SourceCoordinator.lambda$runInEventLoop$10(SourceCoordinator.java:533) ~[flink-runtime-1.20.0.jar:1.20.0]
	at org.apache.flink.util.ThrowableCatchingRunnable.run(ThrowableCatchingRunnable.java:40) [flink-core-1.20.0.jar:1.20.0]
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:539) [?:?]
	at java.util.concurrent.FutureTask.run(FutureTask.java:264) [?:?]
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:304) [?:?]
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136) [?:?]
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635) [?:?]
	at java.lang.Thread.run(Thread.java:840) [?:?]
2025-08-20 15:18:00.245 [SourceCoordinator-Source: Kafka-交易] INFO  org.apache.flink.connector.kafka.source.enumerator.KafkaSourceEnumerator - Starting the KafkaSourceEnumerator for consumer group orderbook-local-test-group without periodic partition discovery.
2025-08-20 15:18:00.347 [SourceCoordinator-Source: Kafka-组合订单] INFO  org.apache.flink.connector.kafka.source.enumerator.KafkaSourceEnumerator - Discovered new partitions: [cmb_order_data_event-0]
2025-08-20 15:18:00.347 [SourceCoordinator-Source: Kafka-单腿订单] INFO  org.apache.flink.connector.kafka.source.enumerator.KafkaSourceEnumerator - Discovered new partitions: [singleleg_order_data_event-0]
2025-08-20 15:18:00.347 [SourceCoordinator-Source: Kafka-交易] INFO  org.apache.flink.connector.kafka.source.enumerator.KafkaSourceEnumerator - Discovered new partitions: [trade_data_event-0]
2025-08-20 15:18:00.369 [SourceCoordinator-Source: Kafka-单腿订单] INFO  org.apache.flink.connector.kafka.source.enumerator.KafkaSourceEnumerator - Assigning splits to readers {0=[[Partition: singleleg_order_data_event-0, StartingOffset: -2, StoppingOffset: -9223372036854775808]]}
2025-08-20 15:18:00.369 [SourceCoordinator-Source: Kafka-交易] INFO  org.apache.flink.connector.kafka.source.enumerator.KafkaSourceEnumerator - Assigning splits to readers {0=[[Partition: trade_data_event-0, StartingOffset: -2, StoppingOffset: -9223372036854775808]]}
2025-08-20 15:18:00.369 [SourceCoordinator-Source: Kafka-组合订单] INFO  org.apache.flink.connector.kafka.source.enumerator.KafkaSourceEnumerator - Assigning splits to readers {0=[[Partition: cmb_order_data_event-0, StartingOffset: -2, StoppingOffset: -9223372036854775808]]}
2025-08-20 15:18:00.398 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] WARN  org.apache.kafka.clients.consumer.ConsumerConfig - These configurations '[client.id.prefix, partition.discovery.interval.ms]' were supplied but are not used yet.
2025-08-20 15:18:00.398 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] WARN  org.apache.kafka.clients.consumer.ConsumerConfig - These configurations '[client.id.prefix, partition.discovery.interval.ms]' were supplied but are not used yet.
2025-08-20 15:18:00.398 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] WARN  org.apache.kafka.clients.consumer.ConsumerConfig - These configurations '[client.id.prefix, partition.discovery.interval.ms]' were supplied but are not used yet.
2025-08-20 15:18:00.399 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] WARN  org.apache.kafka.common.utils.AppInfoParser - Error registering AppInfo mbean
javax.management.InstanceAlreadyExistsException: kafka.consumer:type=app-info,id=orderbook-local-test-group-0
	at com.sun.jmx.mbeanserver.Repository.addMBean(Repository.java:436) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerWithRepository(DefaultMBeanServerInterceptor.java:1865) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerDynamicMBean(DefaultMBeanServerInterceptor.java:960) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerObject(DefaultMBeanServerInterceptor.java:895) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerMBean(DefaultMBeanServerInterceptor.java:320) ~[?:?]
	at com.sun.jmx.mbeanserver.JmxMBeanServer.registerMBean(JmxMBeanServer.java:523) ~[?:?]
	at org.apache.kafka.common.utils.AppInfoParser.registerAppInfo(AppInfoParser.java:64) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.consumer.KafkaConsumer.<init>(KafkaConsumer.java:821) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.consumer.KafkaConsumer.<init>(KafkaConsumer.java:665) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.consumer.KafkaConsumer.<init>(KafkaConsumer.java:646) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.consumer.KafkaConsumer.<init>(KafkaConsumer.java:626) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.flink.connector.kafka.source.reader.KafkaPartitionSplitReader.<init>(KafkaPartitionSplitReader.java:97) ~[flink-connector-kafka-3.4.0-1.20.jar:3.4.0-1.20]
	at org.apache.flink.connector.kafka.source.KafkaSource.lambda$createReader$1(KafkaSource.java:185) ~[flink-connector-kafka-3.4.0-1.20.jar:3.4.0-1.20]
	at org.apache.flink.connector.base.source.reader.fetcher.SplitFetcherManager.createSplitFetcher(SplitFetcherManager.java:259) ~[flink-connector-base-1.20.0.jar:1.20.0]
	at org.apache.flink.connector.base.source.reader.fetcher.SingleThreadFetcherManager.addSplits(SingleThreadFetcherManager.java:148) ~[flink-connector-base-1.20.0.jar:1.20.0]
	at org.apache.flink.connector.base.source.reader.SourceReaderBase.addSplits(SourceReaderBase.java:315) ~[flink-connector-base-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.api.operators.SourceOperator.handleAddSplitsEvent(SourceOperator.java:606) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.api.operators.SourceOperator.handleOperatorEvent(SourceOperator.java:575) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.OperatorEventDispatcherImpl.dispatchEventToHandlers(OperatorEventDispatcherImpl.java:72) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.RegularOperatorChain.dispatchOperatorEvent(RegularOperatorChain.java:80) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.StreamTask.lambda$dispatchOperatorEvent$24(StreamTask.java:1609) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.StreamTaskActionExecutor$1.runThrowing(StreamTaskActionExecutor.java:50) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.mailbox.Mail.run(Mail.java:101) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.mailbox.MailboxProcessor.runMail(MailboxProcessor.java:414) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.mailbox.MailboxProcessor.processMailsWhenDefaultActionUnavailable(MailboxProcessor.java:383) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.mailbox.MailboxProcessor.processMail(MailboxProcessor.java:368) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.mailbox.MailboxProcessor.runMailboxLoop(MailboxProcessor.java:229) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.StreamTask.runMailboxLoop(StreamTask.java:973) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.StreamTask.invoke(StreamTask.java:917) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.runtime.taskmanager.Task.runWithSystemExitMonitoring(Task.java:970) [flink-runtime-1.20.0.jar:1.20.0]
	at org.apache.flink.runtime.taskmanager.Task.restoreAndInvoke(Task.java:949) [flink-runtime-1.20.0.jar:1.20.0]
	at org.apache.flink.runtime.taskmanager.Task.doRun(Task.java:763) [flink-runtime-1.20.0.jar:1.20.0]
	at org.apache.flink.runtime.taskmanager.Task.run(Task.java:575) [flink-runtime-1.20.0.jar:1.20.0]
	at java.lang.Thread.run(Thread.java:840) [?:?]
2025-08-20 15:18:00.399 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] WARN  org.apache.kafka.common.utils.AppInfoParser - Error registering AppInfo mbean
javax.management.InstanceAlreadyExistsException: kafka.consumer:type=app-info,id=orderbook-local-test-group-0
	at com.sun.jmx.mbeanserver.Repository.addMBean(Repository.java:436) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerWithRepository(DefaultMBeanServerInterceptor.java:1865) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerDynamicMBean(DefaultMBeanServerInterceptor.java:960) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerObject(DefaultMBeanServerInterceptor.java:895) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerMBean(DefaultMBeanServerInterceptor.java:320) ~[?:?]
	at com.sun.jmx.mbeanserver.JmxMBeanServer.registerMBean(JmxMBeanServer.java:523) ~[?:?]
	at org.apache.kafka.common.utils.AppInfoParser.registerAppInfo(AppInfoParser.java:64) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.consumer.KafkaConsumer.<init>(KafkaConsumer.java:821) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.consumer.KafkaConsumer.<init>(KafkaConsumer.java:665) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.consumer.KafkaConsumer.<init>(KafkaConsumer.java:646) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.consumer.KafkaConsumer.<init>(KafkaConsumer.java:626) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.flink.connector.kafka.source.reader.KafkaPartitionSplitReader.<init>(KafkaPartitionSplitReader.java:97) ~[flink-connector-kafka-3.4.0-1.20.jar:3.4.0-1.20]
	at org.apache.flink.connector.kafka.source.KafkaSource.lambda$createReader$1(KafkaSource.java:185) ~[flink-connector-kafka-3.4.0-1.20.jar:3.4.0-1.20]
	at org.apache.flink.connector.base.source.reader.fetcher.SplitFetcherManager.createSplitFetcher(SplitFetcherManager.java:259) ~[flink-connector-base-1.20.0.jar:1.20.0]
	at org.apache.flink.connector.base.source.reader.fetcher.SingleThreadFetcherManager.addSplits(SingleThreadFetcherManager.java:148) ~[flink-connector-base-1.20.0.jar:1.20.0]
	at org.apache.flink.connector.base.source.reader.SourceReaderBase.addSplits(SourceReaderBase.java:315) ~[flink-connector-base-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.api.operators.SourceOperator.handleAddSplitsEvent(SourceOperator.java:606) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.api.operators.SourceOperator.handleOperatorEvent(SourceOperator.java:575) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.OperatorEventDispatcherImpl.dispatchEventToHandlers(OperatorEventDispatcherImpl.java:72) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.RegularOperatorChain.dispatchOperatorEvent(RegularOperatorChain.java:80) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.StreamTask.lambda$dispatchOperatorEvent$24(StreamTask.java:1609) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.StreamTaskActionExecutor$1.runThrowing(StreamTaskActionExecutor.java:50) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.mailbox.Mail.run(Mail.java:101) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.mailbox.MailboxProcessor.runMail(MailboxProcessor.java:414) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.mailbox.MailboxProcessor.processMailsWhenDefaultActionUnavailable(MailboxProcessor.java:383) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.mailbox.MailboxProcessor.processMail(MailboxProcessor.java:368) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.mailbox.MailboxProcessor.runMailboxLoop(MailboxProcessor.java:229) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.StreamTask.runMailboxLoop(StreamTask.java:973) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.StreamTask.invoke(StreamTask.java:917) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.runtime.taskmanager.Task.runWithSystemExitMonitoring(Task.java:970) [flink-runtime-1.20.0.jar:1.20.0]
	at org.apache.flink.runtime.taskmanager.Task.restoreAndInvoke(Task.java:949) [flink-runtime-1.20.0.jar:1.20.0]
	at org.apache.flink.runtime.taskmanager.Task.doRun(Task.java:763) [flink-runtime-1.20.0.jar:1.20.0]
	at org.apache.flink.runtime.taskmanager.Task.run(Task.java:575) [flink-runtime-1.20.0.jar:1.20.0]
	at java.lang.Thread.run(Thread.java:840) [?:?]
2025-08-20 15:20:09.618 [SourceCoordinator-Source: Kafka-组合订单] WARN  org.apache.kafka.clients.admin.AdminClientConfig - These configurations '[key.deserializer, value.deserializer, enable.auto.commit, client.id.prefix, group.id, partition.discovery.interval.ms, auto.offset.reset]' were supplied but are not used yet.
2025-08-20 15:20:09.618 [SourceCoordinator-Source: Kafka-单腿订单] WARN  org.apache.kafka.clients.admin.AdminClientConfig - These configurations '[key.deserializer, value.deserializer, enable.auto.commit, client.id.prefix, group.id, partition.discovery.interval.ms, auto.offset.reset]' were supplied but are not used yet.
2025-08-20 15:20:09.618 [SourceCoordinator-Source: Kafka-交易] WARN  org.apache.kafka.clients.admin.AdminClientConfig - These configurations '[key.deserializer, value.deserializer, enable.auto.commit, client.id.prefix, group.id, partition.discovery.interval.ms, auto.offset.reset]' were supplied but are not used yet.
2025-08-20 15:20:09.620 [SourceCoordinator-Source: Kafka-单腿订单] INFO  org.apache.flink.connector.kafka.source.enumerator.KafkaSourceEnumerator - Starting the KafkaSourceEnumerator for consumer group orderbook-debug-group without periodic partition discovery.
2025-08-20 15:20:09.620 [SourceCoordinator-Source: Kafka-交易] WARN  org.apache.kafka.common.utils.AppInfoParser - Error registering AppInfo mbean
javax.management.InstanceAlreadyExistsException: kafka.admin.client:type=app-info,id=orderbook-debug-group-enumerator-admin-client
	at com.sun.jmx.mbeanserver.Repository.addMBean(Repository.java:436) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerWithRepository(DefaultMBeanServerInterceptor.java:1865) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerDynamicMBean(DefaultMBeanServerInterceptor.java:960) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerObject(DefaultMBeanServerInterceptor.java:895) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerMBean(DefaultMBeanServerInterceptor.java:320) ~[?:?]
	at com.sun.jmx.mbeanserver.JmxMBeanServer.registerMBean(JmxMBeanServer.java:523) ~[?:?]
	at org.apache.kafka.common.utils.AppInfoParser.registerAppInfo(AppInfoParser.java:64) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.admin.KafkaAdminClient.<init>(KafkaAdminClient.java:602) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.admin.KafkaAdminClient.createInternal(KafkaAdminClient.java:544) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.admin.KafkaAdminClient.createInternal(KafkaAdminClient.java:488) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.admin.Admin.create(Admin.java:134) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.admin.AdminClient.create(AdminClient.java:39) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.flink.connector.kafka.source.enumerator.KafkaSourceEnumerator.getKafkaAdminClient(KafkaSourceEnumerator.java:441) ~[flink-connector-kafka-3.4.0-1.20.jar:3.4.0-1.20]
	at org.apache.flink.connector.kafka.source.enumerator.KafkaSourceEnumerator.start(KafkaSourceEnumerator.java:164) ~[flink-connector-kafka-3.4.0-1.20.jar:3.4.0-1.20]
	at org.apache.flink.runtime.source.coordinator.SourceCoordinator.lambda$start$1(SourceCoordinator.java:244) ~[flink-runtime-1.20.0.jar:1.20.0]
	at org.apache.flink.runtime.source.coordinator.SourceCoordinator.lambda$runInEventLoop$10(SourceCoordinator.java:533) ~[flink-runtime-1.20.0.jar:1.20.0]
	at org.apache.flink.util.ThrowableCatchingRunnable.run(ThrowableCatchingRunnable.java:40) [flink-core-1.20.0.jar:1.20.0]
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:539) [?:?]
	at java.util.concurrent.FutureTask.run(FutureTask.java:264) [?:?]
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:304) [?:?]
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136) [?:?]
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635) [?:?]
	at java.lang.Thread.run(Thread.java:840) [?:?]
2025-08-20 15:20:09.623 [SourceCoordinator-Source: Kafka-交易] INFO  org.apache.flink.connector.kafka.source.enumerator.KafkaSourceEnumerator - Starting the KafkaSourceEnumerator for consumer group orderbook-debug-group without periodic partition discovery.
2025-08-20 15:20:09.623 [SourceCoordinator-Source: Kafka-组合订单] WARN  org.apache.kafka.common.utils.AppInfoParser - Error registering AppInfo mbean
javax.management.InstanceAlreadyExistsException: kafka.admin.client:type=app-info,id=orderbook-debug-group-enumerator-admin-client
	at com.sun.jmx.mbeanserver.Repository.addMBean(Repository.java:436) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerWithRepository(DefaultMBeanServerInterceptor.java:1865) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerDynamicMBean(DefaultMBeanServerInterceptor.java:960) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerObject(DefaultMBeanServerInterceptor.java:895) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerMBean(DefaultMBeanServerInterceptor.java:320) ~[?:?]
	at com.sun.jmx.mbeanserver.JmxMBeanServer.registerMBean(JmxMBeanServer.java:523) ~[?:?]
	at org.apache.kafka.common.utils.AppInfoParser.registerAppInfo(AppInfoParser.java:64) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.admin.KafkaAdminClient.<init>(KafkaAdminClient.java:602) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.admin.KafkaAdminClient.createInternal(KafkaAdminClient.java:544) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.admin.KafkaAdminClient.createInternal(KafkaAdminClient.java:488) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.admin.Admin.create(Admin.java:134) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.admin.AdminClient.create(AdminClient.java:39) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.flink.connector.kafka.source.enumerator.KafkaSourceEnumerator.getKafkaAdminClient(KafkaSourceEnumerator.java:441) ~[flink-connector-kafka-3.4.0-1.20.jar:3.4.0-1.20]
	at org.apache.flink.connector.kafka.source.enumerator.KafkaSourceEnumerator.start(KafkaSourceEnumerator.java:164) ~[flink-connector-kafka-3.4.0-1.20.jar:3.4.0-1.20]
	at org.apache.flink.runtime.source.coordinator.SourceCoordinator.lambda$start$1(SourceCoordinator.java:244) ~[flink-runtime-1.20.0.jar:1.20.0]
	at org.apache.flink.runtime.source.coordinator.SourceCoordinator.lambda$runInEventLoop$10(SourceCoordinator.java:533) ~[flink-runtime-1.20.0.jar:1.20.0]
	at org.apache.flink.util.ThrowableCatchingRunnable.run(ThrowableCatchingRunnable.java:40) [flink-core-1.20.0.jar:1.20.0]
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:539) [?:?]
	at java.util.concurrent.FutureTask.run(FutureTask.java:264) [?:?]
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:304) [?:?]
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136) [?:?]
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635) [?:?]
	at java.lang.Thread.run(Thread.java:840) [?:?]
2025-08-20 15:20:09.624 [SourceCoordinator-Source: Kafka-组合订单] INFO  org.apache.flink.connector.kafka.source.enumerator.KafkaSourceEnumerator - Starting the KafkaSourceEnumerator for consumer group orderbook-debug-group without periodic partition discovery.
2025-08-20 15:24:09.526 [SourceCoordinator-Source: Kafka-组合订单] WARN  org.apache.kafka.clients.admin.AdminClientConfig - These configurations '[key.deserializer, value.deserializer, enable.auto.commit, client.id.prefix, group.id, partition.discovery.interval.ms, auto.offset.reset]' were supplied but are not used yet.
2025-08-20 15:24:09.526 [SourceCoordinator-Source: Kafka-单腿订单] WARN  org.apache.kafka.clients.admin.AdminClientConfig - These configurations '[key.deserializer, value.deserializer, enable.auto.commit, client.id.prefix, group.id, partition.discovery.interval.ms, auto.offset.reset]' were supplied but are not used yet.
2025-08-20 15:24:09.526 [SourceCoordinator-Source: Kafka-交易] WARN  org.apache.kafka.clients.admin.AdminClientConfig - These configurations '[key.deserializer, value.deserializer, enable.auto.commit, client.id.prefix, group.id, partition.discovery.interval.ms, auto.offset.reset]' were supplied but are not used yet.
2025-08-20 15:24:09.528 [SourceCoordinator-Source: Kafka-组合订单] INFO  org.apache.flink.connector.kafka.source.enumerator.KafkaSourceEnumerator - Starting the KafkaSourceEnumerator for consumer group orderbook-debug-group without periodic partition discovery.
2025-08-20 15:24:09.528 [SourceCoordinator-Source: Kafka-交易] WARN  org.apache.kafka.common.utils.AppInfoParser - Error registering AppInfo mbean
javax.management.InstanceAlreadyExistsException: kafka.admin.client:type=app-info,id=orderbook-debug-group-enumerator-admin-client
	at com.sun.jmx.mbeanserver.Repository.addMBean(Repository.java:436) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerWithRepository(DefaultMBeanServerInterceptor.java:1865) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerDynamicMBean(DefaultMBeanServerInterceptor.java:960) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerObject(DefaultMBeanServerInterceptor.java:895) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerMBean(DefaultMBeanServerInterceptor.java:320) ~[?:?]
	at com.sun.jmx.mbeanserver.JmxMBeanServer.registerMBean(JmxMBeanServer.java:523) ~[?:?]
	at org.apache.kafka.common.utils.AppInfoParser.registerAppInfo(AppInfoParser.java:64) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.admin.KafkaAdminClient.<init>(KafkaAdminClient.java:602) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.admin.KafkaAdminClient.createInternal(KafkaAdminClient.java:544) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.admin.KafkaAdminClient.createInternal(KafkaAdminClient.java:488) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.admin.Admin.create(Admin.java:134) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.admin.AdminClient.create(AdminClient.java:39) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.flink.connector.kafka.source.enumerator.KafkaSourceEnumerator.getKafkaAdminClient(KafkaSourceEnumerator.java:441) ~[flink-connector-kafka-3.4.0-1.20.jar:3.4.0-1.20]
	at org.apache.flink.connector.kafka.source.enumerator.KafkaSourceEnumerator.start(KafkaSourceEnumerator.java:164) ~[flink-connector-kafka-3.4.0-1.20.jar:3.4.0-1.20]
	at org.apache.flink.runtime.source.coordinator.SourceCoordinator.lambda$start$1(SourceCoordinator.java:244) ~[flink-runtime-1.20.0.jar:1.20.0]
	at org.apache.flink.runtime.source.coordinator.SourceCoordinator.lambda$runInEventLoop$10(SourceCoordinator.java:533) ~[flink-runtime-1.20.0.jar:1.20.0]
	at org.apache.flink.util.ThrowableCatchingRunnable.run(ThrowableCatchingRunnable.java:40) [flink-core-1.20.0.jar:1.20.0]
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:539) [?:?]
	at java.util.concurrent.FutureTask.run(FutureTask.java:264) [?:?]
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:304) [?:?]
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136) [?:?]
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635) [?:?]
	at java.lang.Thread.run(Thread.java:840) [?:?]
2025-08-20 15:24:09.530 [SourceCoordinator-Source: Kafka-交易] INFO  org.apache.flink.connector.kafka.source.enumerator.KafkaSourceEnumerator - Starting the KafkaSourceEnumerator for consumer group orderbook-debug-group without periodic partition discovery.
2025-08-20 15:24:09.530 [SourceCoordinator-Source: Kafka-单腿订单] WARN  org.apache.kafka.common.utils.AppInfoParser - Error registering AppInfo mbean
javax.management.InstanceAlreadyExistsException: kafka.admin.client:type=app-info,id=orderbook-debug-group-enumerator-admin-client
	at com.sun.jmx.mbeanserver.Repository.addMBean(Repository.java:436) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerWithRepository(DefaultMBeanServerInterceptor.java:1865) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerDynamicMBean(DefaultMBeanServerInterceptor.java:960) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerObject(DefaultMBeanServerInterceptor.java:895) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerMBean(DefaultMBeanServerInterceptor.java:320) ~[?:?]
	at com.sun.jmx.mbeanserver.JmxMBeanServer.registerMBean(JmxMBeanServer.java:523) ~[?:?]
	at org.apache.kafka.common.utils.AppInfoParser.registerAppInfo(AppInfoParser.java:64) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.admin.KafkaAdminClient.<init>(KafkaAdminClient.java:602) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.admin.KafkaAdminClient.createInternal(KafkaAdminClient.java:544) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.admin.KafkaAdminClient.createInternal(KafkaAdminClient.java:488) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.admin.Admin.create(Admin.java:134) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.admin.AdminClient.create(AdminClient.java:39) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.flink.connector.kafka.source.enumerator.KafkaSourceEnumerator.getKafkaAdminClient(KafkaSourceEnumerator.java:441) ~[flink-connector-kafka-3.4.0-1.20.jar:3.4.0-1.20]
	at org.apache.flink.connector.kafka.source.enumerator.KafkaSourceEnumerator.start(KafkaSourceEnumerator.java:164) ~[flink-connector-kafka-3.4.0-1.20.jar:3.4.0-1.20]
	at org.apache.flink.runtime.source.coordinator.SourceCoordinator.lambda$start$1(SourceCoordinator.java:244) ~[flink-runtime-1.20.0.jar:1.20.0]
	at org.apache.flink.runtime.source.coordinator.SourceCoordinator.lambda$runInEventLoop$10(SourceCoordinator.java:533) ~[flink-runtime-1.20.0.jar:1.20.0]
	at org.apache.flink.util.ThrowableCatchingRunnable.run(ThrowableCatchingRunnable.java:40) [flink-core-1.20.0.jar:1.20.0]
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:539) [?:?]
	at java.util.concurrent.FutureTask.run(FutureTask.java:264) [?:?]
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:304) [?:?]
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136) [?:?]
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635) [?:?]
	at java.lang.Thread.run(Thread.java:840) [?:?]
2025-08-20 15:24:09.530 [SourceCoordinator-Source: Kafka-单腿订单] INFO  org.apache.flink.connector.kafka.source.enumerator.KafkaSourceEnumerator - Starting the KafkaSourceEnumerator for consumer group orderbook-debug-group without periodic partition discovery.
2025-08-20 15:24:09.639 [SourceCoordinator-Source: Kafka-单腿订单] INFO  org.apache.flink.connector.kafka.source.enumerator.KafkaSourceEnumerator - Discovered new partitions: [singleleg_order_data_event-0]
2025-08-20 15:24:09.639 [SourceCoordinator-Source: Kafka-交易] INFO  org.apache.flink.connector.kafka.source.enumerator.KafkaSourceEnumerator - Discovered new partitions: [trade_data_event-0]
2025-08-20 15:24:09.639 [SourceCoordinator-Source: Kafka-组合订单] INFO  org.apache.flink.connector.kafka.source.enumerator.KafkaSourceEnumerator - Discovered new partitions: [cmb_order_data_event-0]
2025-08-20 15:24:09.650 [SourceCoordinator-Source: Kafka-交易] INFO  org.apache.flink.connector.kafka.source.enumerator.KafkaSourceEnumerator - Assigning splits to readers {0=[[Partition: trade_data_event-0, StartingOffset: -2, StoppingOffset: -9223372036854775808]]}
2025-08-20 15:24:09.650 [SourceCoordinator-Source: Kafka-组合订单] INFO  org.apache.flink.connector.kafka.source.enumerator.KafkaSourceEnumerator - Assigning splits to readers {0=[[Partition: cmb_order_data_event-0, StartingOffset: -2, StoppingOffset: -9223372036854775808]]}
2025-08-20 15:24:09.650 [SourceCoordinator-Source: Kafka-单腿订单] INFO  org.apache.flink.connector.kafka.source.enumerator.KafkaSourceEnumerator - Assigning splits to readers {0=[[Partition: singleleg_order_data_event-0, StartingOffset: -2, StoppingOffset: -9223372036854775808]]}
2025-08-20 15:24:09.679 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> 拆分组合订单 -> (Map, Map) (1/1)#0] WARN  org.apache.kafka.clients.consumer.ConsumerConfig - These configurations '[client.id.prefix, partition.discovery.interval.ms]' were supplied but are not used yet.
2025-08-20 15:24:09.679 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] WARN  org.apache.kafka.clients.consumer.ConsumerConfig - These configurations '[client.id.prefix, partition.discovery.interval.ms]' were supplied but are not used yet.
2025-08-20 15:24:09.680 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] WARN  org.apache.kafka.clients.consumer.ConsumerConfig - These configurations '[client.id.prefix, partition.discovery.interval.ms]' were supplied but are not used yet.
2025-08-20 15:24:09.680 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] WARN  org.apache.kafka.common.utils.AppInfoParser - Error registering AppInfo mbean
javax.management.InstanceAlreadyExistsException: kafka.consumer:type=app-info,id=orderbook-debug-group-0
	at com.sun.jmx.mbeanserver.Repository.addMBean(Repository.java:436) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerWithRepository(DefaultMBeanServerInterceptor.java:1865) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerDynamicMBean(DefaultMBeanServerInterceptor.java:960) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerObject(DefaultMBeanServerInterceptor.java:895) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerMBean(DefaultMBeanServerInterceptor.java:320) ~[?:?]
	at com.sun.jmx.mbeanserver.JmxMBeanServer.registerMBean(JmxMBeanServer.java:523) ~[?:?]
	at org.apache.kafka.common.utils.AppInfoParser.registerAppInfo(AppInfoParser.java:64) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.consumer.KafkaConsumer.<init>(KafkaConsumer.java:821) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.consumer.KafkaConsumer.<init>(KafkaConsumer.java:665) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.consumer.KafkaConsumer.<init>(KafkaConsumer.java:646) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.consumer.KafkaConsumer.<init>(KafkaConsumer.java:626) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.flink.connector.kafka.source.reader.KafkaPartitionSplitReader.<init>(KafkaPartitionSplitReader.java:97) ~[flink-connector-kafka-3.4.0-1.20.jar:3.4.0-1.20]
	at org.apache.flink.connector.kafka.source.KafkaSource.lambda$createReader$1(KafkaSource.java:185) ~[flink-connector-kafka-3.4.0-1.20.jar:3.4.0-1.20]
	at org.apache.flink.connector.base.source.reader.fetcher.SplitFetcherManager.createSplitFetcher(SplitFetcherManager.java:259) ~[flink-connector-base-1.20.0.jar:1.20.0]
	at org.apache.flink.connector.base.source.reader.fetcher.SingleThreadFetcherManager.addSplits(SingleThreadFetcherManager.java:148) ~[flink-connector-base-1.20.0.jar:1.20.0]
	at org.apache.flink.connector.base.source.reader.SourceReaderBase.addSplits(SourceReaderBase.java:315) ~[flink-connector-base-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.api.operators.SourceOperator.handleAddSplitsEvent(SourceOperator.java:606) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.api.operators.SourceOperator.handleOperatorEvent(SourceOperator.java:575) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.OperatorEventDispatcherImpl.dispatchEventToHandlers(OperatorEventDispatcherImpl.java:72) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.RegularOperatorChain.dispatchOperatorEvent(RegularOperatorChain.java:80) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.StreamTask.lambda$dispatchOperatorEvent$24(StreamTask.java:1609) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.StreamTaskActionExecutor$1.runThrowing(StreamTaskActionExecutor.java:50) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.mailbox.Mail.run(Mail.java:101) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.mailbox.MailboxProcessor.runMail(MailboxProcessor.java:414) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.mailbox.MailboxProcessor.processMailsNonBlocking(MailboxProcessor.java:399) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.mailbox.MailboxProcessor.processMail(MailboxProcessor.java:361) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.mailbox.MailboxProcessor.runMailboxLoop(MailboxProcessor.java:229) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.StreamTask.runMailboxLoop(StreamTask.java:973) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.StreamTask.invoke(StreamTask.java:917) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.runtime.taskmanager.Task.runWithSystemExitMonitoring(Task.java:970) [flink-runtime-1.20.0.jar:1.20.0]
	at org.apache.flink.runtime.taskmanager.Task.restoreAndInvoke(Task.java:949) [flink-runtime-1.20.0.jar:1.20.0]
	at org.apache.flink.runtime.taskmanager.Task.doRun(Task.java:763) [flink-runtime-1.20.0.jar:1.20.0]
	at org.apache.flink.runtime.taskmanager.Task.run(Task.java:575) [flink-runtime-1.20.0.jar:1.20.0]
	at java.lang.Thread.run(Thread.java:840) [?:?]
2025-08-20 15:24:09.680 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> 拆分组合订单 -> (Map, Map) (1/1)#0] WARN  org.apache.kafka.common.utils.AppInfoParser - Error registering AppInfo mbean
javax.management.InstanceAlreadyExistsException: kafka.consumer:type=app-info,id=orderbook-debug-group-0
	at com.sun.jmx.mbeanserver.Repository.addMBean(Repository.java:436) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerWithRepository(DefaultMBeanServerInterceptor.java:1865) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerDynamicMBean(DefaultMBeanServerInterceptor.java:960) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerObject(DefaultMBeanServerInterceptor.java:895) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerMBean(DefaultMBeanServerInterceptor.java:320) ~[?:?]
	at com.sun.jmx.mbeanserver.JmxMBeanServer.registerMBean(JmxMBeanServer.java:523) ~[?:?]
	at org.apache.kafka.common.utils.AppInfoParser.registerAppInfo(AppInfoParser.java:64) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.consumer.KafkaConsumer.<init>(KafkaConsumer.java:821) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.consumer.KafkaConsumer.<init>(KafkaConsumer.java:665) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.consumer.KafkaConsumer.<init>(KafkaConsumer.java:646) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.consumer.KafkaConsumer.<init>(KafkaConsumer.java:626) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.flink.connector.kafka.source.reader.KafkaPartitionSplitReader.<init>(KafkaPartitionSplitReader.java:97) ~[flink-connector-kafka-3.4.0-1.20.jar:3.4.0-1.20]
	at org.apache.flink.connector.kafka.source.KafkaSource.lambda$createReader$1(KafkaSource.java:185) ~[flink-connector-kafka-3.4.0-1.20.jar:3.4.0-1.20]
	at org.apache.flink.connector.base.source.reader.fetcher.SplitFetcherManager.createSplitFetcher(SplitFetcherManager.java:259) ~[flink-connector-base-1.20.0.jar:1.20.0]
	at org.apache.flink.connector.base.source.reader.fetcher.SingleThreadFetcherManager.addSplits(SingleThreadFetcherManager.java:148) ~[flink-connector-base-1.20.0.jar:1.20.0]
	at org.apache.flink.connector.base.source.reader.SourceReaderBase.addSplits(SourceReaderBase.java:315) ~[flink-connector-base-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.api.operators.SourceOperator.handleAddSplitsEvent(SourceOperator.java:606) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.api.operators.SourceOperator.handleOperatorEvent(SourceOperator.java:575) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.OperatorEventDispatcherImpl.dispatchEventToHandlers(OperatorEventDispatcherImpl.java:72) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.RegularOperatorChain.dispatchOperatorEvent(RegularOperatorChain.java:80) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.StreamTask.lambda$dispatchOperatorEvent$24(StreamTask.java:1609) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.StreamTaskActionExecutor$1.runThrowing(StreamTaskActionExecutor.java:50) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.mailbox.Mail.run(Mail.java:101) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.mailbox.MailboxProcessor.runMail(MailboxProcessor.java:414) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.mailbox.MailboxProcessor.processMailsNonBlocking(MailboxProcessor.java:399) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.mailbox.MailboxProcessor.processMail(MailboxProcessor.java:361) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.mailbox.MailboxProcessor.runMailboxLoop(MailboxProcessor.java:229) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.StreamTask.runMailboxLoop(StreamTask.java:973) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.StreamTask.invoke(StreamTask.java:917) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.runtime.taskmanager.Task.runWithSystemExitMonitoring(Task.java:970) [flink-runtime-1.20.0.jar:1.20.0]
	at org.apache.flink.runtime.taskmanager.Task.restoreAndInvoke(Task.java:949) [flink-runtime-1.20.0.jar:1.20.0]
	at org.apache.flink.runtime.taskmanager.Task.doRun(Task.java:763) [flink-runtime-1.20.0.jar:1.20.0]
	at org.apache.flink.runtime.taskmanager.Task.run(Task.java:575) [flink-runtime-1.20.0.jar:1.20.0]
	at java.lang.Thread.run(Thread.java:840) [?:?]
2025-08-20 15:27:43.241 [SourceCoordinator-Source: Kafka-组合订单] WARN  org.apache.kafka.clients.admin.AdminClientConfig - These configurations '[key.deserializer, value.deserializer, enable.auto.commit, client.id.prefix, group.id, partition.discovery.interval.ms, auto.offset.reset]' were supplied but are not used yet.
2025-08-20 15:27:43.241 [SourceCoordinator-Source: Kafka-单腿订单] WARN  org.apache.kafka.clients.admin.AdminClientConfig - These configurations '[key.deserializer, value.deserializer, enable.auto.commit, client.id.prefix, group.id, partition.discovery.interval.ms, auto.offset.reset]' were supplied but are not used yet.
2025-08-20 15:27:43.241 [SourceCoordinator-Source: Kafka-交易] WARN  org.apache.kafka.clients.admin.AdminClientConfig - These configurations '[key.deserializer, value.deserializer, enable.auto.commit, client.id.prefix, group.id, partition.discovery.interval.ms, auto.offset.reset]' were supplied but are not used yet.
2025-08-20 15:27:43.243 [SourceCoordinator-Source: Kafka-组合订单] INFO  org.apache.flink.connector.kafka.source.enumerator.KafkaSourceEnumerator - Starting the KafkaSourceEnumerator for consumer group orderbook-debug-group without periodic partition discovery.
2025-08-20 15:27:43.243 [SourceCoordinator-Source: Kafka-单腿订单] WARN  org.apache.kafka.common.utils.AppInfoParser - Error registering AppInfo mbean
javax.management.InstanceAlreadyExistsException: kafka.admin.client:type=app-info,id=orderbook-debug-group-enumerator-admin-client
	at com.sun.jmx.mbeanserver.Repository.addMBean(Repository.java:436) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerWithRepository(DefaultMBeanServerInterceptor.java:1865) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerDynamicMBean(DefaultMBeanServerInterceptor.java:960) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerObject(DefaultMBeanServerInterceptor.java:895) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerMBean(DefaultMBeanServerInterceptor.java:320) ~[?:?]
	at com.sun.jmx.mbeanserver.JmxMBeanServer.registerMBean(JmxMBeanServer.java:523) ~[?:?]
	at org.apache.kafka.common.utils.AppInfoParser.registerAppInfo(AppInfoParser.java:64) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.admin.KafkaAdminClient.<init>(KafkaAdminClient.java:602) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.admin.KafkaAdminClient.createInternal(KafkaAdminClient.java:544) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.admin.KafkaAdminClient.createInternal(KafkaAdminClient.java:488) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.admin.Admin.create(Admin.java:134) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.admin.AdminClient.create(AdminClient.java:39) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.flink.connector.kafka.source.enumerator.KafkaSourceEnumerator.getKafkaAdminClient(KafkaSourceEnumerator.java:441) ~[flink-connector-kafka-3.4.0-1.20.jar:3.4.0-1.20]
	at org.apache.flink.connector.kafka.source.enumerator.KafkaSourceEnumerator.start(KafkaSourceEnumerator.java:164) ~[flink-connector-kafka-3.4.0-1.20.jar:3.4.0-1.20]
	at org.apache.flink.runtime.source.coordinator.SourceCoordinator.lambda$start$1(SourceCoordinator.java:244) ~[flink-runtime-1.20.0.jar:1.20.0]
	at org.apache.flink.runtime.source.coordinator.SourceCoordinator.lambda$runInEventLoop$10(SourceCoordinator.java:533) ~[flink-runtime-1.20.0.jar:1.20.0]
	at org.apache.flink.util.ThrowableCatchingRunnable.run(ThrowableCatchingRunnable.java:40) [flink-core-1.20.0.jar:1.20.0]
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:539) [?:?]
	at java.util.concurrent.FutureTask.run(FutureTask.java:264) [?:?]
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:304) [?:?]
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136) [?:?]
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635) [?:?]
	at java.lang.Thread.run(Thread.java:840) [?:?]
2025-08-20 15:27:43.245 [SourceCoordinator-Source: Kafka-单腿订单] INFO  org.apache.flink.connector.kafka.source.enumerator.KafkaSourceEnumerator - Starting the KafkaSourceEnumerator for consumer group orderbook-debug-group without periodic partition discovery.
2025-08-20 15:27:43.245 [SourceCoordinator-Source: Kafka-交易] WARN  org.apache.kafka.common.utils.AppInfoParser - Error registering AppInfo mbean
javax.management.InstanceAlreadyExistsException: kafka.admin.client:type=app-info,id=orderbook-debug-group-enumerator-admin-client
	at com.sun.jmx.mbeanserver.Repository.addMBean(Repository.java:436) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerWithRepository(DefaultMBeanServerInterceptor.java:1865) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerDynamicMBean(DefaultMBeanServerInterceptor.java:960) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerObject(DefaultMBeanServerInterceptor.java:895) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerMBean(DefaultMBeanServerInterceptor.java:320) ~[?:?]
	at com.sun.jmx.mbeanserver.JmxMBeanServer.registerMBean(JmxMBeanServer.java:523) ~[?:?]
	at org.apache.kafka.common.utils.AppInfoParser.registerAppInfo(AppInfoParser.java:64) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.admin.KafkaAdminClient.<init>(KafkaAdminClient.java:602) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.admin.KafkaAdminClient.createInternal(KafkaAdminClient.java:544) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.admin.KafkaAdminClient.createInternal(KafkaAdminClient.java:488) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.admin.Admin.create(Admin.java:134) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.admin.AdminClient.create(AdminClient.java:39) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.flink.connector.kafka.source.enumerator.KafkaSourceEnumerator.getKafkaAdminClient(KafkaSourceEnumerator.java:441) ~[flink-connector-kafka-3.4.0-1.20.jar:3.4.0-1.20]
	at org.apache.flink.connector.kafka.source.enumerator.KafkaSourceEnumerator.start(KafkaSourceEnumerator.java:164) ~[flink-connector-kafka-3.4.0-1.20.jar:3.4.0-1.20]
	at org.apache.flink.runtime.source.coordinator.SourceCoordinator.lambda$start$1(SourceCoordinator.java:244) ~[flink-runtime-1.20.0.jar:1.20.0]
	at org.apache.flink.runtime.source.coordinator.SourceCoordinator.lambda$runInEventLoop$10(SourceCoordinator.java:533) ~[flink-runtime-1.20.0.jar:1.20.0]
	at org.apache.flink.util.ThrowableCatchingRunnable.run(ThrowableCatchingRunnable.java:40) [flink-core-1.20.0.jar:1.20.0]
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:539) [?:?]
	at java.util.concurrent.FutureTask.run(FutureTask.java:264) [?:?]
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:304) [?:?]
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136) [?:?]
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635) [?:?]
	at java.lang.Thread.run(Thread.java:840) [?:?]
2025-08-20 15:27:43.246 [SourceCoordinator-Source: Kafka-交易] INFO  org.apache.flink.connector.kafka.source.enumerator.KafkaSourceEnumerator - Starting the KafkaSourceEnumerator for consumer group orderbook-debug-group without periodic partition discovery.
2025-08-20 15:27:43.354 [SourceCoordinator-Source: Kafka-单腿订单] INFO  org.apache.flink.connector.kafka.source.enumerator.KafkaSourceEnumerator - Discovered new partitions: [singleleg_order_data_event-0]
2025-08-20 15:27:43.354 [SourceCoordinator-Source: Kafka-交易] INFO  org.apache.flink.connector.kafka.source.enumerator.KafkaSourceEnumerator - Discovered new partitions: [trade_data_event-0]
2025-08-20 15:27:43.354 [SourceCoordinator-Source: Kafka-组合订单] INFO  org.apache.flink.connector.kafka.source.enumerator.KafkaSourceEnumerator - Discovered new partitions: [cmb_order_data_event-0]
2025-08-20 15:27:43.367 [SourceCoordinator-Source: Kafka-交易] INFO  org.apache.flink.connector.kafka.source.enumerator.KafkaSourceEnumerator - Assigning splits to readers {0=[[Partition: trade_data_event-0, StartingOffset: -2, StoppingOffset: -9223372036854775808]]}
2025-08-20 15:27:43.367 [SourceCoordinator-Source: Kafka-单腿订单] INFO  org.apache.flink.connector.kafka.source.enumerator.KafkaSourceEnumerator - Assigning splits to readers {0=[[Partition: singleleg_order_data_event-0, StartingOffset: -2, StoppingOffset: -9223372036854775808]]}
2025-08-20 15:27:43.367 [SourceCoordinator-Source: Kafka-组合订单] INFO  org.apache.flink.connector.kafka.source.enumerator.KafkaSourceEnumerator - Assigning splits to readers {0=[[Partition: cmb_order_data_event-0, StartingOffset: -2, StoppingOffset: -9223372036854775808]]}
2025-08-20 15:27:43.397 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] WARN  org.apache.kafka.clients.consumer.ConsumerConfig - These configurations '[client.id.prefix, partition.discovery.interval.ms]' were supplied but are not used yet.
2025-08-20 15:27:43.397 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> 拆分组合订单 -> (Map, Map) (1/1)#0] WARN  org.apache.kafka.clients.consumer.ConsumerConfig - These configurations '[client.id.prefix, partition.discovery.interval.ms]' were supplied but are not used yet.
2025-08-20 15:27:43.397 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] WARN  org.apache.kafka.clients.consumer.ConsumerConfig - These configurations '[client.id.prefix, partition.discovery.interval.ms]' were supplied but are not used yet.
2025-08-20 15:27:43.397 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] WARN  org.apache.kafka.common.utils.AppInfoParser - Error registering AppInfo mbean
javax.management.InstanceAlreadyExistsException: kafka.consumer:type=app-info,id=orderbook-debug-group-0
	at com.sun.jmx.mbeanserver.Repository.addMBean(Repository.java:436) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerWithRepository(DefaultMBeanServerInterceptor.java:1865) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerDynamicMBean(DefaultMBeanServerInterceptor.java:960) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerObject(DefaultMBeanServerInterceptor.java:895) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerMBean(DefaultMBeanServerInterceptor.java:320) ~[?:?]
	at com.sun.jmx.mbeanserver.JmxMBeanServer.registerMBean(JmxMBeanServer.java:523) ~[?:?]
	at org.apache.kafka.common.utils.AppInfoParser.registerAppInfo(AppInfoParser.java:64) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.consumer.KafkaConsumer.<init>(KafkaConsumer.java:821) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.consumer.KafkaConsumer.<init>(KafkaConsumer.java:665) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.consumer.KafkaConsumer.<init>(KafkaConsumer.java:646) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.consumer.KafkaConsumer.<init>(KafkaConsumer.java:626) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.flink.connector.kafka.source.reader.KafkaPartitionSplitReader.<init>(KafkaPartitionSplitReader.java:97) ~[flink-connector-kafka-3.4.0-1.20.jar:3.4.0-1.20]
	at org.apache.flink.connector.kafka.source.KafkaSource.lambda$createReader$1(KafkaSource.java:185) ~[flink-connector-kafka-3.4.0-1.20.jar:3.4.0-1.20]
	at org.apache.flink.connector.base.source.reader.fetcher.SplitFetcherManager.createSplitFetcher(SplitFetcherManager.java:259) ~[flink-connector-base-1.20.0.jar:1.20.0]
	at org.apache.flink.connector.base.source.reader.fetcher.SingleThreadFetcherManager.addSplits(SingleThreadFetcherManager.java:148) ~[flink-connector-base-1.20.0.jar:1.20.0]
	at org.apache.flink.connector.base.source.reader.SourceReaderBase.addSplits(SourceReaderBase.java:315) ~[flink-connector-base-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.api.operators.SourceOperator.handleAddSplitsEvent(SourceOperator.java:606) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.api.operators.SourceOperator.handleOperatorEvent(SourceOperator.java:575) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.OperatorEventDispatcherImpl.dispatchEventToHandlers(OperatorEventDispatcherImpl.java:72) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.RegularOperatorChain.dispatchOperatorEvent(RegularOperatorChain.java:80) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.StreamTask.lambda$dispatchOperatorEvent$24(StreamTask.java:1609) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.StreamTaskActionExecutor$1.runThrowing(StreamTaskActionExecutor.java:50) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.mailbox.Mail.run(Mail.java:101) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.mailbox.MailboxProcessor.runMail(MailboxProcessor.java:414) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.mailbox.MailboxProcessor.processMailsWhenDefaultActionUnavailable(MailboxProcessor.java:383) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.mailbox.MailboxProcessor.processMail(MailboxProcessor.java:368) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.mailbox.MailboxProcessor.runMailboxLoop(MailboxProcessor.java:229) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.StreamTask.runMailboxLoop(StreamTask.java:973) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.StreamTask.invoke(StreamTask.java:917) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.runtime.taskmanager.Task.runWithSystemExitMonitoring(Task.java:970) [flink-runtime-1.20.0.jar:1.20.0]
	at org.apache.flink.runtime.taskmanager.Task.restoreAndInvoke(Task.java:949) [flink-runtime-1.20.0.jar:1.20.0]
	at org.apache.flink.runtime.taskmanager.Task.doRun(Task.java:763) [flink-runtime-1.20.0.jar:1.20.0]
	at org.apache.flink.runtime.taskmanager.Task.run(Task.java:575) [flink-runtime-1.20.0.jar:1.20.0]
	at java.lang.Thread.run(Thread.java:840) [?:?]
2025-08-20 15:27:43.398 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] WARN  org.apache.kafka.common.utils.AppInfoParser - Error registering AppInfo mbean
javax.management.InstanceAlreadyExistsException: kafka.consumer:type=app-info,id=orderbook-debug-group-0
	at com.sun.jmx.mbeanserver.Repository.addMBean(Repository.java:436) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerWithRepository(DefaultMBeanServerInterceptor.java:1865) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerDynamicMBean(DefaultMBeanServerInterceptor.java:960) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerObject(DefaultMBeanServerInterceptor.java:895) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerMBean(DefaultMBeanServerInterceptor.java:320) ~[?:?]
	at com.sun.jmx.mbeanserver.JmxMBeanServer.registerMBean(JmxMBeanServer.java:523) ~[?:?]
	at org.apache.kafka.common.utils.AppInfoParser.registerAppInfo(AppInfoParser.java:64) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.consumer.KafkaConsumer.<init>(KafkaConsumer.java:821) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.consumer.KafkaConsumer.<init>(KafkaConsumer.java:665) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.consumer.KafkaConsumer.<init>(KafkaConsumer.java:646) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.consumer.KafkaConsumer.<init>(KafkaConsumer.java:626) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.flink.connector.kafka.source.reader.KafkaPartitionSplitReader.<init>(KafkaPartitionSplitReader.java:97) ~[flink-connector-kafka-3.4.0-1.20.jar:3.4.0-1.20]
	at org.apache.flink.connector.kafka.source.KafkaSource.lambda$createReader$1(KafkaSource.java:185) ~[flink-connector-kafka-3.4.0-1.20.jar:3.4.0-1.20]
	at org.apache.flink.connector.base.source.reader.fetcher.SplitFetcherManager.createSplitFetcher(SplitFetcherManager.java:259) ~[flink-connector-base-1.20.0.jar:1.20.0]
	at org.apache.flink.connector.base.source.reader.fetcher.SingleThreadFetcherManager.addSplits(SingleThreadFetcherManager.java:148) ~[flink-connector-base-1.20.0.jar:1.20.0]
	at org.apache.flink.connector.base.source.reader.SourceReaderBase.addSplits(SourceReaderBase.java:315) ~[flink-connector-base-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.api.operators.SourceOperator.handleAddSplitsEvent(SourceOperator.java:606) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.api.operators.SourceOperator.handleOperatorEvent(SourceOperator.java:575) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.OperatorEventDispatcherImpl.dispatchEventToHandlers(OperatorEventDispatcherImpl.java:72) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.RegularOperatorChain.dispatchOperatorEvent(RegularOperatorChain.java:80) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.StreamTask.lambda$dispatchOperatorEvent$24(StreamTask.java:1609) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.StreamTaskActionExecutor$1.runThrowing(StreamTaskActionExecutor.java:50) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.mailbox.Mail.run(Mail.java:101) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.mailbox.MailboxProcessor.runMail(MailboxProcessor.java:414) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.mailbox.MailboxProcessor.processMailsWhenDefaultActionUnavailable(MailboxProcessor.java:383) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.mailbox.MailboxProcessor.processMail(MailboxProcessor.java:368) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.mailbox.MailboxProcessor.runMailboxLoop(MailboxProcessor.java:229) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.StreamTask.runMailboxLoop(StreamTask.java:973) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.StreamTask.invoke(StreamTask.java:917) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.runtime.taskmanager.Task.runWithSystemExitMonitoring(Task.java:970) [flink-runtime-1.20.0.jar:1.20.0]
	at org.apache.flink.runtime.taskmanager.Task.restoreAndInvoke(Task.java:949) [flink-runtime-1.20.0.jar:1.20.0]
	at org.apache.flink.runtime.taskmanager.Task.doRun(Task.java:763) [flink-runtime-1.20.0.jar:1.20.0]
	at org.apache.flink.runtime.taskmanager.Task.run(Task.java:575) [flink-runtime-1.20.0.jar:1.20.0]
	at java.lang.Thread.run(Thread.java:840) [?:?]
2025-08-20 18:40:16.443 [main] WARN  org.apache.flink.connector.kafka.source.KafkaSourceBuilder - Property auto.offset.reset is provided but will be overridden from earliest to earliest
2025-08-20 18:40:16.461 [main] WARN  org.apache.flink.connector.kafka.source.KafkaSourceBuilder - Property auto.offset.reset is provided but will be overridden from earliest to earliest
2025-08-20 18:40:16.461 [main] WARN  org.apache.flink.connector.kafka.source.KafkaSourceBuilder - Property auto.offset.reset is provided but will be overridden from earliest to earliest
2025-08-20 18:40:17.256 [SourceCoordinator-Source: Kafka-组合订单] WARN  org.apache.kafka.clients.admin.AdminClientConfig - These configurations '[key.deserializer, value.deserializer, max.poll.records, client.id.prefix, group.id, partition.discovery.interval.ms, heartbeat.interval.ms, enable.auto.commit, fetch.max.wait.ms, fetch.min.bytes, session.timeout.ms, auto.offset.reset]' were supplied but are not used yet.
2025-08-20 18:40:17.256 [SourceCoordinator-Source: Kafka-单腿订单] WARN  org.apache.kafka.clients.admin.AdminClientConfig - These configurations '[key.deserializer, value.deserializer, max.poll.records, client.id.prefix, group.id, partition.discovery.interval.ms, heartbeat.interval.ms, enable.auto.commit, fetch.max.wait.ms, fetch.min.bytes, session.timeout.ms, auto.offset.reset]' were supplied but are not used yet.
2025-08-20 18:40:17.256 [SourceCoordinator-Source: Kafka-交易] WARN  org.apache.kafka.clients.admin.AdminClientConfig - These configurations '[key.deserializer, value.deserializer, max.poll.records, client.id.prefix, group.id, partition.discovery.interval.ms, heartbeat.interval.ms, enable.auto.commit, fetch.max.wait.ms, fetch.min.bytes, session.timeout.ms, auto.offset.reset]' were supplied but are not used yet.
2025-08-20 18:40:17.257 [SourceCoordinator-Source: Kafka-组合订单] INFO  org.apache.flink.connector.kafka.source.enumerator.KafkaSourceEnumerator - Starting the KafkaSourceEnumerator for consumer group orderbook-local-test-group without periodic partition discovery.
2025-08-20 18:40:17.257 [SourceCoordinator-Source: Kafka-单腿订单] WARN  org.apache.kafka.common.utils.AppInfoParser - Error registering AppInfo mbean
javax.management.InstanceAlreadyExistsException: kafka.admin.client:type=app-info,id=orderbook-local-test-group-enumerator-admin-client
	at com.sun.jmx.mbeanserver.Repository.addMBean(Repository.java:436) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerWithRepository(DefaultMBeanServerInterceptor.java:1865) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerDynamicMBean(DefaultMBeanServerInterceptor.java:960) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerObject(DefaultMBeanServerInterceptor.java:895) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerMBean(DefaultMBeanServerInterceptor.java:320) ~[?:?]
	at com.sun.jmx.mbeanserver.JmxMBeanServer.registerMBean(JmxMBeanServer.java:523) ~[?:?]
	at org.apache.kafka.common.utils.AppInfoParser.registerAppInfo(AppInfoParser.java:64) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.admin.KafkaAdminClient.<init>(KafkaAdminClient.java:602) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.admin.KafkaAdminClient.createInternal(KafkaAdminClient.java:544) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.admin.KafkaAdminClient.createInternal(KafkaAdminClient.java:488) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.admin.Admin.create(Admin.java:134) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.admin.AdminClient.create(AdminClient.java:39) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.flink.connector.kafka.source.enumerator.KafkaSourceEnumerator.getKafkaAdminClient(KafkaSourceEnumerator.java:441) ~[flink-connector-kafka-3.4.0-1.20.jar:3.4.0-1.20]
	at org.apache.flink.connector.kafka.source.enumerator.KafkaSourceEnumerator.start(KafkaSourceEnumerator.java:164) ~[flink-connector-kafka-3.4.0-1.20.jar:3.4.0-1.20]
	at org.apache.flink.runtime.source.coordinator.SourceCoordinator.lambda$start$1(SourceCoordinator.java:244) ~[flink-runtime-1.20.0.jar:1.20.0]
	at org.apache.flink.runtime.source.coordinator.SourceCoordinator.lambda$runInEventLoop$10(SourceCoordinator.java:533) ~[flink-runtime-1.20.0.jar:1.20.0]
	at org.apache.flink.util.ThrowableCatchingRunnable.run(ThrowableCatchingRunnable.java:40) [flink-core-1.20.0.jar:1.20.0]
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:539) [?:?]
	at java.util.concurrent.FutureTask.run(FutureTask.java:264) [?:?]
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:304) [?:?]
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136) [?:?]
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635) [?:?]
	at java.lang.Thread.run(Thread.java:840) [?:?]
2025-08-20 18:40:17.260 [SourceCoordinator-Source: Kafka-单腿订单] INFO  org.apache.flink.connector.kafka.source.enumerator.KafkaSourceEnumerator - Starting the KafkaSourceEnumerator for consumer group orderbook-local-test-group without periodic partition discovery.
2025-08-20 18:40:17.260 [SourceCoordinator-Source: Kafka-交易] WARN  org.apache.kafka.common.utils.AppInfoParser - Error registering AppInfo mbean
javax.management.InstanceAlreadyExistsException: kafka.admin.client:type=app-info,id=orderbook-local-test-group-enumerator-admin-client
	at com.sun.jmx.mbeanserver.Repository.addMBean(Repository.java:436) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerWithRepository(DefaultMBeanServerInterceptor.java:1865) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerDynamicMBean(DefaultMBeanServerInterceptor.java:960) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerObject(DefaultMBeanServerInterceptor.java:895) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerMBean(DefaultMBeanServerInterceptor.java:320) ~[?:?]
	at com.sun.jmx.mbeanserver.JmxMBeanServer.registerMBean(JmxMBeanServer.java:523) ~[?:?]
	at org.apache.kafka.common.utils.AppInfoParser.registerAppInfo(AppInfoParser.java:64) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.admin.KafkaAdminClient.<init>(KafkaAdminClient.java:602) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.admin.KafkaAdminClient.createInternal(KafkaAdminClient.java:544) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.admin.KafkaAdminClient.createInternal(KafkaAdminClient.java:488) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.admin.Admin.create(Admin.java:134) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.admin.AdminClient.create(AdminClient.java:39) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.flink.connector.kafka.source.enumerator.KafkaSourceEnumerator.getKafkaAdminClient(KafkaSourceEnumerator.java:441) ~[flink-connector-kafka-3.4.0-1.20.jar:3.4.0-1.20]
	at org.apache.flink.connector.kafka.source.enumerator.KafkaSourceEnumerator.start(KafkaSourceEnumerator.java:164) ~[flink-connector-kafka-3.4.0-1.20.jar:3.4.0-1.20]
	at org.apache.flink.runtime.source.coordinator.SourceCoordinator.lambda$start$1(SourceCoordinator.java:244) ~[flink-runtime-1.20.0.jar:1.20.0]
	at org.apache.flink.runtime.source.coordinator.SourceCoordinator.lambda$runInEventLoop$10(SourceCoordinator.java:533) ~[flink-runtime-1.20.0.jar:1.20.0]
	at org.apache.flink.util.ThrowableCatchingRunnable.run(ThrowableCatchingRunnable.java:40) [flink-core-1.20.0.jar:1.20.0]
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:539) [?:?]
	at java.util.concurrent.FutureTask.run(FutureTask.java:264) [?:?]
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:304) [?:?]
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136) [?:?]
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635) [?:?]
	at java.lang.Thread.run(Thread.java:840) [?:?]
2025-08-20 18:40:17.261 [SourceCoordinator-Source: Kafka-交易] INFO  org.apache.flink.connector.kafka.source.enumerator.KafkaSourceEnumerator - Starting the KafkaSourceEnumerator for consumer group orderbook-local-test-group without periodic partition discovery.
2025-08-20 18:40:17.383 [SourceCoordinator-Source: Kafka-单腿订单] INFO  org.apache.flink.connector.kafka.source.enumerator.KafkaSourceEnumerator - Discovered new partitions: [singleleg_order_data_event-0]
2025-08-20 18:40:17.383 [SourceCoordinator-Source: Kafka-交易] INFO  org.apache.flink.connector.kafka.source.enumerator.KafkaSourceEnumerator - Discovered new partitions: [trade_data_event-0]
2025-08-20 18:40:17.383 [SourceCoordinator-Source: Kafka-组合订单] INFO  org.apache.flink.connector.kafka.source.enumerator.KafkaSourceEnumerator - Discovered new partitions: [cmb_order_data_event-0]
2025-08-20 18:40:17.392 [SourceCoordinator-Source: Kafka-组合订单] INFO  org.apache.flink.connector.kafka.source.enumerator.KafkaSourceEnumerator - Assigning splits to readers {0=[[Partition: cmb_order_data_event-0, StartingOffset: -2, StoppingOffset: -9223372036854775808]]}
2025-08-20 18:40:17.392 [SourceCoordinator-Source: Kafka-交易] INFO  org.apache.flink.connector.kafka.source.enumerator.KafkaSourceEnumerator - Assigning splits to readers {0=[[Partition: trade_data_event-0, StartingOffset: -2, StoppingOffset: -9223372036854775808]]}
2025-08-20 18:40:17.392 [SourceCoordinator-Source: Kafka-单腿订单] INFO  org.apache.flink.connector.kafka.source.enumerator.KafkaSourceEnumerator - Assigning splits to readers {0=[[Partition: singleleg_order_data_event-0, StartingOffset: -2, StoppingOffset: -9223372036854775808]]}
2025-08-20 18:40:17.420 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] WARN  org.apache.kafka.clients.consumer.ConsumerConfig - These configurations '[client.id.prefix, partition.discovery.interval.ms]' were supplied but are not used yet.
2025-08-20 18:40:17.420 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] WARN  org.apache.kafka.clients.consumer.ConsumerConfig - These configurations '[client.id.prefix, partition.discovery.interval.ms]' were supplied but are not used yet.
2025-08-20 18:40:17.420 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] WARN  org.apache.kafka.clients.consumer.ConsumerConfig - These configurations '[client.id.prefix, partition.discovery.interval.ms]' were supplied but are not used yet.
2025-08-20 18:40:17.421 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] WARN  org.apache.kafka.common.utils.AppInfoParser - Error registering AppInfo mbean
javax.management.InstanceAlreadyExistsException: kafka.consumer:type=app-info,id=orderbook-local-test-group-0
	at com.sun.jmx.mbeanserver.Repository.addMBean(Repository.java:436) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerWithRepository(DefaultMBeanServerInterceptor.java:1865) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerDynamicMBean(DefaultMBeanServerInterceptor.java:960) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerObject(DefaultMBeanServerInterceptor.java:895) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerMBean(DefaultMBeanServerInterceptor.java:320) ~[?:?]
	at com.sun.jmx.mbeanserver.JmxMBeanServer.registerMBean(JmxMBeanServer.java:523) ~[?:?]
	at org.apache.kafka.common.utils.AppInfoParser.registerAppInfo(AppInfoParser.java:64) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.consumer.KafkaConsumer.<init>(KafkaConsumer.java:821) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.consumer.KafkaConsumer.<init>(KafkaConsumer.java:665) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.consumer.KafkaConsumer.<init>(KafkaConsumer.java:646) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.consumer.KafkaConsumer.<init>(KafkaConsumer.java:626) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.flink.connector.kafka.source.reader.KafkaPartitionSplitReader.<init>(KafkaPartitionSplitReader.java:97) ~[flink-connector-kafka-3.4.0-1.20.jar:3.4.0-1.20]
	at org.apache.flink.connector.kafka.source.KafkaSource.lambda$createReader$1(KafkaSource.java:185) ~[flink-connector-kafka-3.4.0-1.20.jar:3.4.0-1.20]
	at org.apache.flink.connector.base.source.reader.fetcher.SplitFetcherManager.createSplitFetcher(SplitFetcherManager.java:259) ~[flink-connector-base-1.20.0.jar:1.20.0]
	at org.apache.flink.connector.base.source.reader.fetcher.SingleThreadFetcherManager.addSplits(SingleThreadFetcherManager.java:148) ~[flink-connector-base-1.20.0.jar:1.20.0]
	at org.apache.flink.connector.base.source.reader.SourceReaderBase.addSplits(SourceReaderBase.java:315) ~[flink-connector-base-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.api.operators.SourceOperator.handleAddSplitsEvent(SourceOperator.java:606) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.api.operators.SourceOperator.handleOperatorEvent(SourceOperator.java:575) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.OperatorEventDispatcherImpl.dispatchEventToHandlers(OperatorEventDispatcherImpl.java:72) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.RegularOperatorChain.dispatchOperatorEvent(RegularOperatorChain.java:80) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.StreamTask.lambda$dispatchOperatorEvent$24(StreamTask.java:1609) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.StreamTaskActionExecutor$1.runThrowing(StreamTaskActionExecutor.java:50) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.mailbox.Mail.run(Mail.java:101) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.mailbox.MailboxProcessor.runMail(MailboxProcessor.java:414) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.mailbox.MailboxProcessor.processMailsWhenDefaultActionUnavailable(MailboxProcessor.java:383) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.mailbox.MailboxProcessor.processMail(MailboxProcessor.java:368) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.mailbox.MailboxProcessor.runMailboxLoop(MailboxProcessor.java:229) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.StreamTask.runMailboxLoop(StreamTask.java:973) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.StreamTask.invoke(StreamTask.java:917) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.runtime.taskmanager.Task.runWithSystemExitMonitoring(Task.java:970) [flink-runtime-1.20.0.jar:1.20.0]
	at org.apache.flink.runtime.taskmanager.Task.restoreAndInvoke(Task.java:949) [flink-runtime-1.20.0.jar:1.20.0]
	at org.apache.flink.runtime.taskmanager.Task.doRun(Task.java:763) [flink-runtime-1.20.0.jar:1.20.0]
	at org.apache.flink.runtime.taskmanager.Task.run(Task.java:575) [flink-runtime-1.20.0.jar:1.20.0]
	at java.lang.Thread.run(Thread.java:840) [?:?]
2025-08-20 18:40:17.421 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] WARN  org.apache.kafka.common.utils.AppInfoParser - Error registering AppInfo mbean
javax.management.InstanceAlreadyExistsException: kafka.consumer:type=app-info,id=orderbook-local-test-group-0
	at com.sun.jmx.mbeanserver.Repository.addMBean(Repository.java:436) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerWithRepository(DefaultMBeanServerInterceptor.java:1865) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerDynamicMBean(DefaultMBeanServerInterceptor.java:960) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerObject(DefaultMBeanServerInterceptor.java:895) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerMBean(DefaultMBeanServerInterceptor.java:320) ~[?:?]
	at com.sun.jmx.mbeanserver.JmxMBeanServer.registerMBean(JmxMBeanServer.java:523) ~[?:?]
	at org.apache.kafka.common.utils.AppInfoParser.registerAppInfo(AppInfoParser.java:64) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.consumer.KafkaConsumer.<init>(KafkaConsumer.java:821) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.consumer.KafkaConsumer.<init>(KafkaConsumer.java:665) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.consumer.KafkaConsumer.<init>(KafkaConsumer.java:646) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.consumer.KafkaConsumer.<init>(KafkaConsumer.java:626) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.flink.connector.kafka.source.reader.KafkaPartitionSplitReader.<init>(KafkaPartitionSplitReader.java:97) ~[flink-connector-kafka-3.4.0-1.20.jar:3.4.0-1.20]
	at org.apache.flink.connector.kafka.source.KafkaSource.lambda$createReader$1(KafkaSource.java:185) ~[flink-connector-kafka-3.4.0-1.20.jar:3.4.0-1.20]
	at org.apache.flink.connector.base.source.reader.fetcher.SplitFetcherManager.createSplitFetcher(SplitFetcherManager.java:259) ~[flink-connector-base-1.20.0.jar:1.20.0]
	at org.apache.flink.connector.base.source.reader.fetcher.SingleThreadFetcherManager.addSplits(SingleThreadFetcherManager.java:148) ~[flink-connector-base-1.20.0.jar:1.20.0]
	at org.apache.flink.connector.base.source.reader.SourceReaderBase.addSplits(SourceReaderBase.java:315) ~[flink-connector-base-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.api.operators.SourceOperator.handleAddSplitsEvent(SourceOperator.java:606) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.api.operators.SourceOperator.handleOperatorEvent(SourceOperator.java:575) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.OperatorEventDispatcherImpl.dispatchEventToHandlers(OperatorEventDispatcherImpl.java:72) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.RegularOperatorChain.dispatchOperatorEvent(RegularOperatorChain.java:80) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.StreamTask.lambda$dispatchOperatorEvent$24(StreamTask.java:1609) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.StreamTaskActionExecutor$1.runThrowing(StreamTaskActionExecutor.java:50) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.mailbox.Mail.run(Mail.java:101) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.mailbox.MailboxProcessor.runMail(MailboxProcessor.java:414) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.mailbox.MailboxProcessor.processMailsWhenDefaultActionUnavailable(MailboxProcessor.java:383) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.mailbox.MailboxProcessor.processMail(MailboxProcessor.java:368) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.mailbox.MailboxProcessor.runMailboxLoop(MailboxProcessor.java:229) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.StreamTask.runMailboxLoop(StreamTask.java:973) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.StreamTask.invoke(StreamTask.java:917) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.runtime.taskmanager.Task.runWithSystemExitMonitoring(Task.java:970) [flink-runtime-1.20.0.jar:1.20.0]
	at org.apache.flink.runtime.taskmanager.Task.restoreAndInvoke(Task.java:949) [flink-runtime-1.20.0.jar:1.20.0]
	at org.apache.flink.runtime.taskmanager.Task.doRun(Task.java:763) [flink-runtime-1.20.0.jar:1.20.0]
	at org.apache.flink.runtime.taskmanager.Task.run(Task.java:575) [flink-runtime-1.20.0.jar:1.20.0]
	at java.lang.Thread.run(Thread.java:840) [?:?]
2025-08-20 18:58:25.963 [main] WARN  org.apache.flink.connector.kafka.source.KafkaSourceBuilder - Property auto.offset.reset is provided but will be overridden from earliest to earliest
2025-08-20 18:58:25.980 [main] WARN  org.apache.flink.connector.kafka.source.KafkaSourceBuilder - Property auto.offset.reset is provided but will be overridden from earliest to earliest
2025-08-20 18:58:25.981 [main] WARN  org.apache.flink.connector.kafka.source.KafkaSourceBuilder - Property auto.offset.reset is provided but will be overridden from earliest to earliest
2025-08-20 18:58:26.747 [SourceCoordinator-Source: Kafka-交易] WARN  org.apache.kafka.clients.admin.AdminClientConfig - These configurations '[key.deserializer, value.deserializer, max.poll.records, client.id.prefix, group.id, partition.discovery.interval.ms, heartbeat.interval.ms, enable.auto.commit, fetch.max.wait.ms, fetch.min.bytes, session.timeout.ms, auto.offset.reset]' were supplied but are not used yet.
2025-08-20 18:58:26.747 [SourceCoordinator-Source: Kafka-单腿订单] WARN  org.apache.kafka.clients.admin.AdminClientConfig - These configurations '[key.deserializer, value.deserializer, max.poll.records, client.id.prefix, group.id, partition.discovery.interval.ms, heartbeat.interval.ms, enable.auto.commit, fetch.max.wait.ms, fetch.min.bytes, session.timeout.ms, auto.offset.reset]' were supplied but are not used yet.
2025-08-20 18:58:26.747 [SourceCoordinator-Source: Kafka-组合订单] WARN  org.apache.kafka.clients.admin.AdminClientConfig - These configurations '[key.deserializer, value.deserializer, max.poll.records, client.id.prefix, group.id, partition.discovery.interval.ms, heartbeat.interval.ms, enable.auto.commit, fetch.max.wait.ms, fetch.min.bytes, session.timeout.ms, auto.offset.reset]' were supplied but are not used yet.
2025-08-20 18:58:26.748 [SourceCoordinator-Source: Kafka-交易] INFO  org.apache.flink.connector.kafka.source.enumerator.KafkaSourceEnumerator - Starting the KafkaSourceEnumerator for consumer group orderbook-local-test-group without periodic partition discovery.
2025-08-20 18:58:26.748 [SourceCoordinator-Source: Kafka-单腿订单] WARN  org.apache.kafka.common.utils.AppInfoParser - Error registering AppInfo mbean
javax.management.InstanceAlreadyExistsException: kafka.admin.client:type=app-info,id=orderbook-local-test-group-enumerator-admin-client
	at com.sun.jmx.mbeanserver.Repository.addMBean(Repository.java:436) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerWithRepository(DefaultMBeanServerInterceptor.java:1865) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerDynamicMBean(DefaultMBeanServerInterceptor.java:960) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerObject(DefaultMBeanServerInterceptor.java:895) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerMBean(DefaultMBeanServerInterceptor.java:320) ~[?:?]
	at com.sun.jmx.mbeanserver.JmxMBeanServer.registerMBean(JmxMBeanServer.java:523) ~[?:?]
	at org.apache.kafka.common.utils.AppInfoParser.registerAppInfo(AppInfoParser.java:64) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.admin.KafkaAdminClient.<init>(KafkaAdminClient.java:602) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.admin.KafkaAdminClient.createInternal(KafkaAdminClient.java:544) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.admin.KafkaAdminClient.createInternal(KafkaAdminClient.java:488) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.admin.Admin.create(Admin.java:134) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.admin.AdminClient.create(AdminClient.java:39) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.flink.connector.kafka.source.enumerator.KafkaSourceEnumerator.getKafkaAdminClient(KafkaSourceEnumerator.java:441) ~[flink-connector-kafka-3.4.0-1.20.jar:3.4.0-1.20]
	at org.apache.flink.connector.kafka.source.enumerator.KafkaSourceEnumerator.start(KafkaSourceEnumerator.java:164) ~[flink-connector-kafka-3.4.0-1.20.jar:3.4.0-1.20]
	at org.apache.flink.runtime.source.coordinator.SourceCoordinator.lambda$start$1(SourceCoordinator.java:244) ~[flink-runtime-1.20.0.jar:1.20.0]
	at org.apache.flink.runtime.source.coordinator.SourceCoordinator.lambda$runInEventLoop$10(SourceCoordinator.java:533) ~[flink-runtime-1.20.0.jar:1.20.0]
	at org.apache.flink.util.ThrowableCatchingRunnable.run(ThrowableCatchingRunnable.java:40) [flink-core-1.20.0.jar:1.20.0]
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:539) [?:?]
	at java.util.concurrent.FutureTask.run(FutureTask.java:264) [?:?]
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:304) [?:?]
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136) [?:?]
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635) [?:?]
	at java.lang.Thread.run(Thread.java:840) [?:?]
2025-08-20 18:58:26.751 [SourceCoordinator-Source: Kafka-单腿订单] INFO  org.apache.flink.connector.kafka.source.enumerator.KafkaSourceEnumerator - Starting the KafkaSourceEnumerator for consumer group orderbook-local-test-group without periodic partition discovery.
2025-08-20 18:58:26.751 [SourceCoordinator-Source: Kafka-组合订单] WARN  org.apache.kafka.common.utils.AppInfoParser - Error registering AppInfo mbean
javax.management.InstanceAlreadyExistsException: kafka.admin.client:type=app-info,id=orderbook-local-test-group-enumerator-admin-client
	at com.sun.jmx.mbeanserver.Repository.addMBean(Repository.java:436) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerWithRepository(DefaultMBeanServerInterceptor.java:1865) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerDynamicMBean(DefaultMBeanServerInterceptor.java:960) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerObject(DefaultMBeanServerInterceptor.java:895) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerMBean(DefaultMBeanServerInterceptor.java:320) ~[?:?]
	at com.sun.jmx.mbeanserver.JmxMBeanServer.registerMBean(JmxMBeanServer.java:523) ~[?:?]
	at org.apache.kafka.common.utils.AppInfoParser.registerAppInfo(AppInfoParser.java:64) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.admin.KafkaAdminClient.<init>(KafkaAdminClient.java:602) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.admin.KafkaAdminClient.createInternal(KafkaAdminClient.java:544) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.admin.KafkaAdminClient.createInternal(KafkaAdminClient.java:488) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.admin.Admin.create(Admin.java:134) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.admin.AdminClient.create(AdminClient.java:39) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.flink.connector.kafka.source.enumerator.KafkaSourceEnumerator.getKafkaAdminClient(KafkaSourceEnumerator.java:441) ~[flink-connector-kafka-3.4.0-1.20.jar:3.4.0-1.20]
	at org.apache.flink.connector.kafka.source.enumerator.KafkaSourceEnumerator.start(KafkaSourceEnumerator.java:164) ~[flink-connector-kafka-3.4.0-1.20.jar:3.4.0-1.20]
	at org.apache.flink.runtime.source.coordinator.SourceCoordinator.lambda$start$1(SourceCoordinator.java:244) ~[flink-runtime-1.20.0.jar:1.20.0]
	at org.apache.flink.runtime.source.coordinator.SourceCoordinator.lambda$runInEventLoop$10(SourceCoordinator.java:533) ~[flink-runtime-1.20.0.jar:1.20.0]
	at org.apache.flink.util.ThrowableCatchingRunnable.run(ThrowableCatchingRunnable.java:40) [flink-core-1.20.0.jar:1.20.0]
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:539) [?:?]
	at java.util.concurrent.FutureTask.run(FutureTask.java:264) [?:?]
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:304) [?:?]
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136) [?:?]
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635) [?:?]
	at java.lang.Thread.run(Thread.java:840) [?:?]
2025-08-20 18:58:26.752 [SourceCoordinator-Source: Kafka-组合订单] INFO  org.apache.flink.connector.kafka.source.enumerator.KafkaSourceEnumerator - Starting the KafkaSourceEnumerator for consumer group orderbook-local-test-group without periodic partition discovery.
2025-08-20 18:58:26.858 [SourceCoordinator-Source: Kafka-组合订单] INFO  org.apache.flink.connector.kafka.source.enumerator.KafkaSourceEnumerator - Discovered new partitions: [cmb_order_data_event-0]
2025-08-20 18:58:26.858 [SourceCoordinator-Source: Kafka-交易] INFO  org.apache.flink.connector.kafka.source.enumerator.KafkaSourceEnumerator - Discovered new partitions: [trade_data_event-0]
2025-08-20 18:58:26.858 [SourceCoordinator-Source: Kafka-单腿订单] INFO  org.apache.flink.connector.kafka.source.enumerator.KafkaSourceEnumerator - Discovered new partitions: [singleleg_order_data_event-0]
2025-08-20 18:58:26.874 [SourceCoordinator-Source: Kafka-组合订单] INFO  org.apache.flink.connector.kafka.source.enumerator.KafkaSourceEnumerator - Assigning splits to readers {0=[[Partition: cmb_order_data_event-0, StartingOffset: -2, StoppingOffset: -9223372036854775808]]}
2025-08-20 18:58:26.874 [SourceCoordinator-Source: Kafka-单腿订单] INFO  org.apache.flink.connector.kafka.source.enumerator.KafkaSourceEnumerator - Assigning splits to readers {0=[[Partition: singleleg_order_data_event-0, StartingOffset: -2, StoppingOffset: -9223372036854775808]]}
2025-08-20 18:58:26.874 [SourceCoordinator-Source: Kafka-交易] INFO  org.apache.flink.connector.kafka.source.enumerator.KafkaSourceEnumerator - Assigning splits to readers {0=[[Partition: trade_data_event-0, StartingOffset: -2, StoppingOffset: -9223372036854775808]]}
2025-08-20 18:58:26.903 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] WARN  org.apache.kafka.clients.consumer.ConsumerConfig - These configurations '[client.id.prefix, partition.discovery.interval.ms]' were supplied but are not used yet.
2025-08-20 18:58:26.903 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] WARN  org.apache.kafka.clients.consumer.ConsumerConfig - These configurations '[client.id.prefix, partition.discovery.interval.ms]' were supplied but are not used yet.
2025-08-20 18:58:26.903 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] WARN  org.apache.kafka.clients.consumer.ConsumerConfig - These configurations '[client.id.prefix, partition.discovery.interval.ms]' were supplied but are not used yet.
2025-08-20 18:58:26.903 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] WARN  org.apache.kafka.common.utils.AppInfoParser - Error registering AppInfo mbean
javax.management.InstanceAlreadyExistsException: kafka.consumer:type=app-info,id=orderbook-local-test-group-0
	at com.sun.jmx.mbeanserver.Repository.addMBean(Repository.java:436) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerWithRepository(DefaultMBeanServerInterceptor.java:1865) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerDynamicMBean(DefaultMBeanServerInterceptor.java:960) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerObject(DefaultMBeanServerInterceptor.java:895) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerMBean(DefaultMBeanServerInterceptor.java:320) ~[?:?]
	at com.sun.jmx.mbeanserver.JmxMBeanServer.registerMBean(JmxMBeanServer.java:523) ~[?:?]
	at org.apache.kafka.common.utils.AppInfoParser.registerAppInfo(AppInfoParser.java:64) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.consumer.KafkaConsumer.<init>(KafkaConsumer.java:821) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.consumer.KafkaConsumer.<init>(KafkaConsumer.java:665) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.consumer.KafkaConsumer.<init>(KafkaConsumer.java:646) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.consumer.KafkaConsumer.<init>(KafkaConsumer.java:626) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.flink.connector.kafka.source.reader.KafkaPartitionSplitReader.<init>(KafkaPartitionSplitReader.java:97) ~[flink-connector-kafka-3.4.0-1.20.jar:3.4.0-1.20]
	at org.apache.flink.connector.kafka.source.KafkaSource.lambda$createReader$1(KafkaSource.java:185) ~[flink-connector-kafka-3.4.0-1.20.jar:3.4.0-1.20]
	at org.apache.flink.connector.base.source.reader.fetcher.SplitFetcherManager.createSplitFetcher(SplitFetcherManager.java:259) ~[flink-connector-base-1.20.0.jar:1.20.0]
	at org.apache.flink.connector.base.source.reader.fetcher.SingleThreadFetcherManager.addSplits(SingleThreadFetcherManager.java:148) ~[flink-connector-base-1.20.0.jar:1.20.0]
	at org.apache.flink.connector.base.source.reader.SourceReaderBase.addSplits(SourceReaderBase.java:315) ~[flink-connector-base-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.api.operators.SourceOperator.handleAddSplitsEvent(SourceOperator.java:606) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.api.operators.SourceOperator.handleOperatorEvent(SourceOperator.java:575) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.OperatorEventDispatcherImpl.dispatchEventToHandlers(OperatorEventDispatcherImpl.java:72) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.RegularOperatorChain.dispatchOperatorEvent(RegularOperatorChain.java:80) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.StreamTask.lambda$dispatchOperatorEvent$24(StreamTask.java:1609) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.StreamTaskActionExecutor$1.runThrowing(StreamTaskActionExecutor.java:50) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.mailbox.Mail.run(Mail.java:101) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.mailbox.MailboxProcessor.runMail(MailboxProcessor.java:414) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.mailbox.MailboxProcessor.processMailsWhenDefaultActionUnavailable(MailboxProcessor.java:383) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.mailbox.MailboxProcessor.processMail(MailboxProcessor.java:368) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.mailbox.MailboxProcessor.runMailboxLoop(MailboxProcessor.java:229) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.StreamTask.runMailboxLoop(StreamTask.java:973) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.StreamTask.invoke(StreamTask.java:917) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.runtime.taskmanager.Task.runWithSystemExitMonitoring(Task.java:970) [flink-runtime-1.20.0.jar:1.20.0]
	at org.apache.flink.runtime.taskmanager.Task.restoreAndInvoke(Task.java:949) [flink-runtime-1.20.0.jar:1.20.0]
	at org.apache.flink.runtime.taskmanager.Task.doRun(Task.java:763) [flink-runtime-1.20.0.jar:1.20.0]
	at org.apache.flink.runtime.taskmanager.Task.run(Task.java:575) [flink-runtime-1.20.0.jar:1.20.0]
	at java.lang.Thread.run(Thread.java:840) [?:?]
2025-08-20 18:58:26.904 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] WARN  org.apache.kafka.common.utils.AppInfoParser - Error registering AppInfo mbean
javax.management.InstanceAlreadyExistsException: kafka.consumer:type=app-info,id=orderbook-local-test-group-0
	at com.sun.jmx.mbeanserver.Repository.addMBean(Repository.java:436) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerWithRepository(DefaultMBeanServerInterceptor.java:1865) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerDynamicMBean(DefaultMBeanServerInterceptor.java:960) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerObject(DefaultMBeanServerInterceptor.java:895) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerMBean(DefaultMBeanServerInterceptor.java:320) ~[?:?]
	at com.sun.jmx.mbeanserver.JmxMBeanServer.registerMBean(JmxMBeanServer.java:523) ~[?:?]
	at org.apache.kafka.common.utils.AppInfoParser.registerAppInfo(AppInfoParser.java:64) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.consumer.KafkaConsumer.<init>(KafkaConsumer.java:821) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.consumer.KafkaConsumer.<init>(KafkaConsumer.java:665) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.consumer.KafkaConsumer.<init>(KafkaConsumer.java:646) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.consumer.KafkaConsumer.<init>(KafkaConsumer.java:626) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.flink.connector.kafka.source.reader.KafkaPartitionSplitReader.<init>(KafkaPartitionSplitReader.java:97) ~[flink-connector-kafka-3.4.0-1.20.jar:3.4.0-1.20]
	at org.apache.flink.connector.kafka.source.KafkaSource.lambda$createReader$1(KafkaSource.java:185) ~[flink-connector-kafka-3.4.0-1.20.jar:3.4.0-1.20]
	at org.apache.flink.connector.base.source.reader.fetcher.SplitFetcherManager.createSplitFetcher(SplitFetcherManager.java:259) ~[flink-connector-base-1.20.0.jar:1.20.0]
	at org.apache.flink.connector.base.source.reader.fetcher.SingleThreadFetcherManager.addSplits(SingleThreadFetcherManager.java:148) ~[flink-connector-base-1.20.0.jar:1.20.0]
	at org.apache.flink.connector.base.source.reader.SourceReaderBase.addSplits(SourceReaderBase.java:315) ~[flink-connector-base-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.api.operators.SourceOperator.handleAddSplitsEvent(SourceOperator.java:606) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.api.operators.SourceOperator.handleOperatorEvent(SourceOperator.java:575) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.OperatorEventDispatcherImpl.dispatchEventToHandlers(OperatorEventDispatcherImpl.java:72) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.RegularOperatorChain.dispatchOperatorEvent(RegularOperatorChain.java:80) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.StreamTask.lambda$dispatchOperatorEvent$24(StreamTask.java:1609) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.StreamTaskActionExecutor$1.runThrowing(StreamTaskActionExecutor.java:50) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.mailbox.Mail.run(Mail.java:101) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.mailbox.MailboxProcessor.runMail(MailboxProcessor.java:414) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.mailbox.MailboxProcessor.processMailsWhenDefaultActionUnavailable(MailboxProcessor.java:383) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.mailbox.MailboxProcessor.processMail(MailboxProcessor.java:368) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.mailbox.MailboxProcessor.runMailboxLoop(MailboxProcessor.java:229) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.StreamTask.runMailboxLoop(StreamTask.java:973) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.StreamTask.invoke(StreamTask.java:917) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.runtime.taskmanager.Task.runWithSystemExitMonitoring(Task.java:970) [flink-runtime-1.20.0.jar:1.20.0]
	at org.apache.flink.runtime.taskmanager.Task.restoreAndInvoke(Task.java:949) [flink-runtime-1.20.0.jar:1.20.0]
	at org.apache.flink.runtime.taskmanager.Task.doRun(Task.java:763) [flink-runtime-1.20.0.jar:1.20.0]
	at org.apache.flink.runtime.taskmanager.Task.run(Task.java:575) [flink-runtime-1.20.0.jar:1.20.0]
	at java.lang.Thread.run(Thread.java:840) [?:?]
2025-08-20 19:01:59.118 [com.futures.job.FuturesOrderBookKafkaJob.main()] WARN  org.apache.flink.connector.kafka.source.KafkaSourceBuilder - Property auto.offset.reset is provided but will be overridden from earliest to earliest
2025-08-20 19:01:59.140 [com.futures.job.FuturesOrderBookKafkaJob.main()] WARN  org.apache.flink.connector.kafka.source.KafkaSourceBuilder - Property auto.offset.reset is provided but will be overridden from earliest to earliest
2025-08-20 19:01:59.141 [com.futures.job.FuturesOrderBookKafkaJob.main()] WARN  org.apache.flink.connector.kafka.source.KafkaSourceBuilder - Property auto.offset.reset is provided but will be overridden from earliest to earliest
2025-08-20 19:02:15.924 [com.futures.job.FuturesOrderBookKafkaJob.main()] WARN  org.apache.flink.connector.kafka.source.KafkaSourceBuilder - Property auto.offset.reset is provided but will be overridden from earliest to earliest
2025-08-20 19:02:15.945 [com.futures.job.FuturesOrderBookKafkaJob.main()] WARN  org.apache.flink.connector.kafka.source.KafkaSourceBuilder - Property auto.offset.reset is provided but will be overridden from earliest to earliest
2025-08-20 19:02:15.946 [com.futures.job.FuturesOrderBookKafkaJob.main()] WARN  org.apache.flink.connector.kafka.source.KafkaSourceBuilder - Property auto.offset.reset is provided but will be overridden from earliest to earliest
2025-08-20 19:10:19.679 [com.futures.job.FuturesOrderBookKafkaJob.main()] WARN  org.apache.flink.connector.kafka.source.KafkaSourceBuilder - Property auto.offset.reset is provided but will be overridden from earliest to earliest
2025-08-20 19:10:19.700 [com.futures.job.FuturesOrderBookKafkaJob.main()] WARN  org.apache.flink.connector.kafka.source.KafkaSourceBuilder - Property auto.offset.reset is provided but will be overridden from earliest to earliest
2025-08-20 19:10:19.701 [com.futures.job.FuturesOrderBookKafkaJob.main()] WARN  org.apache.flink.connector.kafka.source.KafkaSourceBuilder - Property auto.offset.reset is provided but will be overridden from earliest to earliest
2025-08-20 19:46:12.363 [main] WARN  org.apache.flink.connector.kafka.source.KafkaSourceBuilder - Property auto.offset.reset is provided but will be overridden from earliest to earliest
2025-08-20 19:46:12.382 [main] WARN  org.apache.flink.connector.kafka.source.KafkaSourceBuilder - Property auto.offset.reset is provided but will be overridden from earliest to earliest
2025-08-20 19:46:12.382 [main] WARN  org.apache.flink.connector.kafka.source.KafkaSourceBuilder - Property auto.offset.reset is provided but will be overridden from earliest to earliest
2025-08-20 19:46:13.114 [SourceCoordinator-Source: Kafka-单腿订单] WARN  org.apache.kafka.clients.admin.AdminClientConfig - These configurations '[key.deserializer, value.deserializer, max.poll.records, client.id.prefix, group.id, partition.discovery.interval.ms, heartbeat.interval.ms, enable.auto.commit, fetch.max.wait.ms, fetch.min.bytes, session.timeout.ms, auto.offset.reset]' were supplied but are not used yet.
2025-08-20 19:46:13.114 [SourceCoordinator-Source: Kafka-交易] WARN  org.apache.kafka.clients.admin.AdminClientConfig - These configurations '[key.deserializer, value.deserializer, max.poll.records, client.id.prefix, group.id, partition.discovery.interval.ms, heartbeat.interval.ms, enable.auto.commit, fetch.max.wait.ms, fetch.min.bytes, session.timeout.ms, auto.offset.reset]' were supplied but are not used yet.
2025-08-20 19:46:13.114 [SourceCoordinator-Source: Kafka-组合订单] WARN  org.apache.kafka.clients.admin.AdminClientConfig - These configurations '[key.deserializer, value.deserializer, max.poll.records, client.id.prefix, group.id, partition.discovery.interval.ms, heartbeat.interval.ms, enable.auto.commit, fetch.max.wait.ms, fetch.min.bytes, session.timeout.ms, auto.offset.reset]' were supplied but are not used yet.
2025-08-20 19:46:13.115 [SourceCoordinator-Source: Kafka-单腿订单] INFO  org.apache.flink.connector.kafka.source.enumerator.KafkaSourceEnumerator - Starting the KafkaSourceEnumerator for consumer group orderbook-local-test-group without periodic partition discovery.
2025-08-20 19:46:13.116 [SourceCoordinator-Source: Kafka-组合订单] WARN  org.apache.kafka.common.utils.AppInfoParser - Error registering AppInfo mbean
javax.management.InstanceAlreadyExistsException: kafka.admin.client:type=app-info,id=orderbook-local-test-group-enumerator-admin-client
	at com.sun.jmx.mbeanserver.Repository.addMBean(Repository.java:436) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerWithRepository(DefaultMBeanServerInterceptor.java:1865) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerDynamicMBean(DefaultMBeanServerInterceptor.java:960) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerObject(DefaultMBeanServerInterceptor.java:895) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerMBean(DefaultMBeanServerInterceptor.java:320) ~[?:?]
	at com.sun.jmx.mbeanserver.JmxMBeanServer.registerMBean(JmxMBeanServer.java:523) ~[?:?]
	at org.apache.kafka.common.utils.AppInfoParser.registerAppInfo(AppInfoParser.java:64) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.admin.KafkaAdminClient.<init>(KafkaAdminClient.java:602) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.admin.KafkaAdminClient.createInternal(KafkaAdminClient.java:544) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.admin.KafkaAdminClient.createInternal(KafkaAdminClient.java:488) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.admin.Admin.create(Admin.java:134) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.admin.AdminClient.create(AdminClient.java:39) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.flink.connector.kafka.source.enumerator.KafkaSourceEnumerator.getKafkaAdminClient(KafkaSourceEnumerator.java:441) ~[flink-connector-kafka-3.4.0-1.20.jar:3.4.0-1.20]
	at org.apache.flink.connector.kafka.source.enumerator.KafkaSourceEnumerator.start(KafkaSourceEnumerator.java:164) ~[flink-connector-kafka-3.4.0-1.20.jar:3.4.0-1.20]
	at org.apache.flink.runtime.source.coordinator.SourceCoordinator.lambda$start$1(SourceCoordinator.java:244) ~[flink-runtime-1.20.0.jar:1.20.0]
	at org.apache.flink.runtime.source.coordinator.SourceCoordinator.lambda$runInEventLoop$10(SourceCoordinator.java:533) ~[flink-runtime-1.20.0.jar:1.20.0]
	at org.apache.flink.util.ThrowableCatchingRunnable.run(ThrowableCatchingRunnable.java:40) [flink-core-1.20.0.jar:1.20.0]
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:539) [?:?]
	at java.util.concurrent.FutureTask.run(FutureTask.java:264) [?:?]
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:304) [?:?]
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136) [?:?]
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635) [?:?]
	at java.lang.Thread.run(Thread.java:840) [?:?]
2025-08-20 19:46:13.119 [SourceCoordinator-Source: Kafka-组合订单] INFO  org.apache.flink.connector.kafka.source.enumerator.KafkaSourceEnumerator - Starting the KafkaSourceEnumerator for consumer group orderbook-local-test-group without periodic partition discovery.
2025-08-20 19:46:13.119 [SourceCoordinator-Source: Kafka-交易] WARN  org.apache.kafka.common.utils.AppInfoParser - Error registering AppInfo mbean
javax.management.InstanceAlreadyExistsException: kafka.admin.client:type=app-info,id=orderbook-local-test-group-enumerator-admin-client
	at com.sun.jmx.mbeanserver.Repository.addMBean(Repository.java:436) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerWithRepository(DefaultMBeanServerInterceptor.java:1865) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerDynamicMBean(DefaultMBeanServerInterceptor.java:960) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerObject(DefaultMBeanServerInterceptor.java:895) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerMBean(DefaultMBeanServerInterceptor.java:320) ~[?:?]
	at com.sun.jmx.mbeanserver.JmxMBeanServer.registerMBean(JmxMBeanServer.java:523) ~[?:?]
	at org.apache.kafka.common.utils.AppInfoParser.registerAppInfo(AppInfoParser.java:64) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.admin.KafkaAdminClient.<init>(KafkaAdminClient.java:602) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.admin.KafkaAdminClient.createInternal(KafkaAdminClient.java:544) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.admin.KafkaAdminClient.createInternal(KafkaAdminClient.java:488) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.admin.Admin.create(Admin.java:134) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.admin.AdminClient.create(AdminClient.java:39) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.flink.connector.kafka.source.enumerator.KafkaSourceEnumerator.getKafkaAdminClient(KafkaSourceEnumerator.java:441) ~[flink-connector-kafka-3.4.0-1.20.jar:3.4.0-1.20]
	at org.apache.flink.connector.kafka.source.enumerator.KafkaSourceEnumerator.start(KafkaSourceEnumerator.java:164) ~[flink-connector-kafka-3.4.0-1.20.jar:3.4.0-1.20]
	at org.apache.flink.runtime.source.coordinator.SourceCoordinator.lambda$start$1(SourceCoordinator.java:244) ~[flink-runtime-1.20.0.jar:1.20.0]
	at org.apache.flink.runtime.source.coordinator.SourceCoordinator.lambda$runInEventLoop$10(SourceCoordinator.java:533) ~[flink-runtime-1.20.0.jar:1.20.0]
	at org.apache.flink.util.ThrowableCatchingRunnable.run(ThrowableCatchingRunnable.java:40) [flink-core-1.20.0.jar:1.20.0]
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:539) [?:?]
	at java.util.concurrent.FutureTask.run(FutureTask.java:264) [?:?]
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:304) [?:?]
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136) [?:?]
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635) [?:?]
	at java.lang.Thread.run(Thread.java:840) [?:?]
2025-08-20 19:46:13.120 [SourceCoordinator-Source: Kafka-交易] INFO  org.apache.flink.connector.kafka.source.enumerator.KafkaSourceEnumerator - Starting the KafkaSourceEnumerator for consumer group orderbook-local-test-group without periodic partition discovery.
2025-08-20 19:46:13.237 [SourceCoordinator-Source: Kafka-交易] INFO  org.apache.flink.connector.kafka.source.enumerator.KafkaSourceEnumerator - Discovered new partitions: [trade_data_event-0]
2025-08-20 19:46:13.237 [SourceCoordinator-Source: Kafka-组合订单] INFO  org.apache.flink.connector.kafka.source.enumerator.KafkaSourceEnumerator - Discovered new partitions: [cmb_order_data_event-0]
2025-08-20 19:46:13.237 [SourceCoordinator-Source: Kafka-单腿订单] INFO  org.apache.flink.connector.kafka.source.enumerator.KafkaSourceEnumerator - Discovered new partitions: [singleleg_order_data_event-0]
2025-08-20 19:46:13.242 [SourceCoordinator-Source: Kafka-交易] INFO  org.apache.flink.connector.kafka.source.enumerator.KafkaSourceEnumerator - Assigning splits to readers {0=[[Partition: trade_data_event-0, StartingOffset: -2, StoppingOffset: -9223372036854775808]]}
2025-08-20 19:46:13.242 [SourceCoordinator-Source: Kafka-单腿订单] INFO  org.apache.flink.connector.kafka.source.enumerator.KafkaSourceEnumerator - Assigning splits to readers {0=[[Partition: singleleg_order_data_event-0, StartingOffset: -2, StoppingOffset: -9223372036854775808]]}
2025-08-20 19:46:13.242 [SourceCoordinator-Source: Kafka-组合订单] INFO  org.apache.flink.connector.kafka.source.enumerator.KafkaSourceEnumerator - Assigning splits to readers {0=[[Partition: cmb_order_data_event-0, StartingOffset: -2, StoppingOffset: -9223372036854775808]]}
2025-08-20 19:46:13.273 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 (1/1)#0] WARN  org.apache.kafka.clients.consumer.ConsumerConfig - These configurations '[client.id.prefix, partition.discovery.interval.ms]' were supplied but are not used yet.
2025-08-20 19:46:13.273 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] WARN  org.apache.kafka.clients.consumer.ConsumerConfig - These configurations '[client.id.prefix, partition.discovery.interval.ms]' were supplied but are not used yet.
2025-08-20 19:46:13.273 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 (1/1)#0] WARN  org.apache.kafka.clients.consumer.ConsumerConfig - These configurations '[client.id.prefix, partition.discovery.interval.ms]' were supplied but are not used yet.
2025-08-20 19:46:13.273 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 (1/1)#0] WARN  org.apache.kafka.common.utils.AppInfoParser - Error registering AppInfo mbean
javax.management.InstanceAlreadyExistsException: kafka.consumer:type=app-info,id=orderbook-local-test-group-0
	at com.sun.jmx.mbeanserver.Repository.addMBean(Repository.java:436) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerWithRepository(DefaultMBeanServerInterceptor.java:1865) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerDynamicMBean(DefaultMBeanServerInterceptor.java:960) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerObject(DefaultMBeanServerInterceptor.java:895) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerMBean(DefaultMBeanServerInterceptor.java:320) ~[?:?]
	at com.sun.jmx.mbeanserver.JmxMBeanServer.registerMBean(JmxMBeanServer.java:523) ~[?:?]
	at org.apache.kafka.common.utils.AppInfoParser.registerAppInfo(AppInfoParser.java:64) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.consumer.KafkaConsumer.<init>(KafkaConsumer.java:821) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.consumer.KafkaConsumer.<init>(KafkaConsumer.java:665) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.consumer.KafkaConsumer.<init>(KafkaConsumer.java:646) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.consumer.KafkaConsumer.<init>(KafkaConsumer.java:626) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.flink.connector.kafka.source.reader.KafkaPartitionSplitReader.<init>(KafkaPartitionSplitReader.java:97) ~[flink-connector-kafka-3.4.0-1.20.jar:3.4.0-1.20]
	at org.apache.flink.connector.kafka.source.KafkaSource.lambda$createReader$1(KafkaSource.java:185) ~[flink-connector-kafka-3.4.0-1.20.jar:3.4.0-1.20]
	at org.apache.flink.connector.base.source.reader.fetcher.SplitFetcherManager.createSplitFetcher(SplitFetcherManager.java:259) ~[flink-connector-base-1.20.0.jar:1.20.0]
	at org.apache.flink.connector.base.source.reader.fetcher.SingleThreadFetcherManager.addSplits(SingleThreadFetcherManager.java:148) ~[flink-connector-base-1.20.0.jar:1.20.0]
	at org.apache.flink.connector.base.source.reader.SourceReaderBase.addSplits(SourceReaderBase.java:315) ~[flink-connector-base-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.api.operators.SourceOperator.handleAddSplitsEvent(SourceOperator.java:606) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.api.operators.SourceOperator.handleOperatorEvent(SourceOperator.java:575) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.OperatorEventDispatcherImpl.dispatchEventToHandlers(OperatorEventDispatcherImpl.java:72) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.RegularOperatorChain.dispatchOperatorEvent(RegularOperatorChain.java:80) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.StreamTask.lambda$dispatchOperatorEvent$24(StreamTask.java:1609) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.StreamTaskActionExecutor$1.runThrowing(StreamTaskActionExecutor.java:50) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.mailbox.Mail.run(Mail.java:101) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.mailbox.MailboxProcessor.runMail(MailboxProcessor.java:414) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.mailbox.MailboxProcessor.processMailsWhenDefaultActionUnavailable(MailboxProcessor.java:383) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.mailbox.MailboxProcessor.processMail(MailboxProcessor.java:368) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.mailbox.MailboxProcessor.runMailboxLoop(MailboxProcessor.java:229) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.StreamTask.runMailboxLoop(StreamTask.java:973) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.StreamTask.invoke(StreamTask.java:917) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.runtime.taskmanager.Task.runWithSystemExitMonitoring(Task.java:970) [flink-runtime-1.20.0.jar:1.20.0]
	at org.apache.flink.runtime.taskmanager.Task.restoreAndInvoke(Task.java:949) [flink-runtime-1.20.0.jar:1.20.0]
	at org.apache.flink.runtime.taskmanager.Task.doRun(Task.java:763) [flink-runtime-1.20.0.jar:1.20.0]
	at org.apache.flink.runtime.taskmanager.Task.run(Task.java:575) [flink-runtime-1.20.0.jar:1.20.0]
	at java.lang.Thread.run(Thread.java:840) [?:?]
2025-08-20 19:46:13.274 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] WARN  org.apache.kafka.common.utils.AppInfoParser - Error registering AppInfo mbean
javax.management.InstanceAlreadyExistsException: kafka.consumer:type=app-info,id=orderbook-local-test-group-0
	at com.sun.jmx.mbeanserver.Repository.addMBean(Repository.java:436) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerWithRepository(DefaultMBeanServerInterceptor.java:1865) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerDynamicMBean(DefaultMBeanServerInterceptor.java:960) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerObject(DefaultMBeanServerInterceptor.java:895) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerMBean(DefaultMBeanServerInterceptor.java:320) ~[?:?]
	at com.sun.jmx.mbeanserver.JmxMBeanServer.registerMBean(JmxMBeanServer.java:523) ~[?:?]
	at org.apache.kafka.common.utils.AppInfoParser.registerAppInfo(AppInfoParser.java:64) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.consumer.KafkaConsumer.<init>(KafkaConsumer.java:821) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.consumer.KafkaConsumer.<init>(KafkaConsumer.java:665) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.consumer.KafkaConsumer.<init>(KafkaConsumer.java:646) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.consumer.KafkaConsumer.<init>(KafkaConsumer.java:626) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.flink.connector.kafka.source.reader.KafkaPartitionSplitReader.<init>(KafkaPartitionSplitReader.java:97) ~[flink-connector-kafka-3.4.0-1.20.jar:3.4.0-1.20]
	at org.apache.flink.connector.kafka.source.KafkaSource.lambda$createReader$1(KafkaSource.java:185) ~[flink-connector-kafka-3.4.0-1.20.jar:3.4.0-1.20]
	at org.apache.flink.connector.base.source.reader.fetcher.SplitFetcherManager.createSplitFetcher(SplitFetcherManager.java:259) ~[flink-connector-base-1.20.0.jar:1.20.0]
	at org.apache.flink.connector.base.source.reader.fetcher.SingleThreadFetcherManager.addSplits(SingleThreadFetcherManager.java:148) ~[flink-connector-base-1.20.0.jar:1.20.0]
	at org.apache.flink.connector.base.source.reader.SourceReaderBase.addSplits(SourceReaderBase.java:315) ~[flink-connector-base-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.api.operators.SourceOperator.handleAddSplitsEvent(SourceOperator.java:606) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.api.operators.SourceOperator.handleOperatorEvent(SourceOperator.java:575) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.OperatorEventDispatcherImpl.dispatchEventToHandlers(OperatorEventDispatcherImpl.java:72) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.RegularOperatorChain.dispatchOperatorEvent(RegularOperatorChain.java:80) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.StreamTask.lambda$dispatchOperatorEvent$24(StreamTask.java:1609) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.StreamTaskActionExecutor$1.runThrowing(StreamTaskActionExecutor.java:50) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.mailbox.Mail.run(Mail.java:101) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.mailbox.MailboxProcessor.runMail(MailboxProcessor.java:414) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.mailbox.MailboxProcessor.processMailsWhenDefaultActionUnavailable(MailboxProcessor.java:383) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.mailbox.MailboxProcessor.processMail(MailboxProcessor.java:368) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.mailbox.MailboxProcessor.runMailboxLoop(MailboxProcessor.java:229) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.StreamTask.runMailboxLoop(StreamTask.java:973) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.StreamTask.invoke(StreamTask.java:917) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.runtime.taskmanager.Task.runWithSystemExitMonitoring(Task.java:970) [flink-runtime-1.20.0.jar:1.20.0]
	at org.apache.flink.runtime.taskmanager.Task.restoreAndInvoke(Task.java:949) [flink-runtime-1.20.0.jar:1.20.0]
	at org.apache.flink.runtime.taskmanager.Task.doRun(Task.java:763) [flink-runtime-1.20.0.jar:1.20.0]
	at org.apache.flink.runtime.taskmanager.Task.run(Task.java:575) [flink-runtime-1.20.0.jar:1.20.0]
	at java.lang.Thread.run(Thread.java:840) [?:?]
2025-08-20 19:46:40.055 [com.futures.job.FuturesOrderBookKafkaJob.main()] WARN  org.apache.flink.connector.kafka.source.KafkaSourceBuilder - Property auto.offset.reset is provided but will be overridden from earliest to earliest
2025-08-20 19:46:40.077 [com.futures.job.FuturesOrderBookKafkaJob.main()] WARN  org.apache.flink.connector.kafka.source.KafkaSourceBuilder - Property auto.offset.reset is provided but will be overridden from earliest to earliest
2025-08-20 19:46:40.078 [com.futures.job.FuturesOrderBookKafkaJob.main()] WARN  org.apache.flink.connector.kafka.source.KafkaSourceBuilder - Property auto.offset.reset is provided but will be overridden from earliest to earliest
2025-08-20 19:46:50.633 [com.futures.job.FuturesOrderBookKafkaJob.main()] WARN  org.apache.flink.connector.kafka.source.KafkaSourceBuilder - Property auto.offset.reset is provided but will be overridden from earliest to earliest
2025-08-20 19:46:50.655 [com.futures.job.FuturesOrderBookKafkaJob.main()] WARN  org.apache.flink.connector.kafka.source.KafkaSourceBuilder - Property auto.offset.reset is provided but will be overridden from earliest to earliest
2025-08-20 19:46:50.655 [com.futures.job.FuturesOrderBookKafkaJob.main()] WARN  org.apache.flink.connector.kafka.source.KafkaSourceBuilder - Property auto.offset.reset is provided but will be overridden from earliest to earliest
2025-08-20 19:48:40.655 [main] WARN  org.apache.flink.connector.kafka.source.KafkaSourceBuilder - Property auto.offset.reset is provided but will be overridden from earliest to earliest
2025-08-20 19:48:40.672 [main] WARN  org.apache.flink.connector.kafka.source.KafkaSourceBuilder - Property auto.offset.reset is provided but will be overridden from earliest to earliest
2025-08-20 19:48:40.672 [main] WARN  org.apache.flink.connector.kafka.source.KafkaSourceBuilder - Property auto.offset.reset is provided but will be overridden from earliest to earliest
2025-08-20 19:48:41.370 [SourceCoordinator-Source: Kafka-交易] WARN  org.apache.kafka.clients.admin.AdminClientConfig - These configurations '[key.deserializer, value.deserializer, max.poll.records, client.id.prefix, group.id, partition.discovery.interval.ms, heartbeat.interval.ms, enable.auto.commit, fetch.max.wait.ms, fetch.min.bytes, session.timeout.ms, auto.offset.reset]' were supplied but are not used yet.
2025-08-20 19:48:41.370 [SourceCoordinator-Source: Kafka-组合订单] WARN  org.apache.kafka.clients.admin.AdminClientConfig - These configurations '[key.deserializer, value.deserializer, max.poll.records, client.id.prefix, group.id, partition.discovery.interval.ms, heartbeat.interval.ms, enable.auto.commit, fetch.max.wait.ms, fetch.min.bytes, session.timeout.ms, auto.offset.reset]' were supplied but are not used yet.
2025-08-20 19:48:41.370 [SourceCoordinator-Source: Kafka-单腿订单] WARN  org.apache.kafka.clients.admin.AdminClientConfig - These configurations '[key.deserializer, value.deserializer, max.poll.records, client.id.prefix, group.id, partition.discovery.interval.ms, heartbeat.interval.ms, enable.auto.commit, fetch.max.wait.ms, fetch.min.bytes, session.timeout.ms, auto.offset.reset]' were supplied but are not used yet.
2025-08-20 19:48:41.372 [SourceCoordinator-Source: Kafka-交易] INFO  org.apache.flink.connector.kafka.source.enumerator.KafkaSourceEnumerator - Starting the KafkaSourceEnumerator for consumer group orderbook-local-test-group without periodic partition discovery.
2025-08-20 19:48:41.372 [SourceCoordinator-Source: Kafka-单腿订单] WARN  org.apache.kafka.common.utils.AppInfoParser - Error registering AppInfo mbean
javax.management.InstanceAlreadyExistsException: kafka.admin.client:type=app-info,id=orderbook-local-test-group-enumerator-admin-client
	at com.sun.jmx.mbeanserver.Repository.addMBean(Repository.java:436) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerWithRepository(DefaultMBeanServerInterceptor.java:1865) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerDynamicMBean(DefaultMBeanServerInterceptor.java:960) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerObject(DefaultMBeanServerInterceptor.java:895) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerMBean(DefaultMBeanServerInterceptor.java:320) ~[?:?]
	at com.sun.jmx.mbeanserver.JmxMBeanServer.registerMBean(JmxMBeanServer.java:523) ~[?:?]
	at org.apache.kafka.common.utils.AppInfoParser.registerAppInfo(AppInfoParser.java:64) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.admin.KafkaAdminClient.<init>(KafkaAdminClient.java:602) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.admin.KafkaAdminClient.createInternal(KafkaAdminClient.java:544) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.admin.KafkaAdminClient.createInternal(KafkaAdminClient.java:488) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.admin.Admin.create(Admin.java:134) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.admin.AdminClient.create(AdminClient.java:39) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.flink.connector.kafka.source.enumerator.KafkaSourceEnumerator.getKafkaAdminClient(KafkaSourceEnumerator.java:441) ~[flink-connector-kafka-3.4.0-1.20.jar:3.4.0-1.20]
	at org.apache.flink.connector.kafka.source.enumerator.KafkaSourceEnumerator.start(KafkaSourceEnumerator.java:164) ~[flink-connector-kafka-3.4.0-1.20.jar:3.4.0-1.20]
	at org.apache.flink.runtime.source.coordinator.SourceCoordinator.lambda$start$1(SourceCoordinator.java:244) ~[flink-runtime-1.20.0.jar:1.20.0]
	at org.apache.flink.runtime.source.coordinator.SourceCoordinator.lambda$runInEventLoop$10(SourceCoordinator.java:533) ~[flink-runtime-1.20.0.jar:1.20.0]
	at org.apache.flink.util.ThrowableCatchingRunnable.run(ThrowableCatchingRunnable.java:40) [flink-core-1.20.0.jar:1.20.0]
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:539) [?:?]
	at java.util.concurrent.FutureTask.run(FutureTask.java:264) [?:?]
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:304) [?:?]
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136) [?:?]
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635) [?:?]
	at java.lang.Thread.run(Thread.java:840) [?:?]
2025-08-20 19:48:41.376 [SourceCoordinator-Source: Kafka-单腿订单] INFO  org.apache.flink.connector.kafka.source.enumerator.KafkaSourceEnumerator - Starting the KafkaSourceEnumerator for consumer group orderbook-local-test-group without periodic partition discovery.
2025-08-20 19:48:41.376 [SourceCoordinator-Source: Kafka-组合订单] WARN  org.apache.kafka.common.utils.AppInfoParser - Error registering AppInfo mbean
javax.management.InstanceAlreadyExistsException: kafka.admin.client:type=app-info,id=orderbook-local-test-group-enumerator-admin-client
	at com.sun.jmx.mbeanserver.Repository.addMBean(Repository.java:436) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerWithRepository(DefaultMBeanServerInterceptor.java:1865) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerDynamicMBean(DefaultMBeanServerInterceptor.java:960) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerObject(DefaultMBeanServerInterceptor.java:895) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerMBean(DefaultMBeanServerInterceptor.java:320) ~[?:?]
	at com.sun.jmx.mbeanserver.JmxMBeanServer.registerMBean(JmxMBeanServer.java:523) ~[?:?]
	at org.apache.kafka.common.utils.AppInfoParser.registerAppInfo(AppInfoParser.java:64) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.admin.KafkaAdminClient.<init>(KafkaAdminClient.java:602) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.admin.KafkaAdminClient.createInternal(KafkaAdminClient.java:544) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.admin.KafkaAdminClient.createInternal(KafkaAdminClient.java:488) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.admin.Admin.create(Admin.java:134) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.admin.AdminClient.create(AdminClient.java:39) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.flink.connector.kafka.source.enumerator.KafkaSourceEnumerator.getKafkaAdminClient(KafkaSourceEnumerator.java:441) ~[flink-connector-kafka-3.4.0-1.20.jar:3.4.0-1.20]
	at org.apache.flink.connector.kafka.source.enumerator.KafkaSourceEnumerator.start(KafkaSourceEnumerator.java:164) ~[flink-connector-kafka-3.4.0-1.20.jar:3.4.0-1.20]
	at org.apache.flink.runtime.source.coordinator.SourceCoordinator.lambda$start$1(SourceCoordinator.java:244) ~[flink-runtime-1.20.0.jar:1.20.0]
	at org.apache.flink.runtime.source.coordinator.SourceCoordinator.lambda$runInEventLoop$10(SourceCoordinator.java:533) ~[flink-runtime-1.20.0.jar:1.20.0]
	at org.apache.flink.util.ThrowableCatchingRunnable.run(ThrowableCatchingRunnable.java:40) [flink-core-1.20.0.jar:1.20.0]
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:539) [?:?]
	at java.util.concurrent.FutureTask.run(FutureTask.java:264) [?:?]
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:304) [?:?]
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136) [?:?]
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635) [?:?]
	at java.lang.Thread.run(Thread.java:840) [?:?]
2025-08-20 19:48:41.376 [SourceCoordinator-Source: Kafka-组合订单] INFO  org.apache.flink.connector.kafka.source.enumerator.KafkaSourceEnumerator - Starting the KafkaSourceEnumerator for consumer group orderbook-local-test-group without periodic partition discovery.
2025-08-20 19:48:41.472 [SourceCoordinator-Source: Kafka-组合订单] INFO  org.apache.flink.connector.kafka.source.enumerator.KafkaSourceEnumerator - Discovered new partitions: [cmb_order_data_event-0]
2025-08-20 19:48:41.472 [SourceCoordinator-Source: Kafka-交易] INFO  org.apache.flink.connector.kafka.source.enumerator.KafkaSourceEnumerator - Discovered new partitions: [trade_data_event-0]
2025-08-20 19:48:41.472 [SourceCoordinator-Source: Kafka-单腿订单] INFO  org.apache.flink.connector.kafka.source.enumerator.KafkaSourceEnumerator - Discovered new partitions: [singleleg_order_data_event-0]
2025-08-20 19:48:41.491 [SourceCoordinator-Source: Kafka-单腿订单] INFO  org.apache.flink.connector.kafka.source.enumerator.KafkaSourceEnumerator - Assigning splits to readers {0=[[Partition: singleleg_order_data_event-0, StartingOffset: -2, StoppingOffset: -9223372036854775808]]}
2025-08-20 19:48:41.491 [SourceCoordinator-Source: Kafka-交易] INFO  org.apache.flink.connector.kafka.source.enumerator.KafkaSourceEnumerator - Assigning splits to readers {0=[[Partition: trade_data_event-0, StartingOffset: -2, StoppingOffset: -9223372036854775808]]}
2025-08-20 19:48:41.491 [SourceCoordinator-Source: Kafka-组合订单] INFO  org.apache.flink.connector.kafka.source.enumerator.KafkaSourceEnumerator - Assigning splits to readers {0=[[Partition: cmb_order_data_event-0, StartingOffset: -2, StoppingOffset: -9223372036854775808]]}
2025-08-20 19:48:41.521 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] WARN  org.apache.kafka.clients.consumer.ConsumerConfig - These configurations '[client.id.prefix, partition.discovery.interval.ms]' were supplied but are not used yet.
2025-08-20 19:48:41.521 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 (1/1)#0] WARN  org.apache.kafka.clients.consumer.ConsumerConfig - These configurations '[client.id.prefix, partition.discovery.interval.ms]' were supplied but are not used yet.
2025-08-20 19:48:41.521 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 (1/1)#0] WARN  org.apache.kafka.clients.consumer.ConsumerConfig - These configurations '[client.id.prefix, partition.discovery.interval.ms]' were supplied but are not used yet.
2025-08-20 19:48:41.521 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 (1/1)#0] WARN  org.apache.kafka.common.utils.AppInfoParser - Error registering AppInfo mbean
javax.management.InstanceAlreadyExistsException: kafka.consumer:type=app-info,id=orderbook-local-test-group-0
	at com.sun.jmx.mbeanserver.Repository.addMBean(Repository.java:436) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerWithRepository(DefaultMBeanServerInterceptor.java:1865) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerDynamicMBean(DefaultMBeanServerInterceptor.java:960) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerObject(DefaultMBeanServerInterceptor.java:895) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerMBean(DefaultMBeanServerInterceptor.java:320) ~[?:?]
	at com.sun.jmx.mbeanserver.JmxMBeanServer.registerMBean(JmxMBeanServer.java:523) ~[?:?]
	at org.apache.kafka.common.utils.AppInfoParser.registerAppInfo(AppInfoParser.java:64) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.consumer.KafkaConsumer.<init>(KafkaConsumer.java:821) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.consumer.KafkaConsumer.<init>(KafkaConsumer.java:665) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.consumer.KafkaConsumer.<init>(KafkaConsumer.java:646) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.consumer.KafkaConsumer.<init>(KafkaConsumer.java:626) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.flink.connector.kafka.source.reader.KafkaPartitionSplitReader.<init>(KafkaPartitionSplitReader.java:97) ~[flink-connector-kafka-3.4.0-1.20.jar:3.4.0-1.20]
	at org.apache.flink.connector.kafka.source.KafkaSource.lambda$createReader$1(KafkaSource.java:185) ~[flink-connector-kafka-3.4.0-1.20.jar:3.4.0-1.20]
	at org.apache.flink.connector.base.source.reader.fetcher.SplitFetcherManager.createSplitFetcher(SplitFetcherManager.java:259) ~[flink-connector-base-1.20.0.jar:1.20.0]
	at org.apache.flink.connector.base.source.reader.fetcher.SingleThreadFetcherManager.addSplits(SingleThreadFetcherManager.java:148) ~[flink-connector-base-1.20.0.jar:1.20.0]
	at org.apache.flink.connector.base.source.reader.SourceReaderBase.addSplits(SourceReaderBase.java:315) ~[flink-connector-base-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.api.operators.SourceOperator.handleAddSplitsEvent(SourceOperator.java:606) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.api.operators.SourceOperator.handleOperatorEvent(SourceOperator.java:575) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.OperatorEventDispatcherImpl.dispatchEventToHandlers(OperatorEventDispatcherImpl.java:72) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.RegularOperatorChain.dispatchOperatorEvent(RegularOperatorChain.java:80) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.StreamTask.lambda$dispatchOperatorEvent$24(StreamTask.java:1609) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.StreamTaskActionExecutor$1.runThrowing(StreamTaskActionExecutor.java:50) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.mailbox.Mail.run(Mail.java:101) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.mailbox.MailboxProcessor.runMail(MailboxProcessor.java:414) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.mailbox.MailboxProcessor.processMailsNonBlocking(MailboxProcessor.java:399) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.mailbox.MailboxProcessor.processMail(MailboxProcessor.java:361) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.mailbox.MailboxProcessor.runMailboxLoop(MailboxProcessor.java:229) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.StreamTask.runMailboxLoop(StreamTask.java:973) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.StreamTask.invoke(StreamTask.java:917) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.runtime.taskmanager.Task.runWithSystemExitMonitoring(Task.java:970) [flink-runtime-1.20.0.jar:1.20.0]
	at org.apache.flink.runtime.taskmanager.Task.restoreAndInvoke(Task.java:949) [flink-runtime-1.20.0.jar:1.20.0]
	at org.apache.flink.runtime.taskmanager.Task.doRun(Task.java:763) [flink-runtime-1.20.0.jar:1.20.0]
	at org.apache.flink.runtime.taskmanager.Task.run(Task.java:575) [flink-runtime-1.20.0.jar:1.20.0]
	at java.lang.Thread.run(Thread.java:840) [?:?]
2025-08-20 19:48:41.522 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 (1/1)#0] WARN  org.apache.kafka.common.utils.AppInfoParser - Error registering AppInfo mbean
javax.management.InstanceAlreadyExistsException: kafka.consumer:type=app-info,id=orderbook-local-test-group-0
	at com.sun.jmx.mbeanserver.Repository.addMBean(Repository.java:436) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerWithRepository(DefaultMBeanServerInterceptor.java:1865) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerDynamicMBean(DefaultMBeanServerInterceptor.java:960) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerObject(DefaultMBeanServerInterceptor.java:895) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerMBean(DefaultMBeanServerInterceptor.java:320) ~[?:?]
	at com.sun.jmx.mbeanserver.JmxMBeanServer.registerMBean(JmxMBeanServer.java:523) ~[?:?]
	at org.apache.kafka.common.utils.AppInfoParser.registerAppInfo(AppInfoParser.java:64) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.consumer.KafkaConsumer.<init>(KafkaConsumer.java:821) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.consumer.KafkaConsumer.<init>(KafkaConsumer.java:665) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.consumer.KafkaConsumer.<init>(KafkaConsumer.java:646) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.consumer.KafkaConsumer.<init>(KafkaConsumer.java:626) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.flink.connector.kafka.source.reader.KafkaPartitionSplitReader.<init>(KafkaPartitionSplitReader.java:97) ~[flink-connector-kafka-3.4.0-1.20.jar:3.4.0-1.20]
	at org.apache.flink.connector.kafka.source.KafkaSource.lambda$createReader$1(KafkaSource.java:185) ~[flink-connector-kafka-3.4.0-1.20.jar:3.4.0-1.20]
	at org.apache.flink.connector.base.source.reader.fetcher.SplitFetcherManager.createSplitFetcher(SplitFetcherManager.java:259) ~[flink-connector-base-1.20.0.jar:1.20.0]
	at org.apache.flink.connector.base.source.reader.fetcher.SingleThreadFetcherManager.addSplits(SingleThreadFetcherManager.java:148) ~[flink-connector-base-1.20.0.jar:1.20.0]
	at org.apache.flink.connector.base.source.reader.SourceReaderBase.addSplits(SourceReaderBase.java:315) ~[flink-connector-base-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.api.operators.SourceOperator.handleAddSplitsEvent(SourceOperator.java:606) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.api.operators.SourceOperator.handleOperatorEvent(SourceOperator.java:575) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.OperatorEventDispatcherImpl.dispatchEventToHandlers(OperatorEventDispatcherImpl.java:72) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.RegularOperatorChain.dispatchOperatorEvent(RegularOperatorChain.java:80) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.StreamTask.lambda$dispatchOperatorEvent$24(StreamTask.java:1609) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.StreamTaskActionExecutor$1.runThrowing(StreamTaskActionExecutor.java:50) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.mailbox.Mail.run(Mail.java:101) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.mailbox.MailboxProcessor.runMail(MailboxProcessor.java:414) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.mailbox.MailboxProcessor.processMailsNonBlocking(MailboxProcessor.java:399) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.mailbox.MailboxProcessor.processMail(MailboxProcessor.java:361) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.mailbox.MailboxProcessor.runMailboxLoop(MailboxProcessor.java:229) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.StreamTask.runMailboxLoop(StreamTask.java:973) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.StreamTask.invoke(StreamTask.java:917) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.runtime.taskmanager.Task.runWithSystemExitMonitoring(Task.java:970) [flink-runtime-1.20.0.jar:1.20.0]
	at org.apache.flink.runtime.taskmanager.Task.restoreAndInvoke(Task.java:949) [flink-runtime-1.20.0.jar:1.20.0]
	at org.apache.flink.runtime.taskmanager.Task.doRun(Task.java:763) [flink-runtime-1.20.0.jar:1.20.0]
	at org.apache.flink.runtime.taskmanager.Task.run(Task.java:575) [flink-runtime-1.20.0.jar:1.20.0]
	at java.lang.Thread.run(Thread.java:840) [?:?]
2025-08-20 19:49:42.452 [main] WARN  org.apache.flink.connector.kafka.source.KafkaSourceBuilder - Property auto.offset.reset is provided but will be overridden from earliest to earliest
2025-08-20 19:49:48.852 [main] WARN  org.apache.flink.connector.kafka.source.KafkaSourceBuilder - Property auto.offset.reset is provided but will be overridden from earliest to earliest
2025-08-20 19:49:50.855 [main] WARN  org.apache.flink.connector.kafka.source.KafkaSourceBuilder - Property auto.offset.reset is provided but will be overridden from earliest to earliest
2025-08-20 19:49:51.706 [SourceCoordinator-Source: Kafka-单腿订单] WARN  org.apache.kafka.clients.admin.AdminClientConfig - These configurations '[key.deserializer, value.deserializer, max.poll.records, client.id.prefix, group.id, partition.discovery.interval.ms, heartbeat.interval.ms, enable.auto.commit, fetch.max.wait.ms, fetch.min.bytes, session.timeout.ms, auto.offset.reset]' were supplied but are not used yet.
2025-08-20 19:49:51.706 [SourceCoordinator-Source: Kafka-组合订单] WARN  org.apache.kafka.clients.admin.AdminClientConfig - These configurations '[key.deserializer, value.deserializer, max.poll.records, client.id.prefix, group.id, partition.discovery.interval.ms, heartbeat.interval.ms, enable.auto.commit, fetch.max.wait.ms, fetch.min.bytes, session.timeout.ms, auto.offset.reset]' were supplied but are not used yet.
2025-08-20 19:49:51.706 [SourceCoordinator-Source: Kafka-交易] WARN  org.apache.kafka.clients.admin.AdminClientConfig - These configurations '[key.deserializer, value.deserializer, max.poll.records, client.id.prefix, group.id, partition.discovery.interval.ms, heartbeat.interval.ms, enable.auto.commit, fetch.max.wait.ms, fetch.min.bytes, session.timeout.ms, auto.offset.reset]' were supplied but are not used yet.
2025-08-20 19:49:51.707 [SourceCoordinator-Source: Kafka-单腿订单] INFO  org.apache.flink.connector.kafka.source.enumerator.KafkaSourceEnumerator - Starting the KafkaSourceEnumerator for consumer group orderbook-local-test-group without periodic partition discovery.
2025-08-20 19:49:51.707 [SourceCoordinator-Source: Kafka-交易] WARN  org.apache.kafka.common.utils.AppInfoParser - Error registering AppInfo mbean
javax.management.InstanceAlreadyExistsException: kafka.admin.client:type=app-info,id=orderbook-local-test-group-enumerator-admin-client
	at com.sun.jmx.mbeanserver.Repository.addMBean(Repository.java:436) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerWithRepository(DefaultMBeanServerInterceptor.java:1865) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerDynamicMBean(DefaultMBeanServerInterceptor.java:960) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerObject(DefaultMBeanServerInterceptor.java:895) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerMBean(DefaultMBeanServerInterceptor.java:320) ~[?:?]
	at com.sun.jmx.mbeanserver.JmxMBeanServer.registerMBean(JmxMBeanServer.java:523) ~[?:?]
	at org.apache.kafka.common.utils.AppInfoParser.registerAppInfo(AppInfoParser.java:64) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.admin.KafkaAdminClient.<init>(KafkaAdminClient.java:602) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.admin.KafkaAdminClient.createInternal(KafkaAdminClient.java:544) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.admin.KafkaAdminClient.createInternal(KafkaAdminClient.java:488) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.admin.Admin.create(Admin.java:134) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.admin.AdminClient.create(AdminClient.java:39) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.flink.connector.kafka.source.enumerator.KafkaSourceEnumerator.getKafkaAdminClient(KafkaSourceEnumerator.java:441) ~[flink-connector-kafka-3.4.0-1.20.jar:3.4.0-1.20]
	at org.apache.flink.connector.kafka.source.enumerator.KafkaSourceEnumerator.start(KafkaSourceEnumerator.java:164) ~[flink-connector-kafka-3.4.0-1.20.jar:3.4.0-1.20]
	at org.apache.flink.runtime.source.coordinator.SourceCoordinator.lambda$start$1(SourceCoordinator.java:244) ~[flink-runtime-1.20.0.jar:1.20.0]
	at org.apache.flink.runtime.source.coordinator.SourceCoordinator.lambda$runInEventLoop$10(SourceCoordinator.java:533) ~[flink-runtime-1.20.0.jar:1.20.0]
	at org.apache.flink.util.ThrowableCatchingRunnable.run(ThrowableCatchingRunnable.java:40) [flink-core-1.20.0.jar:1.20.0]
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:539) [?:?]
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:264) [?:?]
	at java.util.concurrent.FutureTask.run(FutureTask.java) [?:?]
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:304) [?:?]
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136) [?:?]
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635) [?:?]
	at java.lang.Thread.run(Thread.java:840) [?:?]
2025-08-20 19:49:51.711 [SourceCoordinator-Source: Kafka-交易] INFO  org.apache.flink.connector.kafka.source.enumerator.KafkaSourceEnumerator - Starting the KafkaSourceEnumerator for consumer group orderbook-local-test-group without periodic partition discovery.
2025-08-20 19:49:51.711 [SourceCoordinator-Source: Kafka-组合订单] WARN  org.apache.kafka.common.utils.AppInfoParser - Error registering AppInfo mbean
javax.management.InstanceAlreadyExistsException: kafka.admin.client:type=app-info,id=orderbook-local-test-group-enumerator-admin-client
	at com.sun.jmx.mbeanserver.Repository.addMBean(Repository.java:436) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerWithRepository(DefaultMBeanServerInterceptor.java:1865) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerDynamicMBean(DefaultMBeanServerInterceptor.java:960) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerObject(DefaultMBeanServerInterceptor.java:895) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerMBean(DefaultMBeanServerInterceptor.java:320) ~[?:?]
	at com.sun.jmx.mbeanserver.JmxMBeanServer.registerMBean(JmxMBeanServer.java:523) ~[?:?]
	at org.apache.kafka.common.utils.AppInfoParser.registerAppInfo(AppInfoParser.java:64) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.admin.KafkaAdminClient.<init>(KafkaAdminClient.java:602) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.admin.KafkaAdminClient.createInternal(KafkaAdminClient.java:544) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.admin.KafkaAdminClient.createInternal(KafkaAdminClient.java:488) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.admin.Admin.create(Admin.java:134) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.admin.AdminClient.create(AdminClient.java:39) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.flink.connector.kafka.source.enumerator.KafkaSourceEnumerator.getKafkaAdminClient(KafkaSourceEnumerator.java:441) ~[flink-connector-kafka-3.4.0-1.20.jar:3.4.0-1.20]
	at org.apache.flink.connector.kafka.source.enumerator.KafkaSourceEnumerator.start(KafkaSourceEnumerator.java:164) ~[flink-connector-kafka-3.4.0-1.20.jar:3.4.0-1.20]
	at org.apache.flink.runtime.source.coordinator.SourceCoordinator.lambda$start$1(SourceCoordinator.java:244) ~[flink-runtime-1.20.0.jar:1.20.0]
	at org.apache.flink.runtime.source.coordinator.SourceCoordinator.lambda$runInEventLoop$10(SourceCoordinator.java:533) ~[flink-runtime-1.20.0.jar:1.20.0]
	at org.apache.flink.util.ThrowableCatchingRunnable.run(ThrowableCatchingRunnable.java:40) [flink-core-1.20.0.jar:1.20.0]
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:539) [?:?]
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:264) [?:?]
	at java.util.concurrent.FutureTask.run(FutureTask.java) [?:?]
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:304) [?:?]
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136) [?:?]
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635) [?:?]
	at java.lang.Thread.run(Thread.java:840) [?:?]
2025-08-20 19:49:51.711 [SourceCoordinator-Source: Kafka-组合订单] INFO  org.apache.flink.connector.kafka.source.enumerator.KafkaSourceEnumerator - Starting the KafkaSourceEnumerator for consumer group orderbook-local-test-group without periodic partition discovery.
2025-08-20 19:49:51.854 [SourceCoordinator-Source: Kafka-组合订单] INFO  org.apache.flink.connector.kafka.source.enumerator.KafkaSourceEnumerator - Discovered new partitions: [cmb_order_data_event-0]
2025-08-20 19:49:51.854 [SourceCoordinator-Source: Kafka-交易] INFO  org.apache.flink.connector.kafka.source.enumerator.KafkaSourceEnumerator - Discovered new partitions: [trade_data_event-0]
2025-08-20 19:49:51.854 [SourceCoordinator-Source: Kafka-单腿订单] INFO  org.apache.flink.connector.kafka.source.enumerator.KafkaSourceEnumerator - Discovered new partitions: [singleleg_order_data_event-0]
2025-08-20 19:49:51.866 [SourceCoordinator-Source: Kafka-交易] INFO  org.apache.flink.connector.kafka.source.enumerator.KafkaSourceEnumerator - Assigning splits to readers {0=[[Partition: trade_data_event-0, StartingOffset: -2, StoppingOffset: -9223372036854775808]]}
2025-08-20 19:49:51.866 [SourceCoordinator-Source: Kafka-组合订单] INFO  org.apache.flink.connector.kafka.source.enumerator.KafkaSourceEnumerator - Assigning splits to readers {0=[[Partition: cmb_order_data_event-0, StartingOffset: -2, StoppingOffset: -9223372036854775808]]}
2025-08-20 19:49:51.866 [SourceCoordinator-Source: Kafka-单腿订单] INFO  org.apache.flink.connector.kafka.source.enumerator.KafkaSourceEnumerator - Assigning splits to readers {0=[[Partition: singleleg_order_data_event-0, StartingOffset: -2, StoppingOffset: -9223372036854775808]]}
2025-08-20 19:49:51.890 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 (1/1)#0] WARN  org.apache.kafka.clients.consumer.ConsumerConfig - These configurations '[client.id.prefix, partition.discovery.interval.ms]' were supplied but are not used yet.
2025-08-20 19:49:51.890 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 (1/1)#0] WARN  org.apache.kafka.clients.consumer.ConsumerConfig - These configurations '[client.id.prefix, partition.discovery.interval.ms]' were supplied but are not used yet.
2025-08-20 19:49:51.890 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] WARN  org.apache.kafka.clients.consumer.ConsumerConfig - These configurations '[client.id.prefix, partition.discovery.interval.ms]' were supplied but are not used yet.
2025-08-20 19:49:51.890 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] WARN  org.apache.kafka.common.utils.AppInfoParser - Error registering AppInfo mbean
javax.management.InstanceAlreadyExistsException: kafka.consumer:type=app-info,id=orderbook-local-test-group-0
	at com.sun.jmx.mbeanserver.Repository.addMBean(Repository.java:436) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerWithRepository(DefaultMBeanServerInterceptor.java:1865) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerDynamicMBean(DefaultMBeanServerInterceptor.java:960) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerObject(DefaultMBeanServerInterceptor.java:895) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerMBean(DefaultMBeanServerInterceptor.java:320) ~[?:?]
	at com.sun.jmx.mbeanserver.JmxMBeanServer.registerMBean(JmxMBeanServer.java:523) ~[?:?]
	at org.apache.kafka.common.utils.AppInfoParser.registerAppInfo(AppInfoParser.java:64) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.consumer.KafkaConsumer.<init>(KafkaConsumer.java:821) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.consumer.KafkaConsumer.<init>(KafkaConsumer.java:665) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.consumer.KafkaConsumer.<init>(KafkaConsumer.java:646) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.consumer.KafkaConsumer.<init>(KafkaConsumer.java:626) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.flink.connector.kafka.source.reader.KafkaPartitionSplitReader.<init>(KafkaPartitionSplitReader.java:97) ~[flink-connector-kafka-3.4.0-1.20.jar:3.4.0-1.20]
	at org.apache.flink.connector.kafka.source.KafkaSource.lambda$createReader$1(KafkaSource.java:185) ~[flink-connector-kafka-3.4.0-1.20.jar:3.4.0-1.20]
	at org.apache.flink.connector.base.source.reader.fetcher.SplitFetcherManager.createSplitFetcher(SplitFetcherManager.java:259) ~[flink-connector-base-1.20.0.jar:1.20.0]
	at org.apache.flink.connector.base.source.reader.fetcher.SingleThreadFetcherManager.addSplits(SingleThreadFetcherManager.java:148) ~[flink-connector-base-1.20.0.jar:1.20.0]
	at org.apache.flink.connector.base.source.reader.SourceReaderBase.addSplits(SourceReaderBase.java:315) ~[flink-connector-base-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.api.operators.SourceOperator.handleAddSplitsEvent(SourceOperator.java:606) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.api.operators.SourceOperator.handleOperatorEvent(SourceOperator.java:575) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.OperatorEventDispatcherImpl.dispatchEventToHandlers(OperatorEventDispatcherImpl.java:72) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.RegularOperatorChain.dispatchOperatorEvent(RegularOperatorChain.java:80) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.StreamTask.lambda$dispatchOperatorEvent$24(StreamTask.java:1609) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.StreamTaskActionExecutor$1.runThrowing(StreamTaskActionExecutor.java:50) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.mailbox.Mail.run(Mail.java:101) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.mailbox.MailboxProcessor.runMail(MailboxProcessor.java:414) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.mailbox.MailboxProcessor.processMailsNonBlocking(MailboxProcessor.java:399) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.mailbox.MailboxProcessor.processMail(MailboxProcessor.java:361) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.mailbox.MailboxProcessor.runMailboxLoop(MailboxProcessor.java:229) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.StreamTask.runMailboxLoop(StreamTask.java:973) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.StreamTask.invoke(StreamTask.java:917) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.runtime.taskmanager.Task.runWithSystemExitMonitoring(Task.java:970) [flink-runtime-1.20.0.jar:1.20.0]
	at org.apache.flink.runtime.taskmanager.Task.restoreAndInvoke(Task.java:949) [flink-runtime-1.20.0.jar:1.20.0]
	at org.apache.flink.runtime.taskmanager.Task.doRun(Task.java:763) [flink-runtime-1.20.0.jar:1.20.0]
	at org.apache.flink.runtime.taskmanager.Task.run(Task.java:575) [flink-runtime-1.20.0.jar:1.20.0]
	at java.lang.Thread.run(Thread.java:840) [?:?]
2025-08-20 19:49:51.891 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 (1/1)#0] WARN  org.apache.kafka.common.utils.AppInfoParser - Error registering AppInfo mbean
javax.management.InstanceAlreadyExistsException: kafka.consumer:type=app-info,id=orderbook-local-test-group-0
	at com.sun.jmx.mbeanserver.Repository.addMBean(Repository.java:436) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerWithRepository(DefaultMBeanServerInterceptor.java:1865) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerDynamicMBean(DefaultMBeanServerInterceptor.java:960) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerObject(DefaultMBeanServerInterceptor.java:895) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerMBean(DefaultMBeanServerInterceptor.java:320) ~[?:?]
	at com.sun.jmx.mbeanserver.JmxMBeanServer.registerMBean(JmxMBeanServer.java:523) ~[?:?]
	at org.apache.kafka.common.utils.AppInfoParser.registerAppInfo(AppInfoParser.java:64) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.consumer.KafkaConsumer.<init>(KafkaConsumer.java:821) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.consumer.KafkaConsumer.<init>(KafkaConsumer.java:665) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.consumer.KafkaConsumer.<init>(KafkaConsumer.java:646) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.consumer.KafkaConsumer.<init>(KafkaConsumer.java:626) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.flink.connector.kafka.source.reader.KafkaPartitionSplitReader.<init>(KafkaPartitionSplitReader.java:97) ~[flink-connector-kafka-3.4.0-1.20.jar:3.4.0-1.20]
	at org.apache.flink.connector.kafka.source.KafkaSource.lambda$createReader$1(KafkaSource.java:185) ~[flink-connector-kafka-3.4.0-1.20.jar:3.4.0-1.20]
	at org.apache.flink.connector.base.source.reader.fetcher.SplitFetcherManager.createSplitFetcher(SplitFetcherManager.java:259) ~[flink-connector-base-1.20.0.jar:1.20.0]
	at org.apache.flink.connector.base.source.reader.fetcher.SingleThreadFetcherManager.addSplits(SingleThreadFetcherManager.java:148) ~[flink-connector-base-1.20.0.jar:1.20.0]
	at org.apache.flink.connector.base.source.reader.SourceReaderBase.addSplits(SourceReaderBase.java:315) ~[flink-connector-base-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.api.operators.SourceOperator.handleAddSplitsEvent(SourceOperator.java:606) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.api.operators.SourceOperator.handleOperatorEvent(SourceOperator.java:575) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.OperatorEventDispatcherImpl.dispatchEventToHandlers(OperatorEventDispatcherImpl.java:72) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.RegularOperatorChain.dispatchOperatorEvent(RegularOperatorChain.java:80) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.StreamTask.lambda$dispatchOperatorEvent$24(StreamTask.java:1609) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.StreamTaskActionExecutor$1.runThrowing(StreamTaskActionExecutor.java:50) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.mailbox.Mail.run(Mail.java:101) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.mailbox.MailboxProcessor.runMail(MailboxProcessor.java:414) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.mailbox.MailboxProcessor.processMailsNonBlocking(MailboxProcessor.java:399) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.mailbox.MailboxProcessor.processMail(MailboxProcessor.java:361) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.mailbox.MailboxProcessor.runMailboxLoop(MailboxProcessor.java:229) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.StreamTask.runMailboxLoop(StreamTask.java:973) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.StreamTask.invoke(StreamTask.java:917) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.runtime.taskmanager.Task.runWithSystemExitMonitoring(Task.java:970) [flink-runtime-1.20.0.jar:1.20.0]
	at org.apache.flink.runtime.taskmanager.Task.restoreAndInvoke(Task.java:949) [flink-runtime-1.20.0.jar:1.20.0]
	at org.apache.flink.runtime.taskmanager.Task.doRun(Task.java:763) [flink-runtime-1.20.0.jar:1.20.0]
	at org.apache.flink.runtime.taskmanager.Task.run(Task.java:575) [flink-runtime-1.20.0.jar:1.20.0]
	at java.lang.Thread.run(Thread.java:840) [?:?]
2025-08-20 19:50:23.366 [main] WARN  org.apache.flink.connector.kafka.source.KafkaSourceBuilder - Property auto.offset.reset is provided but will be overridden from earliest to earliest
2025-08-20 19:50:23.382 [main] WARN  org.apache.flink.connector.kafka.source.KafkaSourceBuilder - Property auto.offset.reset is provided but will be overridden from earliest to earliest
2025-08-20 19:50:23.383 [main] WARN  org.apache.flink.connector.kafka.source.KafkaSourceBuilder - Property auto.offset.reset is provided but will be overridden from earliest to earliest
2025-08-20 19:50:24.129 [SourceCoordinator-Source: Kafka-单腿订单] WARN  org.apache.kafka.clients.admin.AdminClientConfig - These configurations '[key.deserializer, value.deserializer, max.poll.records, client.id.prefix, group.id, partition.discovery.interval.ms, heartbeat.interval.ms, enable.auto.commit, fetch.max.wait.ms, fetch.min.bytes, session.timeout.ms, auto.offset.reset]' were supplied but are not used yet.
2025-08-20 19:50:24.129 [SourceCoordinator-Source: Kafka-交易] WARN  org.apache.kafka.clients.admin.AdminClientConfig - These configurations '[key.deserializer, value.deserializer, max.poll.records, client.id.prefix, group.id, partition.discovery.interval.ms, heartbeat.interval.ms, enable.auto.commit, fetch.max.wait.ms, fetch.min.bytes, session.timeout.ms, auto.offset.reset]' were supplied but are not used yet.
2025-08-20 19:50:24.129 [SourceCoordinator-Source: Kafka-组合订单] WARN  org.apache.kafka.clients.admin.AdminClientConfig - These configurations '[key.deserializer, value.deserializer, max.poll.records, client.id.prefix, group.id, partition.discovery.interval.ms, heartbeat.interval.ms, enable.auto.commit, fetch.max.wait.ms, fetch.min.bytes, session.timeout.ms, auto.offset.reset]' were supplied but are not used yet.
2025-08-20 19:50:24.130 [SourceCoordinator-Source: Kafka-单腿订单] INFO  org.apache.flink.connector.kafka.source.enumerator.KafkaSourceEnumerator - Starting the KafkaSourceEnumerator for consumer group orderbook-local-test-group without periodic partition discovery.
2025-08-20 19:50:24.130 [SourceCoordinator-Source: Kafka-组合订单] WARN  org.apache.kafka.common.utils.AppInfoParser - Error registering AppInfo mbean
javax.management.InstanceAlreadyExistsException: kafka.admin.client:type=app-info,id=orderbook-local-test-group-enumerator-admin-client
	at com.sun.jmx.mbeanserver.Repository.addMBean(Repository.java:436) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerWithRepository(DefaultMBeanServerInterceptor.java:1865) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerDynamicMBean(DefaultMBeanServerInterceptor.java:960) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerObject(DefaultMBeanServerInterceptor.java:895) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerMBean(DefaultMBeanServerInterceptor.java:320) ~[?:?]
	at com.sun.jmx.mbeanserver.JmxMBeanServer.registerMBean(JmxMBeanServer.java:523) ~[?:?]
	at org.apache.kafka.common.utils.AppInfoParser.registerAppInfo(AppInfoParser.java:64) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.admin.KafkaAdminClient.<init>(KafkaAdminClient.java:602) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.admin.KafkaAdminClient.createInternal(KafkaAdminClient.java:544) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.admin.KafkaAdminClient.createInternal(KafkaAdminClient.java:488) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.admin.Admin.create(Admin.java:134) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.admin.AdminClient.create(AdminClient.java:39) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.flink.connector.kafka.source.enumerator.KafkaSourceEnumerator.getKafkaAdminClient(KafkaSourceEnumerator.java:441) ~[flink-connector-kafka-3.4.0-1.20.jar:3.4.0-1.20]
	at org.apache.flink.connector.kafka.source.enumerator.KafkaSourceEnumerator.start(KafkaSourceEnumerator.java:164) ~[flink-connector-kafka-3.4.0-1.20.jar:3.4.0-1.20]
	at org.apache.flink.runtime.source.coordinator.SourceCoordinator.lambda$start$1(SourceCoordinator.java:244) ~[flink-runtime-1.20.0.jar:1.20.0]
	at org.apache.flink.runtime.source.coordinator.SourceCoordinator.lambda$runInEventLoop$10(SourceCoordinator.java:533) ~[flink-runtime-1.20.0.jar:1.20.0]
	at org.apache.flink.util.ThrowableCatchingRunnable.run(ThrowableCatchingRunnable.java:40) [flink-core-1.20.0.jar:1.20.0]
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:539) [?:?]
	at java.util.concurrent.FutureTask.run(FutureTask.java:264) [?:?]
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:304) [?:?]
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136) [?:?]
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635) [?:?]
	at java.lang.Thread.run(Thread.java:840) [?:?]
2025-08-20 19:50:24.133 [SourceCoordinator-Source: Kafka-组合订单] INFO  org.apache.flink.connector.kafka.source.enumerator.KafkaSourceEnumerator - Starting the KafkaSourceEnumerator for consumer group orderbook-local-test-group without periodic partition discovery.
2025-08-20 19:50:24.133 [SourceCoordinator-Source: Kafka-交易] WARN  org.apache.kafka.common.utils.AppInfoParser - Error registering AppInfo mbean
javax.management.InstanceAlreadyExistsException: kafka.admin.client:type=app-info,id=orderbook-local-test-group-enumerator-admin-client
	at com.sun.jmx.mbeanserver.Repository.addMBean(Repository.java:436) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerWithRepository(DefaultMBeanServerInterceptor.java:1865) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerDynamicMBean(DefaultMBeanServerInterceptor.java:960) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerObject(DefaultMBeanServerInterceptor.java:895) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerMBean(DefaultMBeanServerInterceptor.java:320) ~[?:?]
	at com.sun.jmx.mbeanserver.JmxMBeanServer.registerMBean(JmxMBeanServer.java:523) ~[?:?]
	at org.apache.kafka.common.utils.AppInfoParser.registerAppInfo(AppInfoParser.java:64) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.admin.KafkaAdminClient.<init>(KafkaAdminClient.java:602) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.admin.KafkaAdminClient.createInternal(KafkaAdminClient.java:544) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.admin.KafkaAdminClient.createInternal(KafkaAdminClient.java:488) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.admin.Admin.create(Admin.java:134) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.admin.AdminClient.create(AdminClient.java:39) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.flink.connector.kafka.source.enumerator.KafkaSourceEnumerator.getKafkaAdminClient(KafkaSourceEnumerator.java:441) ~[flink-connector-kafka-3.4.0-1.20.jar:3.4.0-1.20]
	at org.apache.flink.connector.kafka.source.enumerator.KafkaSourceEnumerator.start(KafkaSourceEnumerator.java:164) ~[flink-connector-kafka-3.4.0-1.20.jar:3.4.0-1.20]
	at org.apache.flink.runtime.source.coordinator.SourceCoordinator.lambda$start$1(SourceCoordinator.java:244) ~[flink-runtime-1.20.0.jar:1.20.0]
	at org.apache.flink.runtime.source.coordinator.SourceCoordinator.lambda$runInEventLoop$10(SourceCoordinator.java:533) ~[flink-runtime-1.20.0.jar:1.20.0]
	at org.apache.flink.util.ThrowableCatchingRunnable.run(ThrowableCatchingRunnable.java:40) [flink-core-1.20.0.jar:1.20.0]
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:539) [?:?]
	at java.util.concurrent.FutureTask.run(FutureTask.java:264) [?:?]
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:304) [?:?]
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136) [?:?]
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635) [?:?]
	at java.lang.Thread.run(Thread.java:840) [?:?]
2025-08-20 19:50:24.133 [SourceCoordinator-Source: Kafka-交易] INFO  org.apache.flink.connector.kafka.source.enumerator.KafkaSourceEnumerator - Starting the KafkaSourceEnumerator for consumer group orderbook-local-test-group without periodic partition discovery.
2025-08-20 19:50:24.226 [SourceCoordinator-Source: Kafka-单腿订单] INFO  org.apache.flink.connector.kafka.source.enumerator.KafkaSourceEnumerator - Discovered new partitions: [singleleg_order_data_event-0]
2025-08-20 19:50:24.226 [SourceCoordinator-Source: Kafka-交易] INFO  org.apache.flink.connector.kafka.source.enumerator.KafkaSourceEnumerator - Discovered new partitions: [trade_data_event-0]
2025-08-20 19:50:24.226 [SourceCoordinator-Source: Kafka-组合订单] INFO  org.apache.flink.connector.kafka.source.enumerator.KafkaSourceEnumerator - Discovered new partitions: [cmb_order_data_event-0]
2025-08-20 19:50:24.249 [SourceCoordinator-Source: Kafka-组合订单] INFO  org.apache.flink.connector.kafka.source.enumerator.KafkaSourceEnumerator - Assigning splits to readers {0=[[Partition: cmb_order_data_event-0, StartingOffset: -2, StoppingOffset: -9223372036854775808]]}
2025-08-20 19:50:24.249 [SourceCoordinator-Source: Kafka-交易] INFO  org.apache.flink.connector.kafka.source.enumerator.KafkaSourceEnumerator - Assigning splits to readers {0=[[Partition: trade_data_event-0, StartingOffset: -2, StoppingOffset: -9223372036854775808]]}
2025-08-20 19:50:24.249 [SourceCoordinator-Source: Kafka-单腿订单] INFO  org.apache.flink.connector.kafka.source.enumerator.KafkaSourceEnumerator - Assigning splits to readers {0=[[Partition: singleleg_order_data_event-0, StartingOffset: -2, StoppingOffset: -9223372036854775808]]}
2025-08-20 19:50:24.281 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 (1/1)#0] WARN  org.apache.kafka.clients.consumer.ConsumerConfig - These configurations '[client.id.prefix, partition.discovery.interval.ms]' were supplied but are not used yet.
2025-08-20 19:50:24.281 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] WARN  org.apache.kafka.clients.consumer.ConsumerConfig - These configurations '[client.id.prefix, partition.discovery.interval.ms]' were supplied but are not used yet.
2025-08-20 19:50:24.281 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 (1/1)#0] WARN  org.apache.kafka.clients.consumer.ConsumerConfig - These configurations '[client.id.prefix, partition.discovery.interval.ms]' were supplied but are not used yet.
2025-08-20 19:50:24.282 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 (1/1)#0] WARN  org.apache.kafka.common.utils.AppInfoParser - Error registering AppInfo mbean
javax.management.InstanceAlreadyExistsException: kafka.consumer:type=app-info,id=orderbook-local-test-group-0
	at com.sun.jmx.mbeanserver.Repository.addMBean(Repository.java:436) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerWithRepository(DefaultMBeanServerInterceptor.java:1865) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerDynamicMBean(DefaultMBeanServerInterceptor.java:960) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerObject(DefaultMBeanServerInterceptor.java:895) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerMBean(DefaultMBeanServerInterceptor.java:320) ~[?:?]
	at com.sun.jmx.mbeanserver.JmxMBeanServer.registerMBean(JmxMBeanServer.java:523) ~[?:?]
	at org.apache.kafka.common.utils.AppInfoParser.registerAppInfo(AppInfoParser.java:64) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.consumer.KafkaConsumer.<init>(KafkaConsumer.java:821) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.consumer.KafkaConsumer.<init>(KafkaConsumer.java:665) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.consumer.KafkaConsumer.<init>(KafkaConsumer.java:646) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.consumer.KafkaConsumer.<init>(KafkaConsumer.java:626) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.flink.connector.kafka.source.reader.KafkaPartitionSplitReader.<init>(KafkaPartitionSplitReader.java:97) ~[flink-connector-kafka-3.4.0-1.20.jar:3.4.0-1.20]
	at org.apache.flink.connector.kafka.source.KafkaSource.lambda$createReader$1(KafkaSource.java:185) ~[flink-connector-kafka-3.4.0-1.20.jar:3.4.0-1.20]
	at org.apache.flink.connector.base.source.reader.fetcher.SplitFetcherManager.createSplitFetcher(SplitFetcherManager.java:259) ~[flink-connector-base-1.20.0.jar:1.20.0]
	at org.apache.flink.connector.base.source.reader.fetcher.SingleThreadFetcherManager.addSplits(SingleThreadFetcherManager.java:148) ~[flink-connector-base-1.20.0.jar:1.20.0]
	at org.apache.flink.connector.base.source.reader.SourceReaderBase.addSplits(SourceReaderBase.java:315) ~[flink-connector-base-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.api.operators.SourceOperator.handleAddSplitsEvent(SourceOperator.java:606) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.api.operators.SourceOperator.handleOperatorEvent(SourceOperator.java:575) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.OperatorEventDispatcherImpl.dispatchEventToHandlers(OperatorEventDispatcherImpl.java:72) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.RegularOperatorChain.dispatchOperatorEvent(RegularOperatorChain.java:80) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.StreamTask.lambda$dispatchOperatorEvent$24(StreamTask.java:1609) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.StreamTaskActionExecutor$1.runThrowing(StreamTaskActionExecutor.java:50) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.mailbox.Mail.run(Mail.java:101) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.mailbox.MailboxProcessor.runMail(MailboxProcessor.java:414) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.mailbox.MailboxProcessor.processMailsWhenDefaultActionUnavailable(MailboxProcessor.java:383) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.mailbox.MailboxProcessor.processMail(MailboxProcessor.java:368) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.mailbox.MailboxProcessor.runMailboxLoop(MailboxProcessor.java:229) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.StreamTask.runMailboxLoop(StreamTask.java:973) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.StreamTask.invoke(StreamTask.java:917) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.runtime.taskmanager.Task.runWithSystemExitMonitoring(Task.java:970) [flink-runtime-1.20.0.jar:1.20.0]
	at org.apache.flink.runtime.taskmanager.Task.restoreAndInvoke(Task.java:949) [flink-runtime-1.20.0.jar:1.20.0]
	at org.apache.flink.runtime.taskmanager.Task.doRun(Task.java:763) [flink-runtime-1.20.0.jar:1.20.0]
	at org.apache.flink.runtime.taskmanager.Task.run(Task.java:575) [flink-runtime-1.20.0.jar:1.20.0]
	at java.lang.Thread.run(Thread.java:840) [?:?]
2025-08-20 19:50:24.282 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 (1/1)#0] WARN  org.apache.kafka.common.utils.AppInfoParser - Error registering AppInfo mbean
javax.management.InstanceAlreadyExistsException: kafka.consumer:type=app-info,id=orderbook-local-test-group-0
	at com.sun.jmx.mbeanserver.Repository.addMBean(Repository.java:436) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerWithRepository(DefaultMBeanServerInterceptor.java:1865) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerDynamicMBean(DefaultMBeanServerInterceptor.java:960) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerObject(DefaultMBeanServerInterceptor.java:895) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerMBean(DefaultMBeanServerInterceptor.java:320) ~[?:?]
	at com.sun.jmx.mbeanserver.JmxMBeanServer.registerMBean(JmxMBeanServer.java:523) ~[?:?]
	at org.apache.kafka.common.utils.AppInfoParser.registerAppInfo(AppInfoParser.java:64) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.consumer.KafkaConsumer.<init>(KafkaConsumer.java:821) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.consumer.KafkaConsumer.<init>(KafkaConsumer.java:665) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.consumer.KafkaConsumer.<init>(KafkaConsumer.java:646) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.consumer.KafkaConsumer.<init>(KafkaConsumer.java:626) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.flink.connector.kafka.source.reader.KafkaPartitionSplitReader.<init>(KafkaPartitionSplitReader.java:97) ~[flink-connector-kafka-3.4.0-1.20.jar:3.4.0-1.20]
	at org.apache.flink.connector.kafka.source.KafkaSource.lambda$createReader$1(KafkaSource.java:185) ~[flink-connector-kafka-3.4.0-1.20.jar:3.4.0-1.20]
	at org.apache.flink.connector.base.source.reader.fetcher.SplitFetcherManager.createSplitFetcher(SplitFetcherManager.java:259) ~[flink-connector-base-1.20.0.jar:1.20.0]
	at org.apache.flink.connector.base.source.reader.fetcher.SingleThreadFetcherManager.addSplits(SingleThreadFetcherManager.java:148) ~[flink-connector-base-1.20.0.jar:1.20.0]
	at org.apache.flink.connector.base.source.reader.SourceReaderBase.addSplits(SourceReaderBase.java:315) ~[flink-connector-base-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.api.operators.SourceOperator.handleAddSplitsEvent(SourceOperator.java:606) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.api.operators.SourceOperator.handleOperatorEvent(SourceOperator.java:575) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.OperatorEventDispatcherImpl.dispatchEventToHandlers(OperatorEventDispatcherImpl.java:72) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.RegularOperatorChain.dispatchOperatorEvent(RegularOperatorChain.java:80) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.StreamTask.lambda$dispatchOperatorEvent$24(StreamTask.java:1609) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.StreamTaskActionExecutor$1.runThrowing(StreamTaskActionExecutor.java:50) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.mailbox.Mail.run(Mail.java:101) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.mailbox.MailboxProcessor.runMail(MailboxProcessor.java:414) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.mailbox.MailboxProcessor.processMailsWhenDefaultActionUnavailable(MailboxProcessor.java:383) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.mailbox.MailboxProcessor.processMail(MailboxProcessor.java:368) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.mailbox.MailboxProcessor.runMailboxLoop(MailboxProcessor.java:229) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.StreamTask.runMailboxLoop(StreamTask.java:973) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.StreamTask.invoke(StreamTask.java:917) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.runtime.taskmanager.Task.runWithSystemExitMonitoring(Task.java:970) [flink-runtime-1.20.0.jar:1.20.0]
	at org.apache.flink.runtime.taskmanager.Task.restoreAndInvoke(Task.java:949) [flink-runtime-1.20.0.jar:1.20.0]
	at org.apache.flink.runtime.taskmanager.Task.doRun(Task.java:763) [flink-runtime-1.20.0.jar:1.20.0]
	at org.apache.flink.runtime.taskmanager.Task.run(Task.java:575) [flink-runtime-1.20.0.jar:1.20.0]
	at java.lang.Thread.run(Thread.java:840) [?:?]
2025-08-20 19:50:28.446 [main] WARN  org.apache.flink.connector.kafka.source.KafkaSourceBuilder - Property auto.offset.reset is provided but will be overridden from earliest to earliest
2025-08-20 19:50:47.198 [main] WARN  org.apache.flink.connector.kafka.source.KafkaSourceBuilder - Property auto.offset.reset is provided but will be overridden from earliest to earliest
2025-08-20 19:50:47.200 [main] WARN  org.apache.flink.connector.kafka.source.KafkaSourceBuilder - Property auto.offset.reset is provided but will be overridden from earliest to earliest
2025-08-20 19:52:24.524 [SourceCoordinator-Source: Kafka-交易] WARN  org.apache.kafka.clients.admin.AdminClientConfig - These configurations '[key.deserializer, value.deserializer, max.poll.records, client.id.prefix, group.id, partition.discovery.interval.ms, heartbeat.interval.ms, enable.auto.commit, fetch.max.wait.ms, fetch.min.bytes, session.timeout.ms, auto.offset.reset]' were supplied but are not used yet.
2025-08-20 19:52:24.524 [SourceCoordinator-Source: Kafka-组合订单] WARN  org.apache.kafka.clients.admin.AdminClientConfig - These configurations '[key.deserializer, value.deserializer, max.poll.records, client.id.prefix, group.id, partition.discovery.interval.ms, heartbeat.interval.ms, enable.auto.commit, fetch.max.wait.ms, fetch.min.bytes, session.timeout.ms, auto.offset.reset]' were supplied but are not used yet.
2025-08-20 19:52:24.524 [SourceCoordinator-Source: Kafka-单腿订单] WARN  org.apache.kafka.clients.admin.AdminClientConfig - These configurations '[key.deserializer, value.deserializer, max.poll.records, client.id.prefix, group.id, partition.discovery.interval.ms, heartbeat.interval.ms, enable.auto.commit, fetch.max.wait.ms, fetch.min.bytes, session.timeout.ms, auto.offset.reset]' were supplied but are not used yet.
2025-08-20 19:52:24.525 [SourceCoordinator-Source: Kafka-交易] INFO  org.apache.flink.connector.kafka.source.enumerator.KafkaSourceEnumerator - Starting the KafkaSourceEnumerator for consumer group orderbook-local-test-group without periodic partition discovery.
2025-08-20 19:52:24.525 [SourceCoordinator-Source: Kafka-单腿订单] WARN  org.apache.kafka.common.utils.AppInfoParser - Error registering AppInfo mbean
javax.management.InstanceAlreadyExistsException: kafka.admin.client:type=app-info,id=orderbook-local-test-group-enumerator-admin-client
	at com.sun.jmx.mbeanserver.Repository.addMBean(Repository.java:436) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerWithRepository(DefaultMBeanServerInterceptor.java:1865) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerDynamicMBean(DefaultMBeanServerInterceptor.java:960) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerObject(DefaultMBeanServerInterceptor.java:895) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerMBean(DefaultMBeanServerInterceptor.java:320) ~[?:?]
	at com.sun.jmx.mbeanserver.JmxMBeanServer.registerMBean(JmxMBeanServer.java:523) ~[?:?]
	at org.apache.kafka.common.utils.AppInfoParser.registerAppInfo(AppInfoParser.java:64) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.admin.KafkaAdminClient.<init>(KafkaAdminClient.java:602) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.admin.KafkaAdminClient.createInternal(KafkaAdminClient.java:544) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.admin.KafkaAdminClient.createInternal(KafkaAdminClient.java:488) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.admin.Admin.create(Admin.java:134) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.admin.AdminClient.create(AdminClient.java:39) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.flink.connector.kafka.source.enumerator.KafkaSourceEnumerator.getKafkaAdminClient(KafkaSourceEnumerator.java:441) ~[flink-connector-kafka-3.4.0-1.20.jar:3.4.0-1.20]
	at org.apache.flink.connector.kafka.source.enumerator.KafkaSourceEnumerator.start(KafkaSourceEnumerator.java:164) ~[flink-connector-kafka-3.4.0-1.20.jar:3.4.0-1.20]
	at org.apache.flink.runtime.source.coordinator.SourceCoordinator.lambda$start$1(SourceCoordinator.java:244) ~[flink-runtime-1.20.0.jar:1.20.0]
	at org.apache.flink.runtime.source.coordinator.SourceCoordinator.lambda$runInEventLoop$10(SourceCoordinator.java:533) ~[flink-runtime-1.20.0.jar:1.20.0]
	at org.apache.flink.util.ThrowableCatchingRunnable.run(ThrowableCatchingRunnable.java:40) [flink-core-1.20.0.jar:1.20.0]
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:539) [?:?]
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:264) [?:?]
	at java.util.concurrent.FutureTask.run(FutureTask.java) [?:?]
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:304) [?:?]
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136) [?:?]
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635) [?:?]
	at java.lang.Thread.run(Thread.java:840) [?:?]
2025-08-20 19:52:24.528 [SourceCoordinator-Source: Kafka-单腿订单] INFO  org.apache.flink.connector.kafka.source.enumerator.KafkaSourceEnumerator - Starting the KafkaSourceEnumerator for consumer group orderbook-local-test-group without periodic partition discovery.
2025-08-20 19:52:24.528 [SourceCoordinator-Source: Kafka-组合订单] WARN  org.apache.kafka.common.utils.AppInfoParser - Error registering AppInfo mbean
javax.management.InstanceAlreadyExistsException: kafka.admin.client:type=app-info,id=orderbook-local-test-group-enumerator-admin-client
	at com.sun.jmx.mbeanserver.Repository.addMBean(Repository.java:436) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerWithRepository(DefaultMBeanServerInterceptor.java:1865) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerDynamicMBean(DefaultMBeanServerInterceptor.java:960) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerObject(DefaultMBeanServerInterceptor.java:895) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerMBean(DefaultMBeanServerInterceptor.java:320) ~[?:?]
	at com.sun.jmx.mbeanserver.JmxMBeanServer.registerMBean(JmxMBeanServer.java:523) ~[?:?]
	at org.apache.kafka.common.utils.AppInfoParser.registerAppInfo(AppInfoParser.java:64) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.admin.KafkaAdminClient.<init>(KafkaAdminClient.java:602) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.admin.KafkaAdminClient.createInternal(KafkaAdminClient.java:544) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.admin.KafkaAdminClient.createInternal(KafkaAdminClient.java:488) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.admin.Admin.create(Admin.java:134) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.admin.AdminClient.create(AdminClient.java:39) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.flink.connector.kafka.source.enumerator.KafkaSourceEnumerator.getKafkaAdminClient(KafkaSourceEnumerator.java:441) ~[flink-connector-kafka-3.4.0-1.20.jar:3.4.0-1.20]
	at org.apache.flink.connector.kafka.source.enumerator.KafkaSourceEnumerator.start(KafkaSourceEnumerator.java:164) ~[flink-connector-kafka-3.4.0-1.20.jar:3.4.0-1.20]
	at org.apache.flink.runtime.source.coordinator.SourceCoordinator.lambda$start$1(SourceCoordinator.java:244) ~[flink-runtime-1.20.0.jar:1.20.0]
	at org.apache.flink.runtime.source.coordinator.SourceCoordinator.lambda$runInEventLoop$10(SourceCoordinator.java:533) ~[flink-runtime-1.20.0.jar:1.20.0]
	at org.apache.flink.util.ThrowableCatchingRunnable.run(ThrowableCatchingRunnable.java:40) [flink-core-1.20.0.jar:1.20.0]
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:539) [?:?]
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:264) [?:?]
	at java.util.concurrent.FutureTask.run(FutureTask.java) [?:?]
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:304) [?:?]
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136) [?:?]
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635) [?:?]
	at java.lang.Thread.run(Thread.java:840) [?:?]
2025-08-20 19:52:24.528 [SourceCoordinator-Source: Kafka-组合订单] INFO  org.apache.flink.connector.kafka.source.enumerator.KafkaSourceEnumerator - Starting the KafkaSourceEnumerator for consumer group orderbook-local-test-group without periodic partition discovery.
2025-08-20 19:52:24.681 [SourceCoordinator-Source: Kafka-交易] INFO  org.apache.flink.connector.kafka.source.enumerator.KafkaSourceEnumerator - Discovered new partitions: [trade_data_event-0]
2025-08-20 19:52:24.681 [SourceCoordinator-Source: Kafka-单腿订单] INFO  org.apache.flink.connector.kafka.source.enumerator.KafkaSourceEnumerator - Discovered new partitions: [singleleg_order_data_event-0]
2025-08-20 19:52:24.681 [SourceCoordinator-Source: Kafka-组合订单] INFO  org.apache.flink.connector.kafka.source.enumerator.KafkaSourceEnumerator - Discovered new partitions: [cmb_order_data_event-0]
2025-08-20 19:52:24.683 [SourceCoordinator-Source: Kafka-单腿订单] INFO  org.apache.flink.connector.kafka.source.enumerator.KafkaSourceEnumerator - Assigning splits to readers {0=[[Partition: singleleg_order_data_event-0, StartingOffset: -2, StoppingOffset: -9223372036854775808]]}
2025-08-20 19:52:24.683 [SourceCoordinator-Source: Kafka-组合订单] INFO  org.apache.flink.connector.kafka.source.enumerator.KafkaSourceEnumerator - Assigning splits to readers {0=[[Partition: cmb_order_data_event-0, StartingOffset: -2, StoppingOffset: -9223372036854775808]]}
2025-08-20 19:52:24.684 [SourceCoordinator-Source: Kafka-交易] INFO  org.apache.flink.connector.kafka.source.enumerator.KafkaSourceEnumerator - Assigning splits to readers {0=[[Partition: trade_data_event-0, StartingOffset: -2, StoppingOffset: -9223372036854775808]]}
2025-08-20 19:52:24.704 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 (1/1)#0] WARN  org.apache.kafka.clients.consumer.ConsumerConfig - These configurations '[client.id.prefix, partition.discovery.interval.ms]' were supplied but are not used yet.
2025-08-20 19:52:24.704 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 (1/1)#0] WARN  org.apache.kafka.clients.consumer.ConsumerConfig - These configurations '[client.id.prefix, partition.discovery.interval.ms]' were supplied but are not used yet.
2025-08-20 19:52:24.704 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] WARN  org.apache.kafka.clients.consumer.ConsumerConfig - These configurations '[client.id.prefix, partition.discovery.interval.ms]' were supplied but are not used yet.
2025-08-20 19:52:24.704 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] WARN  org.apache.kafka.common.utils.AppInfoParser - Error registering AppInfo mbean
javax.management.InstanceAlreadyExistsException: kafka.consumer:type=app-info,id=orderbook-local-test-group-0
	at com.sun.jmx.mbeanserver.Repository.addMBean(Repository.java:436) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerWithRepository(DefaultMBeanServerInterceptor.java:1865) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerDynamicMBean(DefaultMBeanServerInterceptor.java:960) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerObject(DefaultMBeanServerInterceptor.java:895) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerMBean(DefaultMBeanServerInterceptor.java:320) ~[?:?]
	at com.sun.jmx.mbeanserver.JmxMBeanServer.registerMBean(JmxMBeanServer.java:523) ~[?:?]
	at org.apache.kafka.common.utils.AppInfoParser.registerAppInfo(AppInfoParser.java:64) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.consumer.KafkaConsumer.<init>(KafkaConsumer.java:821) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.consumer.KafkaConsumer.<init>(KafkaConsumer.java:665) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.consumer.KafkaConsumer.<init>(KafkaConsumer.java:646) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.consumer.KafkaConsumer.<init>(KafkaConsumer.java:626) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.flink.connector.kafka.source.reader.KafkaPartitionSplitReader.<init>(KafkaPartitionSplitReader.java:97) ~[flink-connector-kafka-3.4.0-1.20.jar:3.4.0-1.20]
	at org.apache.flink.connector.kafka.source.KafkaSource.lambda$createReader$1(KafkaSource.java:185) ~[flink-connector-kafka-3.4.0-1.20.jar:3.4.0-1.20]
	at org.apache.flink.connector.base.source.reader.fetcher.SplitFetcherManager.createSplitFetcher(SplitFetcherManager.java:259) ~[flink-connector-base-1.20.0.jar:1.20.0]
	at org.apache.flink.connector.base.source.reader.fetcher.SingleThreadFetcherManager.addSplits(SingleThreadFetcherManager.java:148) ~[flink-connector-base-1.20.0.jar:1.20.0]
	at org.apache.flink.connector.base.source.reader.SourceReaderBase.addSplits(SourceReaderBase.java:315) ~[flink-connector-base-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.api.operators.SourceOperator.handleAddSplitsEvent(SourceOperator.java:606) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.api.operators.SourceOperator.handleOperatorEvent(SourceOperator.java:575) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.OperatorEventDispatcherImpl.dispatchEventToHandlers(OperatorEventDispatcherImpl.java:72) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.RegularOperatorChain.dispatchOperatorEvent(RegularOperatorChain.java:80) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.StreamTask.lambda$dispatchOperatorEvent$24(StreamTask.java:1609) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.StreamTaskActionExecutor$1.runThrowing(StreamTaskActionExecutor.java:50) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.mailbox.Mail.run(Mail.java:101) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.mailbox.MailboxProcessor.runMail(MailboxProcessor.java:414) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.mailbox.MailboxProcessor.processMailsWhenDefaultActionUnavailable(MailboxProcessor.java:383) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.mailbox.MailboxProcessor.processMail(MailboxProcessor.java:368) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.mailbox.MailboxProcessor.runMailboxLoop(MailboxProcessor.java:229) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.StreamTask.runMailboxLoop(StreamTask.java:973) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.StreamTask.invoke(StreamTask.java:917) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.runtime.taskmanager.Task.runWithSystemExitMonitoring(Task.java:970) [flink-runtime-1.20.0.jar:1.20.0]
	at org.apache.flink.runtime.taskmanager.Task.restoreAndInvoke(Task.java:949) [flink-runtime-1.20.0.jar:1.20.0]
	at org.apache.flink.runtime.taskmanager.Task.doRun(Task.java:763) [flink-runtime-1.20.0.jar:1.20.0]
	at org.apache.flink.runtime.taskmanager.Task.run(Task.java:575) [flink-runtime-1.20.0.jar:1.20.0]
	at java.lang.Thread.run(Thread.java:840) [?:?]
2025-08-20 19:52:24.704 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 (1/1)#0] WARN  org.apache.kafka.common.utils.AppInfoParser - Error registering AppInfo mbean
javax.management.InstanceAlreadyExistsException: kafka.consumer:type=app-info,id=orderbook-local-test-group-0
	at com.sun.jmx.mbeanserver.Repository.addMBean(Repository.java:436) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerWithRepository(DefaultMBeanServerInterceptor.java:1865) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerDynamicMBean(DefaultMBeanServerInterceptor.java:960) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerObject(DefaultMBeanServerInterceptor.java:895) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerMBean(DefaultMBeanServerInterceptor.java:320) ~[?:?]
	at com.sun.jmx.mbeanserver.JmxMBeanServer.registerMBean(JmxMBeanServer.java:523) ~[?:?]
	at org.apache.kafka.common.utils.AppInfoParser.registerAppInfo(AppInfoParser.java:64) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.consumer.KafkaConsumer.<init>(KafkaConsumer.java:821) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.consumer.KafkaConsumer.<init>(KafkaConsumer.java:665) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.consumer.KafkaConsumer.<init>(KafkaConsumer.java:646) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.consumer.KafkaConsumer.<init>(KafkaConsumer.java:626) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.flink.connector.kafka.source.reader.KafkaPartitionSplitReader.<init>(KafkaPartitionSplitReader.java:97) ~[flink-connector-kafka-3.4.0-1.20.jar:3.4.0-1.20]
	at org.apache.flink.connector.kafka.source.KafkaSource.lambda$createReader$1(KafkaSource.java:185) ~[flink-connector-kafka-3.4.0-1.20.jar:3.4.0-1.20]
	at org.apache.flink.connector.base.source.reader.fetcher.SplitFetcherManager.createSplitFetcher(SplitFetcherManager.java:259) ~[flink-connector-base-1.20.0.jar:1.20.0]
	at org.apache.flink.connector.base.source.reader.fetcher.SingleThreadFetcherManager.addSplits(SingleThreadFetcherManager.java:148) ~[flink-connector-base-1.20.0.jar:1.20.0]
	at org.apache.flink.connector.base.source.reader.SourceReaderBase.addSplits(SourceReaderBase.java:315) ~[flink-connector-base-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.api.operators.SourceOperator.handleAddSplitsEvent(SourceOperator.java:606) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.api.operators.SourceOperator.handleOperatorEvent(SourceOperator.java:575) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.OperatorEventDispatcherImpl.dispatchEventToHandlers(OperatorEventDispatcherImpl.java:72) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.RegularOperatorChain.dispatchOperatorEvent(RegularOperatorChain.java:80) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.StreamTask.lambda$dispatchOperatorEvent$24(StreamTask.java:1609) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.StreamTaskActionExecutor$1.runThrowing(StreamTaskActionExecutor.java:50) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.mailbox.Mail.run(Mail.java:101) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.mailbox.MailboxProcessor.runMail(MailboxProcessor.java:414) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.mailbox.MailboxProcessor.processMailsWhenDefaultActionUnavailable(MailboxProcessor.java:383) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.mailbox.MailboxProcessor.processMail(MailboxProcessor.java:368) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.mailbox.MailboxProcessor.runMailboxLoop(MailboxProcessor.java:229) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.StreamTask.runMailboxLoop(StreamTask.java:973) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.StreamTask.invoke(StreamTask.java:917) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.runtime.taskmanager.Task.runWithSystemExitMonitoring(Task.java:970) [flink-runtime-1.20.0.jar:1.20.0]
	at org.apache.flink.runtime.taskmanager.Task.restoreAndInvoke(Task.java:949) [flink-runtime-1.20.0.jar:1.20.0]
	at org.apache.flink.runtime.taskmanager.Task.doRun(Task.java:763) [flink-runtime-1.20.0.jar:1.20.0]
	at org.apache.flink.runtime.taskmanager.Task.run(Task.java:575) [flink-runtime-1.20.0.jar:1.20.0]
	at java.lang.Thread.run(Thread.java:840) [?:?]
