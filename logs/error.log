2025-08-20 14:33:38.137 [main] WARN  [o.a.f.c.k.s.KafkaSourceBuilder:497] - Property auto.offset.reset is provided but will be overridden from earliest to earliest
2025-08-20 14:33:38.154 [main] WARN  [o.a.f.c.k.s.KafkaSourceBuilder:497] - Property auto.offset.reset is provided but will be overridden from earliest to earliest
2025-08-20 14:33:38.155 [main] WARN  [o.a.f.c.k.s.KafkaSourceBuilder:497] - Property auto.offset.reset is provided but will be overridden from earliest to earliest
2025-08-20 14:33:38.795 [main] WARN  [o.a.f.r.s.t.DefaultDelegationTokenManager:248] - No tokens obtained so skipping notifications
2025-08-20 14:33:38.919 [main] WARN  [o.a.f.r.w.WebMonitorUtils:76] - Log file environment variable 'log.file' is not set.
2025-08-20 14:33:38.919 [main] WARN  [o.a.f.r.w.WebMonitorUtils:82] - JobManager log files are unavailable in the web dashboard. Log file location not found in environment variable 'log.file' or configuration key 'web.log.path'.
2025-08-20 14:33:38.990 [flink-pekko.actor.default-dispatcher-4] WARN  [o.a.f.r.s.t.DefaultDelegationTokenManager:320] - No tokens obtained so skipping notifications
2025-08-20 14:33:38.991 [flink-pekko.actor.default-dispatcher-4] WARN  [o.a.f.r.s.t.DefaultDelegationTokenManager:335] - Tokens update task not started because either no tokens obtained or none of the tokens specified its renewal date
2025-08-20 14:33:39.106 [SourceCoordinator-Source: Kafka-组合订单] WARN  [o.a.k.c.a.AdminClientConfig:385] - These configurations '[key.deserializer, value.deserializer, max.poll.records, client.id.prefix, group.id, partition.discovery.interval.ms, heartbeat.interval.ms, enable.auto.commit, fetch.max.wait.ms, fetch.min.bytes, session.timeout.ms, auto.offset.reset]' were supplied but are not used yet.
2025-08-20 14:33:39.107 [SourceCoordinator-Source: Kafka-单腿订单] WARN  [o.a.k.c.a.AdminClientConfig:385] - These configurations '[key.deserializer, value.deserializer, max.poll.records, client.id.prefix, group.id, partition.discovery.interval.ms, heartbeat.interval.ms, enable.auto.commit, fetch.max.wait.ms, fetch.min.bytes, session.timeout.ms, auto.offset.reset]' were supplied but are not used yet.
2025-08-20 14:33:39.106 [SourceCoordinator-Source: Kafka-交易] WARN  [o.a.k.c.a.AdminClientConfig:385] - These configurations '[key.deserializer, value.deserializer, max.poll.records, client.id.prefix, group.id, partition.discovery.interval.ms, heartbeat.interval.ms, enable.auto.commit, fetch.max.wait.ms, fetch.min.bytes, session.timeout.ms, auto.offset.reset]' were supplied but are not used yet.
2025-08-20 14:33:39.108 [SourceCoordinator-Source: Kafka-交易] WARN  [o.a.k.c.u.AppInfoParser:68] - Error registering AppInfo mbean
javax.management.InstanceAlreadyExistsException: kafka.admin.client:type=app-info,id=orderbook-local-test-group-enumerator-admin-client
	at com.sun.jmx.mbeanserver.Repository.addMBean(Repository.java:436) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerWithRepository(DefaultMBeanServerInterceptor.java:1865) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerDynamicMBean(DefaultMBeanServerInterceptor.java:960) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerObject(DefaultMBeanServerInterceptor.java:895) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerMBean(DefaultMBeanServerInterceptor.java:320) ~[?:?]
	at com.sun.jmx.mbeanserver.JmxMBeanServer.registerMBean(JmxMBeanServer.java:523) ~[?:?]
	at org.apache.kafka.common.utils.AppInfoParser.registerAppInfo(AppInfoParser.java:64) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.admin.KafkaAdminClient.<init>(KafkaAdminClient.java:602) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.admin.KafkaAdminClient.createInternal(KafkaAdminClient.java:544) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.admin.KafkaAdminClient.createInternal(KafkaAdminClient.java:488) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.admin.Admin.create(Admin.java:134) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.admin.AdminClient.create(AdminClient.java:39) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.flink.connector.kafka.source.enumerator.KafkaSourceEnumerator.getKafkaAdminClient(KafkaSourceEnumerator.java:441) ~[flink-connector-kafka-3.4.0-1.20.jar:3.4.0-1.20]
	at org.apache.flink.connector.kafka.source.enumerator.KafkaSourceEnumerator.start(KafkaSourceEnumerator.java:164) ~[flink-connector-kafka-3.4.0-1.20.jar:3.4.0-1.20]
	at org.apache.flink.runtime.source.coordinator.SourceCoordinator.lambda$start$1(SourceCoordinator.java:244) ~[flink-runtime-1.20.0.jar:1.20.0]
	at org.apache.flink.runtime.source.coordinator.SourceCoordinator.lambda$runInEventLoop$10(SourceCoordinator.java:533) ~[flink-runtime-1.20.0.jar:1.20.0]
	at org.apache.flink.util.ThrowableCatchingRunnable.run(ThrowableCatchingRunnable.java:40) [flink-core-1.20.0.jar:1.20.0]
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:539) [?:?]
	at java.util.concurrent.FutureTask.run(FutureTask.java:264) [?:?]
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:304) [?:?]
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136) [?:?]
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635) [?:?]
	at java.lang.Thread.run(Thread.java:840) [?:?]
2025-08-20 14:33:39.110 [SourceCoordinator-Source: Kafka-单腿订单] WARN  [o.a.k.c.u.AppInfoParser:68] - Error registering AppInfo mbean
javax.management.InstanceAlreadyExistsException: kafka.admin.client:type=app-info,id=orderbook-local-test-group-enumerator-admin-client
	at com.sun.jmx.mbeanserver.Repository.addMBean(Repository.java:436) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerWithRepository(DefaultMBeanServerInterceptor.java:1865) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerDynamicMBean(DefaultMBeanServerInterceptor.java:960) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerObject(DefaultMBeanServerInterceptor.java:895) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerMBean(DefaultMBeanServerInterceptor.java:320) ~[?:?]
	at com.sun.jmx.mbeanserver.JmxMBeanServer.registerMBean(JmxMBeanServer.java:523) ~[?:?]
	at org.apache.kafka.common.utils.AppInfoParser.registerAppInfo(AppInfoParser.java:64) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.admin.KafkaAdminClient.<init>(KafkaAdminClient.java:602) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.admin.KafkaAdminClient.createInternal(KafkaAdminClient.java:544) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.admin.KafkaAdminClient.createInternal(KafkaAdminClient.java:488) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.admin.Admin.create(Admin.java:134) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.admin.AdminClient.create(AdminClient.java:39) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.flink.connector.kafka.source.enumerator.KafkaSourceEnumerator.getKafkaAdminClient(KafkaSourceEnumerator.java:441) ~[flink-connector-kafka-3.4.0-1.20.jar:3.4.0-1.20]
	at org.apache.flink.connector.kafka.source.enumerator.KafkaSourceEnumerator.start(KafkaSourceEnumerator.java:164) ~[flink-connector-kafka-3.4.0-1.20.jar:3.4.0-1.20]
	at org.apache.flink.runtime.source.coordinator.SourceCoordinator.lambda$start$1(SourceCoordinator.java:244) ~[flink-runtime-1.20.0.jar:1.20.0]
	at org.apache.flink.runtime.source.coordinator.SourceCoordinator.lambda$runInEventLoop$10(SourceCoordinator.java:533) ~[flink-runtime-1.20.0.jar:1.20.0]
	at org.apache.flink.util.ThrowableCatchingRunnable.run(ThrowableCatchingRunnable.java:40) [flink-core-1.20.0.jar:1.20.0]
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:539) [?:?]
	at java.util.concurrent.FutureTask.run(FutureTask.java:264) [?:?]
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:304) [?:?]
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136) [?:?]
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635) [?:?]
	at java.lang.Thread.run(Thread.java:840) [?:?]
2025-08-20 14:33:39.266 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] WARN  [o.a.k.c.c.ConsumerConfig:385] - These configurations '[client.id.prefix, partition.discovery.interval.ms]' were supplied but are not used yet.
2025-08-20 14:33:39.276 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] WARN  [o.a.k.c.c.ConsumerConfig:385] - These configurations '[client.id.prefix, partition.discovery.interval.ms]' were supplied but are not used yet.
2025-08-20 14:33:39.276 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] WARN  [o.a.k.c.c.ConsumerConfig:385] - These configurations '[client.id.prefix, partition.discovery.interval.ms]' were supplied but are not used yet.
2025-08-20 14:33:39.276 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] WARN  [o.a.k.c.u.AppInfoParser:68] - Error registering AppInfo mbean
javax.management.InstanceAlreadyExistsException: kafka.consumer:type=app-info,id=orderbook-local-test-group-0
	at com.sun.jmx.mbeanserver.Repository.addMBean(Repository.java:436) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerWithRepository(DefaultMBeanServerInterceptor.java:1865) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerDynamicMBean(DefaultMBeanServerInterceptor.java:960) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerObject(DefaultMBeanServerInterceptor.java:895) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerMBean(DefaultMBeanServerInterceptor.java:320) ~[?:?]
	at com.sun.jmx.mbeanserver.JmxMBeanServer.registerMBean(JmxMBeanServer.java:523) ~[?:?]
	at org.apache.kafka.common.utils.AppInfoParser.registerAppInfo(AppInfoParser.java:64) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.consumer.KafkaConsumer.<init>(KafkaConsumer.java:821) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.consumer.KafkaConsumer.<init>(KafkaConsumer.java:665) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.consumer.KafkaConsumer.<init>(KafkaConsumer.java:646) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.consumer.KafkaConsumer.<init>(KafkaConsumer.java:626) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.flink.connector.kafka.source.reader.KafkaPartitionSplitReader.<init>(KafkaPartitionSplitReader.java:97) ~[flink-connector-kafka-3.4.0-1.20.jar:3.4.0-1.20]
	at org.apache.flink.connector.kafka.source.KafkaSource.lambda$createReader$1(KafkaSource.java:185) ~[flink-connector-kafka-3.4.0-1.20.jar:3.4.0-1.20]
	at org.apache.flink.connector.base.source.reader.fetcher.SplitFetcherManager.createSplitFetcher(SplitFetcherManager.java:259) ~[flink-connector-base-1.20.0.jar:1.20.0]
	at org.apache.flink.connector.base.source.reader.fetcher.SingleThreadFetcherManager.addSplits(SingleThreadFetcherManager.java:148) ~[flink-connector-base-1.20.0.jar:1.20.0]
	at org.apache.flink.connector.base.source.reader.SourceReaderBase.addSplits(SourceReaderBase.java:315) ~[flink-connector-base-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.api.operators.SourceOperator.handleAddSplitsEvent(SourceOperator.java:606) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.api.operators.SourceOperator.handleOperatorEvent(SourceOperator.java:575) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.OperatorEventDispatcherImpl.dispatchEventToHandlers(OperatorEventDispatcherImpl.java:72) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.RegularOperatorChain.dispatchOperatorEvent(RegularOperatorChain.java:80) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.StreamTask.lambda$dispatchOperatorEvent$24(StreamTask.java:1609) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.StreamTaskActionExecutor$1.runThrowing(StreamTaskActionExecutor.java:50) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.mailbox.Mail.run(Mail.java:101) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.mailbox.MailboxProcessor.runMail(MailboxProcessor.java:414) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.mailbox.MailboxProcessor.processMailsWhenDefaultActionUnavailable(MailboxProcessor.java:383) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.mailbox.MailboxProcessor.processMail(MailboxProcessor.java:368) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.mailbox.MailboxProcessor.runMailboxLoop(MailboxProcessor.java:229) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.StreamTask.runMailboxLoop(StreamTask.java:973) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.StreamTask.invoke(StreamTask.java:917) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.runtime.taskmanager.Task.runWithSystemExitMonitoring(Task.java:970) [flink-runtime-1.20.0.jar:1.20.0]
	at org.apache.flink.runtime.taskmanager.Task.restoreAndInvoke(Task.java:949) [flink-runtime-1.20.0.jar:1.20.0]
	at org.apache.flink.runtime.taskmanager.Task.doRun(Task.java:763) [flink-runtime-1.20.0.jar:1.20.0]
	at org.apache.flink.runtime.taskmanager.Task.run(Task.java:575) [flink-runtime-1.20.0.jar:1.20.0]
	at java.lang.Thread.run(Thread.java:840) [?:?]
2025-08-20 14:33:39.277 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] WARN  [o.a.k.c.u.AppInfoParser:68] - Error registering AppInfo mbean
javax.management.InstanceAlreadyExistsException: kafka.consumer:type=app-info,id=orderbook-local-test-group-0
	at com.sun.jmx.mbeanserver.Repository.addMBean(Repository.java:436) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerWithRepository(DefaultMBeanServerInterceptor.java:1865) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerDynamicMBean(DefaultMBeanServerInterceptor.java:960) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerObject(DefaultMBeanServerInterceptor.java:895) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerMBean(DefaultMBeanServerInterceptor.java:320) ~[?:?]
	at com.sun.jmx.mbeanserver.JmxMBeanServer.registerMBean(JmxMBeanServer.java:523) ~[?:?]
	at org.apache.kafka.common.utils.AppInfoParser.registerAppInfo(AppInfoParser.java:64) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.consumer.KafkaConsumer.<init>(KafkaConsumer.java:821) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.consumer.KafkaConsumer.<init>(KafkaConsumer.java:665) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.consumer.KafkaConsumer.<init>(KafkaConsumer.java:646) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.consumer.KafkaConsumer.<init>(KafkaConsumer.java:626) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.flink.connector.kafka.source.reader.KafkaPartitionSplitReader.<init>(KafkaPartitionSplitReader.java:97) ~[flink-connector-kafka-3.4.0-1.20.jar:3.4.0-1.20]
	at org.apache.flink.connector.kafka.source.KafkaSource.lambda$createReader$1(KafkaSource.java:185) ~[flink-connector-kafka-3.4.0-1.20.jar:3.4.0-1.20]
	at org.apache.flink.connector.base.source.reader.fetcher.SplitFetcherManager.createSplitFetcher(SplitFetcherManager.java:259) ~[flink-connector-base-1.20.0.jar:1.20.0]
	at org.apache.flink.connector.base.source.reader.fetcher.SingleThreadFetcherManager.addSplits(SingleThreadFetcherManager.java:148) ~[flink-connector-base-1.20.0.jar:1.20.0]
	at org.apache.flink.connector.base.source.reader.SourceReaderBase.addSplits(SourceReaderBase.java:315) ~[flink-connector-base-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.api.operators.SourceOperator.handleAddSplitsEvent(SourceOperator.java:606) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.api.operators.SourceOperator.handleOperatorEvent(SourceOperator.java:575) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.OperatorEventDispatcherImpl.dispatchEventToHandlers(OperatorEventDispatcherImpl.java:72) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.RegularOperatorChain.dispatchOperatorEvent(RegularOperatorChain.java:80) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.StreamTask.lambda$dispatchOperatorEvent$24(StreamTask.java:1609) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.StreamTaskActionExecutor$1.runThrowing(StreamTaskActionExecutor.java:50) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.mailbox.Mail.run(Mail.java:101) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.mailbox.MailboxProcessor.runMail(MailboxProcessor.java:414) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.mailbox.MailboxProcessor.processMailsWhenDefaultActionUnavailable(MailboxProcessor.java:383) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.mailbox.MailboxProcessor.processMail(MailboxProcessor.java:368) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.mailbox.MailboxProcessor.runMailboxLoop(MailboxProcessor.java:229) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.StreamTask.runMailboxLoop(StreamTask.java:973) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.StreamTask.invoke(StreamTask.java:917) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.runtime.taskmanager.Task.runWithSystemExitMonitoring(Task.java:970) [flink-runtime-1.20.0.jar:1.20.0]
	at org.apache.flink.runtime.taskmanager.Task.restoreAndInvoke(Task.java:949) [flink-runtime-1.20.0.jar:1.20.0]
	at org.apache.flink.runtime.taskmanager.Task.doRun(Task.java:763) [flink-runtime-1.20.0.jar:1.20.0]
	at org.apache.flink.runtime.taskmanager.Task.run(Task.java:575) [flink-runtime-1.20.0.jar:1.20.0]
	at java.lang.Thread.run(Thread.java:840) [?:?]
2025-08-20 14:33:39.394 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] WARN  [c.f.f.PnLCalculationFunction:180] - 发现无持仓的平仓交易 - 发送到异常流: 交易号=101355625, 合约=JD2504-C-3200, 方向=S, 数量=5
2025-08-20 14:33:39.395 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] WARN  [c.f.f.PnLCalculationFunction:180] - 发现无持仓的平仓交易 - 发送到异常流: 交易号=101355626, 合约=JD2504-C-3200, 方向=S, 数量=5
2025-08-20 14:33:39.395 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] WARN  [c.f.f.PnLCalculationFunction:180] - 发现无持仓的平仓交易 - 发送到异常流: 交易号=101355700, 合约=EB2505, 方向=B, 数量=10
2025-08-20 14:33:39.396 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] WARN  [c.f.f.PnLCalculationFunction:180] - 发现无持仓的平仓交易 - 发送到异常流: 交易号=48060, 合约=SH2505, 方向=S, 数量=1
2025-08-20 14:33:39.396 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] WARN  [c.f.f.PnLCalculationFunction:180] - 发现无持仓的平仓交易 - 发送到异常流: 交易号=100052597, 合约=EB2505, 方向=B, 数量=8
2025-08-20 14:33:39.396 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] WARN  [c.f.f.PnLCalculationFunction:180] - 发现无持仓的平仓交易 - 发送到异常流: 交易号=100052597, 合约=EB2505, 方向=S, 数量=8
2025-08-20 14:33:39.396 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] WARN  [c.f.f.PnLCalculationFunction:180] - 发现无持仓的平仓交易 - 发送到异常流: 交易号=100052678, 合约=EB2505, 方向=B, 数量=1
2025-08-20 14:33:39.396 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] WARN  [c.f.f.PnLCalculationFunction:180] - 发现无持仓的平仓交易 - 发送到异常流: 交易号=100052678, 合约=EB2505, 方向=S, 数量=1
2025-08-20 14:33:39.396 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] WARN  [c.f.f.PnLCalculationFunction:180] - 发现无持仓的平仓交易 - 发送到异常流: 交易号=100052768, 合约=EB2505, 方向=B, 数量=1
2025-08-20 14:33:39.398 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] WARN  [c.f.f.PnLCalculationFunction:180] - 发现无持仓的平仓交易 - 发送到异常流: 交易号=100052770, 合约=EB2505, 方向=B, 数量=1
2025-08-20 14:33:39.398 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] WARN  [c.f.f.PnLCalculationFunction:180] - 发现无持仓的平仓交易 - 发送到异常流: 交易号=100052795, 合约=EB2505, 方向=B, 数量=1
2025-08-20 14:33:39.399 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] WARN  [c.f.f.PnLCalculationFunction:180] - 发现无持仓的平仓交易 - 发送到异常流: 交易号=100052821, 合约=EB2505, 方向=B, 数量=1
2025-08-20 14:33:39.400 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] WARN  [c.f.f.PnLCalculationFunction:180] - 发现无持仓的平仓交易 - 发送到异常流: 交易号=100052822, 合约=EB2505, 方向=B, 数量=2
2025-08-20 14:33:39.400 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] WARN  [c.f.f.PnLCalculationFunction:180] - 发现无持仓的平仓交易 - 发送到异常流: 交易号=100052833, 合约=EB2505, 方向=B, 数量=2
2025-08-20 14:33:39.401 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] WARN  [c.f.f.PnLCalculationFunction:180] - 发现无持仓的平仓交易 - 发送到异常流: 交易号=100052846, 合约=EB2504, 方向=B, 数量=1
2025-08-20 14:33:39.401 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] WARN  [c.f.f.PnLCalculationFunction:180] - 发现无持仓的平仓交易 - 发送到异常流: 交易号=100052847, 合约=EB2505, 方向=B, 数量=1
2025-08-20 14:33:39.402 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] WARN  [c.f.f.PnLCalculationFunction:180] - 发现无持仓的平仓交易 - 发送到异常流: 交易号=100052849, 合约=EB2504, 方向=S, 数量=10
2025-08-20 14:36:43.567 [main] WARN  [o.a.f.c.k.s.KafkaSourceBuilder:497] - Property auto.offset.reset is provided but will be overridden from earliest to earliest
2025-08-20 14:36:43.585 [main] WARN  [o.a.f.c.k.s.KafkaSourceBuilder:497] - Property auto.offset.reset is provided but will be overridden from earliest to earliest
2025-08-20 14:36:43.585 [main] WARN  [o.a.f.c.k.s.KafkaSourceBuilder:497] - Property auto.offset.reset is provided but will be overridden from earliest to earliest
2025-08-20 14:36:44.061 [main] WARN  [o.a.f.r.s.t.DefaultDelegationTokenManager:248] - No tokens obtained so skipping notifications
2025-08-20 14:36:44.167 [main] WARN  [o.a.f.r.w.WebMonitorUtils:76] - Log file environment variable 'log.file' is not set.
2025-08-20 14:36:44.167 [main] WARN  [o.a.f.r.w.WebMonitorUtils:82] - JobManager log files are unavailable in the web dashboard. Log file location not found in environment variable 'log.file' or configuration key 'web.log.path'.
2025-08-20 14:36:44.229 [flink-pekko.actor.default-dispatcher-4] WARN  [o.a.f.r.s.t.DefaultDelegationTokenManager:320] - No tokens obtained so skipping notifications
2025-08-20 14:36:44.229 [flink-pekko.actor.default-dispatcher-4] WARN  [o.a.f.r.s.t.DefaultDelegationTokenManager:335] - Tokens update task not started because either no tokens obtained or none of the tokens specified its renewal date
2025-08-20 14:36:44.332 [SourceCoordinator-Source: Kafka-单腿订单] WARN  [o.a.k.c.a.AdminClientConfig:385] - These configurations '[key.deserializer, value.deserializer, max.poll.records, client.id.prefix, group.id, partition.discovery.interval.ms, heartbeat.interval.ms, enable.auto.commit, fetch.max.wait.ms, fetch.min.bytes, session.timeout.ms, auto.offset.reset]' were supplied but are not used yet.
2025-08-20 14:36:44.332 [SourceCoordinator-Source: Kafka-交易] WARN  [o.a.k.c.a.AdminClientConfig:385] - These configurations '[key.deserializer, value.deserializer, max.poll.records, client.id.prefix, group.id, partition.discovery.interval.ms, heartbeat.interval.ms, enable.auto.commit, fetch.max.wait.ms, fetch.min.bytes, session.timeout.ms, auto.offset.reset]' were supplied but are not used yet.
2025-08-20 14:36:44.332 [SourceCoordinator-Source: Kafka-组合订单] WARN  [o.a.k.c.a.AdminClientConfig:385] - These configurations '[key.deserializer, value.deserializer, max.poll.records, client.id.prefix, group.id, partition.discovery.interval.ms, heartbeat.interval.ms, enable.auto.commit, fetch.max.wait.ms, fetch.min.bytes, session.timeout.ms, auto.offset.reset]' were supplied but are not used yet.
2025-08-20 14:36:44.334 [SourceCoordinator-Source: Kafka-交易] WARN  [o.a.k.c.u.AppInfoParser:68] - Error registering AppInfo mbean
javax.management.InstanceAlreadyExistsException: kafka.admin.client:type=app-info,id=orderbook-local-test-group-enumerator-admin-client
	at com.sun.jmx.mbeanserver.Repository.addMBean(Repository.java:436) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerWithRepository(DefaultMBeanServerInterceptor.java:1865) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerDynamicMBean(DefaultMBeanServerInterceptor.java:960) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerObject(DefaultMBeanServerInterceptor.java:895) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerMBean(DefaultMBeanServerInterceptor.java:320) ~[?:?]
	at com.sun.jmx.mbeanserver.JmxMBeanServer.registerMBean(JmxMBeanServer.java:523) ~[?:?]
	at org.apache.kafka.common.utils.AppInfoParser.registerAppInfo(AppInfoParser.java:64) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.admin.KafkaAdminClient.<init>(KafkaAdminClient.java:602) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.admin.KafkaAdminClient.createInternal(KafkaAdminClient.java:544) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.admin.KafkaAdminClient.createInternal(KafkaAdminClient.java:488) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.admin.Admin.create(Admin.java:134) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.admin.AdminClient.create(AdminClient.java:39) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.flink.connector.kafka.source.enumerator.KafkaSourceEnumerator.getKafkaAdminClient(KafkaSourceEnumerator.java:441) ~[flink-connector-kafka-3.4.0-1.20.jar:3.4.0-1.20]
	at org.apache.flink.connector.kafka.source.enumerator.KafkaSourceEnumerator.start(KafkaSourceEnumerator.java:164) ~[flink-connector-kafka-3.4.0-1.20.jar:3.4.0-1.20]
	at org.apache.flink.runtime.source.coordinator.SourceCoordinator.lambda$start$1(SourceCoordinator.java:244) ~[flink-runtime-1.20.0.jar:1.20.0]
	at org.apache.flink.runtime.source.coordinator.SourceCoordinator.lambda$runInEventLoop$10(SourceCoordinator.java:533) ~[flink-runtime-1.20.0.jar:1.20.0]
	at org.apache.flink.util.ThrowableCatchingRunnable.run(ThrowableCatchingRunnable.java:40) [flink-core-1.20.0.jar:1.20.0]
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:539) [?:?]
	at java.util.concurrent.FutureTask.run(FutureTask.java:264) [?:?]
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:304) [?:?]
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136) [?:?]
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635) [?:?]
	at java.lang.Thread.run(Thread.java:840) [?:?]
2025-08-20 14:36:44.337 [SourceCoordinator-Source: Kafka-组合订单] WARN  [o.a.k.c.u.AppInfoParser:68] - Error registering AppInfo mbean
javax.management.InstanceAlreadyExistsException: kafka.admin.client:type=app-info,id=orderbook-local-test-group-enumerator-admin-client
	at com.sun.jmx.mbeanserver.Repository.addMBean(Repository.java:436) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerWithRepository(DefaultMBeanServerInterceptor.java:1865) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerDynamicMBean(DefaultMBeanServerInterceptor.java:960) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerObject(DefaultMBeanServerInterceptor.java:895) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerMBean(DefaultMBeanServerInterceptor.java:320) ~[?:?]
	at com.sun.jmx.mbeanserver.JmxMBeanServer.registerMBean(JmxMBeanServer.java:523) ~[?:?]
	at org.apache.kafka.common.utils.AppInfoParser.registerAppInfo(AppInfoParser.java:64) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.admin.KafkaAdminClient.<init>(KafkaAdminClient.java:602) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.admin.KafkaAdminClient.createInternal(KafkaAdminClient.java:544) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.admin.KafkaAdminClient.createInternal(KafkaAdminClient.java:488) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.admin.Admin.create(Admin.java:134) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.admin.AdminClient.create(AdminClient.java:39) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.flink.connector.kafka.source.enumerator.KafkaSourceEnumerator.getKafkaAdminClient(KafkaSourceEnumerator.java:441) ~[flink-connector-kafka-3.4.0-1.20.jar:3.4.0-1.20]
	at org.apache.flink.connector.kafka.source.enumerator.KafkaSourceEnumerator.start(KafkaSourceEnumerator.java:164) ~[flink-connector-kafka-3.4.0-1.20.jar:3.4.0-1.20]
	at org.apache.flink.runtime.source.coordinator.SourceCoordinator.lambda$start$1(SourceCoordinator.java:244) ~[flink-runtime-1.20.0.jar:1.20.0]
	at org.apache.flink.runtime.source.coordinator.SourceCoordinator.lambda$runInEventLoop$10(SourceCoordinator.java:533) ~[flink-runtime-1.20.0.jar:1.20.0]
	at org.apache.flink.util.ThrowableCatchingRunnable.run(ThrowableCatchingRunnable.java:40) [flink-core-1.20.0.jar:1.20.0]
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:539) [?:?]
	at java.util.concurrent.FutureTask.run(FutureTask.java:264) [?:?]
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:304) [?:?]
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136) [?:?]
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635) [?:?]
	at java.lang.Thread.run(Thread.java:840) [?:?]
2025-08-20 14:36:44.497 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] WARN  [o.a.k.c.c.ConsumerConfig:385] - These configurations '[client.id.prefix, partition.discovery.interval.ms]' were supplied but are not used yet.
2025-08-20 14:36:44.497 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] WARN  [o.a.k.c.c.ConsumerConfig:385] - These configurations '[client.id.prefix, partition.discovery.interval.ms]' were supplied but are not used yet.
2025-08-20 14:36:44.497 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] WARN  [o.a.k.c.c.ConsumerConfig:385] - These configurations '[client.id.prefix, partition.discovery.interval.ms]' were supplied but are not used yet.
2025-08-20 14:36:44.497 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] WARN  [o.a.k.c.u.AppInfoParser:68] - Error registering AppInfo mbean
javax.management.InstanceAlreadyExistsException: kafka.consumer:type=app-info,id=orderbook-local-test-group-0
	at com.sun.jmx.mbeanserver.Repository.addMBean(Repository.java:436) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerWithRepository(DefaultMBeanServerInterceptor.java:1865) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerDynamicMBean(DefaultMBeanServerInterceptor.java:960) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerObject(DefaultMBeanServerInterceptor.java:895) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerMBean(DefaultMBeanServerInterceptor.java:320) ~[?:?]
	at com.sun.jmx.mbeanserver.JmxMBeanServer.registerMBean(JmxMBeanServer.java:523) ~[?:?]
	at org.apache.kafka.common.utils.AppInfoParser.registerAppInfo(AppInfoParser.java:64) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.consumer.KafkaConsumer.<init>(KafkaConsumer.java:821) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.consumer.KafkaConsumer.<init>(KafkaConsumer.java:665) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.consumer.KafkaConsumer.<init>(KafkaConsumer.java:646) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.consumer.KafkaConsumer.<init>(KafkaConsumer.java:626) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.flink.connector.kafka.source.reader.KafkaPartitionSplitReader.<init>(KafkaPartitionSplitReader.java:97) ~[flink-connector-kafka-3.4.0-1.20.jar:3.4.0-1.20]
	at org.apache.flink.connector.kafka.source.KafkaSource.lambda$createReader$1(KafkaSource.java:185) ~[flink-connector-kafka-3.4.0-1.20.jar:3.4.0-1.20]
	at org.apache.flink.connector.base.source.reader.fetcher.SplitFetcherManager.createSplitFetcher(SplitFetcherManager.java:259) ~[flink-connector-base-1.20.0.jar:1.20.0]
	at org.apache.flink.connector.base.source.reader.fetcher.SingleThreadFetcherManager.addSplits(SingleThreadFetcherManager.java:148) ~[flink-connector-base-1.20.0.jar:1.20.0]
	at org.apache.flink.connector.base.source.reader.SourceReaderBase.addSplits(SourceReaderBase.java:315) ~[flink-connector-base-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.api.operators.SourceOperator.handleAddSplitsEvent(SourceOperator.java:606) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.api.operators.SourceOperator.handleOperatorEvent(SourceOperator.java:575) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.OperatorEventDispatcherImpl.dispatchEventToHandlers(OperatorEventDispatcherImpl.java:72) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.RegularOperatorChain.dispatchOperatorEvent(RegularOperatorChain.java:80) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.StreamTask.lambda$dispatchOperatorEvent$24(StreamTask.java:1609) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.StreamTaskActionExecutor$1.runThrowing(StreamTaskActionExecutor.java:50) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.mailbox.Mail.run(Mail.java:101) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.mailbox.MailboxProcessor.runMail(MailboxProcessor.java:414) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.mailbox.MailboxProcessor.processMailsWhenDefaultActionUnavailable(MailboxProcessor.java:383) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.mailbox.MailboxProcessor.processMail(MailboxProcessor.java:368) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.mailbox.MailboxProcessor.runMailboxLoop(MailboxProcessor.java:229) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.StreamTask.runMailboxLoop(StreamTask.java:973) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.StreamTask.invoke(StreamTask.java:917) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.runtime.taskmanager.Task.runWithSystemExitMonitoring(Task.java:970) [flink-runtime-1.20.0.jar:1.20.0]
	at org.apache.flink.runtime.taskmanager.Task.restoreAndInvoke(Task.java:949) [flink-runtime-1.20.0.jar:1.20.0]
	at org.apache.flink.runtime.taskmanager.Task.doRun(Task.java:763) [flink-runtime-1.20.0.jar:1.20.0]
	at org.apache.flink.runtime.taskmanager.Task.run(Task.java:575) [flink-runtime-1.20.0.jar:1.20.0]
	at java.lang.Thread.run(Thread.java:840) [?:?]
2025-08-20 14:36:44.497 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] WARN  [o.a.k.c.u.AppInfoParser:68] - Error registering AppInfo mbean
javax.management.InstanceAlreadyExistsException: kafka.consumer:type=app-info,id=orderbook-local-test-group-0
	at com.sun.jmx.mbeanserver.Repository.addMBean(Repository.java:436) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerWithRepository(DefaultMBeanServerInterceptor.java:1865) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerDynamicMBean(DefaultMBeanServerInterceptor.java:960) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerObject(DefaultMBeanServerInterceptor.java:895) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerMBean(DefaultMBeanServerInterceptor.java:320) ~[?:?]
	at com.sun.jmx.mbeanserver.JmxMBeanServer.registerMBean(JmxMBeanServer.java:523) ~[?:?]
	at org.apache.kafka.common.utils.AppInfoParser.registerAppInfo(AppInfoParser.java:64) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.consumer.KafkaConsumer.<init>(KafkaConsumer.java:821) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.consumer.KafkaConsumer.<init>(KafkaConsumer.java:665) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.consumer.KafkaConsumer.<init>(KafkaConsumer.java:646) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.consumer.KafkaConsumer.<init>(KafkaConsumer.java:626) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.flink.connector.kafka.source.reader.KafkaPartitionSplitReader.<init>(KafkaPartitionSplitReader.java:97) ~[flink-connector-kafka-3.4.0-1.20.jar:3.4.0-1.20]
	at org.apache.flink.connector.kafka.source.KafkaSource.lambda$createReader$1(KafkaSource.java:185) ~[flink-connector-kafka-3.4.0-1.20.jar:3.4.0-1.20]
	at org.apache.flink.connector.base.source.reader.fetcher.SplitFetcherManager.createSplitFetcher(SplitFetcherManager.java:259) ~[flink-connector-base-1.20.0.jar:1.20.0]
	at org.apache.flink.connector.base.source.reader.fetcher.SingleThreadFetcherManager.addSplits(SingleThreadFetcherManager.java:148) ~[flink-connector-base-1.20.0.jar:1.20.0]
	at org.apache.flink.connector.base.source.reader.SourceReaderBase.addSplits(SourceReaderBase.java:315) ~[flink-connector-base-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.api.operators.SourceOperator.handleAddSplitsEvent(SourceOperator.java:606) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.api.operators.SourceOperator.handleOperatorEvent(SourceOperator.java:575) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.OperatorEventDispatcherImpl.dispatchEventToHandlers(OperatorEventDispatcherImpl.java:72) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.RegularOperatorChain.dispatchOperatorEvent(RegularOperatorChain.java:80) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.StreamTask.lambda$dispatchOperatorEvent$24(StreamTask.java:1609) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.StreamTaskActionExecutor$1.runThrowing(StreamTaskActionExecutor.java:50) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.mailbox.Mail.run(Mail.java:101) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.mailbox.MailboxProcessor.runMail(MailboxProcessor.java:414) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.mailbox.MailboxProcessor.processMailsWhenDefaultActionUnavailable(MailboxProcessor.java:383) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.mailbox.MailboxProcessor.processMail(MailboxProcessor.java:368) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.mailbox.MailboxProcessor.runMailboxLoop(MailboxProcessor.java:229) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.StreamTask.runMailboxLoop(StreamTask.java:973) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.StreamTask.invoke(StreamTask.java:917) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.runtime.taskmanager.Task.runWithSystemExitMonitoring(Task.java:970) [flink-runtime-1.20.0.jar:1.20.0]
	at org.apache.flink.runtime.taskmanager.Task.restoreAndInvoke(Task.java:949) [flink-runtime-1.20.0.jar:1.20.0]
	at org.apache.flink.runtime.taskmanager.Task.doRun(Task.java:763) [flink-runtime-1.20.0.jar:1.20.0]
	at org.apache.flink.runtime.taskmanager.Task.run(Task.java:575) [flink-runtime-1.20.0.jar:1.20.0]
	at java.lang.Thread.run(Thread.java:840) [?:?]
2025-08-20 14:36:44.626 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] WARN  [c.f.f.PnLCalculationFunction:180] - 发现无持仓的平仓交易 - 发送到异常流: 交易号=101355625, 合约=JD2504-C-3200, 方向=S, 数量=5
2025-08-20 14:36:44.627 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] WARN  [c.f.f.PnLCalculationFunction:180] - 发现无持仓的平仓交易 - 发送到异常流: 交易号=101355626, 合约=JD2504-C-3200, 方向=S, 数量=5
2025-08-20 14:36:44.628 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] WARN  [c.f.f.PnLCalculationFunction:180] - 发现无持仓的平仓交易 - 发送到异常流: 交易号=101355700, 合约=EB2505, 方向=B, 数量=10
2025-08-20 14:36:44.629 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] WARN  [c.f.f.PnLCalculationFunction:180] - 发现无持仓的平仓交易 - 发送到异常流: 交易号=48060, 合约=SH2505, 方向=S, 数量=1
2025-08-20 14:36:44.629 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] WARN  [c.f.f.PnLCalculationFunction:180] - 发现无持仓的平仓交易 - 发送到异常流: 交易号=100052597, 合约=EB2505, 方向=B, 数量=8
2025-08-20 14:36:44.629 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] WARN  [c.f.f.PnLCalculationFunction:180] - 发现无持仓的平仓交易 - 发送到异常流: 交易号=100052597, 合约=EB2505, 方向=S, 数量=8
2025-08-20 14:36:44.629 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] WARN  [c.f.f.PnLCalculationFunction:180] - 发现无持仓的平仓交易 - 发送到异常流: 交易号=100052678, 合约=EB2505, 方向=B, 数量=1
2025-08-20 14:36:44.630 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] WARN  [c.f.f.PnLCalculationFunction:180] - 发现无持仓的平仓交易 - 发送到异常流: 交易号=100052678, 合约=EB2505, 方向=S, 数量=1
2025-08-20 14:36:44.630 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] WARN  [c.f.f.PnLCalculationFunction:180] - 发现无持仓的平仓交易 - 发送到异常流: 交易号=100052768, 合约=EB2505, 方向=B, 数量=1
2025-08-20 14:36:44.631 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] WARN  [c.f.f.PnLCalculationFunction:180] - 发现无持仓的平仓交易 - 发送到异常流: 交易号=100052770, 合约=EB2505, 方向=B, 数量=1
2025-08-20 14:36:44.631 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] WARN  [c.f.f.PnLCalculationFunction:180] - 发现无持仓的平仓交易 - 发送到异常流: 交易号=100052795, 合约=EB2505, 方向=B, 数量=1
2025-08-20 14:36:44.633 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] WARN  [c.f.f.PnLCalculationFunction:180] - 发现无持仓的平仓交易 - 发送到异常流: 交易号=100052821, 合约=EB2505, 方向=B, 数量=1
2025-08-20 14:36:44.633 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] WARN  [c.f.f.PnLCalculationFunction:180] - 发现无持仓的平仓交易 - 发送到异常流: 交易号=100052822, 合约=EB2505, 方向=B, 数量=2
2025-08-20 14:36:44.634 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] WARN  [c.f.f.PnLCalculationFunction:180] - 发现无持仓的平仓交易 - 发送到异常流: 交易号=100052833, 合约=EB2505, 方向=B, 数量=2
2025-08-20 14:36:44.634 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] WARN  [c.f.f.PnLCalculationFunction:180] - 发现无持仓的平仓交易 - 发送到异常流: 交易号=100052846, 合约=EB2504, 方向=B, 数量=1
2025-08-20 14:36:44.634 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] WARN  [c.f.f.PnLCalculationFunction:180] - 发现无持仓的平仓交易 - 发送到异常流: 交易号=100052847, 合约=EB2505, 方向=B, 数量=1
2025-08-20 14:36:44.635 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] WARN  [c.f.f.PnLCalculationFunction:180] - 发现无持仓的平仓交易 - 发送到异常流: 交易号=100052849, 合约=EB2504, 方向=S, 数量=10
2025-08-20 14:39:22.481 [main] WARN  [o.a.f.c.k.s.KafkaSourceBuilder:497] - Property auto.offset.reset is provided but will be overridden from earliest to earliest
2025-08-20 14:39:22.502 [main] WARN  [o.a.f.c.k.s.KafkaSourceBuilder:497] - Property auto.offset.reset is provided but will be overridden from earliest to earliest
2025-08-20 14:39:22.503 [main] WARN  [o.a.f.c.k.s.KafkaSourceBuilder:497] - Property auto.offset.reset is provided but will be overridden from earliest to earliest
2025-08-20 14:39:22.937 [main] WARN  [o.a.f.r.s.t.DefaultDelegationTokenManager:248] - No tokens obtained so skipping notifications
2025-08-20 14:39:23.037 [main] WARN  [o.a.f.r.w.WebMonitorUtils:76] - Log file environment variable 'log.file' is not set.
2025-08-20 14:39:23.037 [main] WARN  [o.a.f.r.w.WebMonitorUtils:82] - JobManager log files are unavailable in the web dashboard. Log file location not found in environment variable 'log.file' or configuration key 'web.log.path'.
2025-08-20 14:39:23.096 [flink-pekko.actor.default-dispatcher-4] WARN  [o.a.f.r.s.t.DefaultDelegationTokenManager:320] - No tokens obtained so skipping notifications
2025-08-20 14:39:23.096 [flink-pekko.actor.default-dispatcher-4] WARN  [o.a.f.r.s.t.DefaultDelegationTokenManager:335] - Tokens update task not started because either no tokens obtained or none of the tokens specified its renewal date
2025-08-20 14:39:23.247 [SourceCoordinator-Source: Kafka-交易] WARN  [o.a.k.c.a.AdminClientConfig:385] - These configurations '[key.deserializer, value.deserializer, max.poll.records, client.id.prefix, group.id, partition.discovery.interval.ms, heartbeat.interval.ms, enable.auto.commit, fetch.max.wait.ms, fetch.min.bytes, session.timeout.ms, auto.offset.reset]' were supplied but are not used yet.
2025-08-20 14:39:23.247 [SourceCoordinator-Source: Kafka-单腿订单] WARN  [o.a.k.c.a.AdminClientConfig:385] - These configurations '[key.deserializer, value.deserializer, max.poll.records, client.id.prefix, group.id, partition.discovery.interval.ms, heartbeat.interval.ms, enable.auto.commit, fetch.max.wait.ms, fetch.min.bytes, session.timeout.ms, auto.offset.reset]' were supplied but are not used yet.
2025-08-20 14:39:23.247 [SourceCoordinator-Source: Kafka-组合订单] WARN  [o.a.k.c.a.AdminClientConfig:385] - These configurations '[key.deserializer, value.deserializer, max.poll.records, client.id.prefix, group.id, partition.discovery.interval.ms, heartbeat.interval.ms, enable.auto.commit, fetch.max.wait.ms, fetch.min.bytes, session.timeout.ms, auto.offset.reset]' were supplied but are not used yet.
2025-08-20 14:39:23.250 [SourceCoordinator-Source: Kafka-组合订单] WARN  [o.a.k.c.u.AppInfoParser:68] - Error registering AppInfo mbean
javax.management.InstanceAlreadyExistsException: kafka.admin.client:type=app-info,id=orderbook-local-test-group-enumerator-admin-client
	at com.sun.jmx.mbeanserver.Repository.addMBean(Repository.java:436) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerWithRepository(DefaultMBeanServerInterceptor.java:1865) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerDynamicMBean(DefaultMBeanServerInterceptor.java:960) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerObject(DefaultMBeanServerInterceptor.java:895) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerMBean(DefaultMBeanServerInterceptor.java:320) ~[?:?]
	at com.sun.jmx.mbeanserver.JmxMBeanServer.registerMBean(JmxMBeanServer.java:523) ~[?:?]
	at org.apache.kafka.common.utils.AppInfoParser.registerAppInfo(AppInfoParser.java:64) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.admin.KafkaAdminClient.<init>(KafkaAdminClient.java:602) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.admin.KafkaAdminClient.createInternal(KafkaAdminClient.java:544) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.admin.KafkaAdminClient.createInternal(KafkaAdminClient.java:488) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.admin.Admin.create(Admin.java:134) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.admin.AdminClient.create(AdminClient.java:39) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.flink.connector.kafka.source.enumerator.KafkaSourceEnumerator.getKafkaAdminClient(KafkaSourceEnumerator.java:441) ~[flink-connector-kafka-3.4.0-1.20.jar:3.4.0-1.20]
	at org.apache.flink.connector.kafka.source.enumerator.KafkaSourceEnumerator.start(KafkaSourceEnumerator.java:164) ~[flink-connector-kafka-3.4.0-1.20.jar:3.4.0-1.20]
	at org.apache.flink.runtime.source.coordinator.SourceCoordinator.lambda$start$1(SourceCoordinator.java:244) ~[flink-runtime-1.20.0.jar:1.20.0]
	at org.apache.flink.runtime.source.coordinator.SourceCoordinator.lambda$runInEventLoop$10(SourceCoordinator.java:533) ~[flink-runtime-1.20.0.jar:1.20.0]
	at org.apache.flink.util.ThrowableCatchingRunnable.run(ThrowableCatchingRunnable.java:40) [flink-core-1.20.0.jar:1.20.0]
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:539) [?:?]
	at java.util.concurrent.FutureTask.run(FutureTask.java:264) [?:?]
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:304) [?:?]
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136) [?:?]
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635) [?:?]
	at java.lang.Thread.run(Thread.java:840) [?:?]
2025-08-20 14:39:23.256 [SourceCoordinator-Source: Kafka-交易] WARN  [o.a.k.c.u.AppInfoParser:68] - Error registering AppInfo mbean
javax.management.InstanceAlreadyExistsException: kafka.admin.client:type=app-info,id=orderbook-local-test-group-enumerator-admin-client
	at com.sun.jmx.mbeanserver.Repository.addMBean(Repository.java:436) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerWithRepository(DefaultMBeanServerInterceptor.java:1865) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerDynamicMBean(DefaultMBeanServerInterceptor.java:960) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerObject(DefaultMBeanServerInterceptor.java:895) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerMBean(DefaultMBeanServerInterceptor.java:320) ~[?:?]
	at com.sun.jmx.mbeanserver.JmxMBeanServer.registerMBean(JmxMBeanServer.java:523) ~[?:?]
	at org.apache.kafka.common.utils.AppInfoParser.registerAppInfo(AppInfoParser.java:64) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.admin.KafkaAdminClient.<init>(KafkaAdminClient.java:602) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.admin.KafkaAdminClient.createInternal(KafkaAdminClient.java:544) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.admin.KafkaAdminClient.createInternal(KafkaAdminClient.java:488) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.admin.Admin.create(Admin.java:134) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.admin.AdminClient.create(AdminClient.java:39) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.flink.connector.kafka.source.enumerator.KafkaSourceEnumerator.getKafkaAdminClient(KafkaSourceEnumerator.java:441) ~[flink-connector-kafka-3.4.0-1.20.jar:3.4.0-1.20]
	at org.apache.flink.connector.kafka.source.enumerator.KafkaSourceEnumerator.start(KafkaSourceEnumerator.java:164) ~[flink-connector-kafka-3.4.0-1.20.jar:3.4.0-1.20]
	at org.apache.flink.runtime.source.coordinator.SourceCoordinator.lambda$start$1(SourceCoordinator.java:244) ~[flink-runtime-1.20.0.jar:1.20.0]
	at org.apache.flink.runtime.source.coordinator.SourceCoordinator.lambda$runInEventLoop$10(SourceCoordinator.java:533) ~[flink-runtime-1.20.0.jar:1.20.0]
	at org.apache.flink.util.ThrowableCatchingRunnable.run(ThrowableCatchingRunnable.java:40) [flink-core-1.20.0.jar:1.20.0]
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:539) [?:?]
	at java.util.concurrent.FutureTask.run(FutureTask.java:264) [?:?]
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:304) [?:?]
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136) [?:?]
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635) [?:?]
	at java.lang.Thread.run(Thread.java:840) [?:?]
2025-08-20 14:39:23.569 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] WARN  [o.a.k.c.c.ConsumerConfig:385] - These configurations '[client.id.prefix, partition.discovery.interval.ms]' were supplied but are not used yet.
2025-08-20 14:39:23.570 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] WARN  [o.a.k.c.c.ConsumerConfig:385] - These configurations '[client.id.prefix, partition.discovery.interval.ms]' were supplied but are not used yet.
2025-08-20 14:39:23.570 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] WARN  [o.a.k.c.c.ConsumerConfig:385] - These configurations '[client.id.prefix, partition.discovery.interval.ms]' were supplied but are not used yet.
2025-08-20 14:39:23.570 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] WARN  [o.a.k.c.u.AppInfoParser:68] - Error registering AppInfo mbean
javax.management.InstanceAlreadyExistsException: kafka.consumer:type=app-info,id=orderbook-local-test-group-0
	at com.sun.jmx.mbeanserver.Repository.addMBean(Repository.java:436) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerWithRepository(DefaultMBeanServerInterceptor.java:1865) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerDynamicMBean(DefaultMBeanServerInterceptor.java:960) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerObject(DefaultMBeanServerInterceptor.java:895) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerMBean(DefaultMBeanServerInterceptor.java:320) ~[?:?]
	at com.sun.jmx.mbeanserver.JmxMBeanServer.registerMBean(JmxMBeanServer.java:523) ~[?:?]
	at org.apache.kafka.common.utils.AppInfoParser.registerAppInfo(AppInfoParser.java:64) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.consumer.KafkaConsumer.<init>(KafkaConsumer.java:821) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.consumer.KafkaConsumer.<init>(KafkaConsumer.java:665) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.consumer.KafkaConsumer.<init>(KafkaConsumer.java:646) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.consumer.KafkaConsumer.<init>(KafkaConsumer.java:626) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.flink.connector.kafka.source.reader.KafkaPartitionSplitReader.<init>(KafkaPartitionSplitReader.java:97) ~[flink-connector-kafka-3.4.0-1.20.jar:3.4.0-1.20]
	at org.apache.flink.connector.kafka.source.KafkaSource.lambda$createReader$1(KafkaSource.java:185) ~[flink-connector-kafka-3.4.0-1.20.jar:3.4.0-1.20]
	at org.apache.flink.connector.base.source.reader.fetcher.SplitFetcherManager.createSplitFetcher(SplitFetcherManager.java:259) ~[flink-connector-base-1.20.0.jar:1.20.0]
	at org.apache.flink.connector.base.source.reader.fetcher.SingleThreadFetcherManager.addSplits(SingleThreadFetcherManager.java:148) ~[flink-connector-base-1.20.0.jar:1.20.0]
	at org.apache.flink.connector.base.source.reader.SourceReaderBase.addSplits(SourceReaderBase.java:315) ~[flink-connector-base-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.api.operators.SourceOperator.handleAddSplitsEvent(SourceOperator.java:606) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.api.operators.SourceOperator.handleOperatorEvent(SourceOperator.java:575) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.OperatorEventDispatcherImpl.dispatchEventToHandlers(OperatorEventDispatcherImpl.java:72) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.RegularOperatorChain.dispatchOperatorEvent(RegularOperatorChain.java:80) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.StreamTask.lambda$dispatchOperatorEvent$24(StreamTask.java:1609) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.StreamTaskActionExecutor$1.runThrowing(StreamTaskActionExecutor.java:50) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.mailbox.Mail.run(Mail.java:101) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.mailbox.MailboxProcessor.runMail(MailboxProcessor.java:414) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.mailbox.MailboxProcessor.processMailsWhenDefaultActionUnavailable(MailboxProcessor.java:383) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.mailbox.MailboxProcessor.processMail(MailboxProcessor.java:368) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.mailbox.MailboxProcessor.runMailboxLoop(MailboxProcessor.java:229) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.StreamTask.runMailboxLoop(StreamTask.java:973) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.StreamTask.invoke(StreamTask.java:917) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.runtime.taskmanager.Task.runWithSystemExitMonitoring(Task.java:970) [flink-runtime-1.20.0.jar:1.20.0]
	at org.apache.flink.runtime.taskmanager.Task.restoreAndInvoke(Task.java:949) [flink-runtime-1.20.0.jar:1.20.0]
	at org.apache.flink.runtime.taskmanager.Task.doRun(Task.java:763) [flink-runtime-1.20.0.jar:1.20.0]
	at org.apache.flink.runtime.taskmanager.Task.run(Task.java:575) [flink-runtime-1.20.0.jar:1.20.0]
	at java.lang.Thread.run(Thread.java:840) [?:?]
2025-08-20 14:39:23.570 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] WARN  [o.a.k.c.u.AppInfoParser:68] - Error registering AppInfo mbean
javax.management.InstanceAlreadyExistsException: kafka.consumer:type=app-info,id=orderbook-local-test-group-0
	at com.sun.jmx.mbeanserver.Repository.addMBean(Repository.java:436) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerWithRepository(DefaultMBeanServerInterceptor.java:1865) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerDynamicMBean(DefaultMBeanServerInterceptor.java:960) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerObject(DefaultMBeanServerInterceptor.java:895) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerMBean(DefaultMBeanServerInterceptor.java:320) ~[?:?]
	at com.sun.jmx.mbeanserver.JmxMBeanServer.registerMBean(JmxMBeanServer.java:523) ~[?:?]
	at org.apache.kafka.common.utils.AppInfoParser.registerAppInfo(AppInfoParser.java:64) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.consumer.KafkaConsumer.<init>(KafkaConsumer.java:821) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.consumer.KafkaConsumer.<init>(KafkaConsumer.java:665) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.consumer.KafkaConsumer.<init>(KafkaConsumer.java:646) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.consumer.KafkaConsumer.<init>(KafkaConsumer.java:626) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.flink.connector.kafka.source.reader.KafkaPartitionSplitReader.<init>(KafkaPartitionSplitReader.java:97) ~[flink-connector-kafka-3.4.0-1.20.jar:3.4.0-1.20]
	at org.apache.flink.connector.kafka.source.KafkaSource.lambda$createReader$1(KafkaSource.java:185) ~[flink-connector-kafka-3.4.0-1.20.jar:3.4.0-1.20]
	at org.apache.flink.connector.base.source.reader.fetcher.SplitFetcherManager.createSplitFetcher(SplitFetcherManager.java:259) ~[flink-connector-base-1.20.0.jar:1.20.0]
	at org.apache.flink.connector.base.source.reader.fetcher.SingleThreadFetcherManager.addSplits(SingleThreadFetcherManager.java:148) ~[flink-connector-base-1.20.0.jar:1.20.0]
	at org.apache.flink.connector.base.source.reader.SourceReaderBase.addSplits(SourceReaderBase.java:315) ~[flink-connector-base-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.api.operators.SourceOperator.handleAddSplitsEvent(SourceOperator.java:606) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.api.operators.SourceOperator.handleOperatorEvent(SourceOperator.java:575) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.OperatorEventDispatcherImpl.dispatchEventToHandlers(OperatorEventDispatcherImpl.java:72) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.RegularOperatorChain.dispatchOperatorEvent(RegularOperatorChain.java:80) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.StreamTask.lambda$dispatchOperatorEvent$24(StreamTask.java:1609) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.StreamTaskActionExecutor$1.runThrowing(StreamTaskActionExecutor.java:50) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.mailbox.Mail.run(Mail.java:101) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.mailbox.MailboxProcessor.runMail(MailboxProcessor.java:414) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.mailbox.MailboxProcessor.processMailsWhenDefaultActionUnavailable(MailboxProcessor.java:383) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.mailbox.MailboxProcessor.processMail(MailboxProcessor.java:368) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.mailbox.MailboxProcessor.runMailboxLoop(MailboxProcessor.java:229) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.StreamTask.runMailboxLoop(StreamTask.java:973) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.StreamTask.invoke(StreamTask.java:917) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.runtime.taskmanager.Task.runWithSystemExitMonitoring(Task.java:970) [flink-runtime-1.20.0.jar:1.20.0]
	at org.apache.flink.runtime.taskmanager.Task.restoreAndInvoke(Task.java:949) [flink-runtime-1.20.0.jar:1.20.0]
	at org.apache.flink.runtime.taskmanager.Task.doRun(Task.java:763) [flink-runtime-1.20.0.jar:1.20.0]
	at org.apache.flink.runtime.taskmanager.Task.run(Task.java:575) [flink-runtime-1.20.0.jar:1.20.0]
	at java.lang.Thread.run(Thread.java:840) [?:?]
2025-08-20 14:39:23.675 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] WARN  [c.f.f.PnLCalculationFunction:180] - 发现无持仓的平仓交易 - 发送到异常流: 交易号=101355625, 合约=JD2504-C-3200, 方向=S, 数量=5
2025-08-20 14:39:23.676 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] WARN  [c.f.f.PnLCalculationFunction:180] - 发现无持仓的平仓交易 - 发送到异常流: 交易号=101355626, 合约=JD2504-C-3200, 方向=S, 数量=5
2025-08-20 14:39:23.677 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] WARN  [c.f.f.PnLCalculationFunction:180] - 发现无持仓的平仓交易 - 发送到异常流: 交易号=101355700, 合约=EB2505, 方向=B, 数量=10
2025-08-20 14:39:23.678 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] WARN  [c.f.f.PnLCalculationFunction:180] - 发现无持仓的平仓交易 - 发送到异常流: 交易号=48060, 合约=SH2505, 方向=S, 数量=1
2025-08-20 14:39:23.678 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] WARN  [c.f.f.PnLCalculationFunction:180] - 发现无持仓的平仓交易 - 发送到异常流: 交易号=100052597, 合约=EB2505, 方向=B, 数量=8
2025-08-20 14:39:23.678 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] WARN  [c.f.f.PnLCalculationFunction:180] - 发现无持仓的平仓交易 - 发送到异常流: 交易号=100052597, 合约=EB2505, 方向=S, 数量=8
2025-08-20 14:39:23.678 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] WARN  [c.f.f.PnLCalculationFunction:180] - 发现无持仓的平仓交易 - 发送到异常流: 交易号=100052678, 合约=EB2505, 方向=B, 数量=1
2025-08-20 14:39:23.679 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] WARN  [c.f.f.PnLCalculationFunction:180] - 发现无持仓的平仓交易 - 发送到异常流: 交易号=100052678, 合约=EB2505, 方向=S, 数量=1
2025-08-20 14:39:23.679 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] WARN  [c.f.f.PnLCalculationFunction:180] - 发现无持仓的平仓交易 - 发送到异常流: 交易号=100052768, 合约=EB2505, 方向=B, 数量=1
2025-08-20 14:39:23.681 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] WARN  [c.f.f.PnLCalculationFunction:180] - 发现无持仓的平仓交易 - 发送到异常流: 交易号=100052770, 合约=EB2505, 方向=B, 数量=1
2025-08-20 14:39:23.682 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] WARN  [c.f.f.PnLCalculationFunction:180] - 发现无持仓的平仓交易 - 发送到异常流: 交易号=100052795, 合约=EB2505, 方向=B, 数量=1
2025-08-20 14:39:23.684 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] WARN  [c.f.f.PnLCalculationFunction:180] - 发现无持仓的平仓交易 - 发送到异常流: 交易号=100052821, 合约=EB2505, 方向=B, 数量=1
2025-08-20 14:39:23.685 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] WARN  [c.f.f.PnLCalculationFunction:180] - 发现无持仓的平仓交易 - 发送到异常流: 交易号=100052822, 合约=EB2505, 方向=B, 数量=2
2025-08-20 14:39:23.686 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] WARN  [c.f.f.PnLCalculationFunction:180] - 发现无持仓的平仓交易 - 发送到异常流: 交易号=100052833, 合约=EB2505, 方向=B, 数量=2
2025-08-20 14:39:23.687 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] WARN  [c.f.f.PnLCalculationFunction:180] - 发现无持仓的平仓交易 - 发送到异常流: 交易号=100052846, 合约=EB2504, 方向=B, 数量=1
2025-08-20 14:39:23.687 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] WARN  [c.f.f.PnLCalculationFunction:180] - 发现无持仓的平仓交易 - 发送到异常流: 交易号=100052847, 合约=EB2505, 方向=B, 数量=1
2025-08-20 14:39:23.688 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] WARN  [c.f.f.PnLCalculationFunction:180] - 发现无持仓的平仓交易 - 发送到异常流: 交易号=100052849, 合约=EB2504, 方向=S, 数量=10
2025-08-20 14:46:26.623 [main] WARN  [o.a.f.c.k.s.KafkaSourceBuilder:497] - Property auto.offset.reset is provided but will be overridden from earliest to earliest
2025-08-20 14:46:26.642 [main] WARN  [o.a.f.c.k.s.KafkaSourceBuilder:497] - Property auto.offset.reset is provided but will be overridden from earliest to earliest
2025-08-20 14:46:26.643 [main] WARN  [o.a.f.c.k.s.KafkaSourceBuilder:497] - Property auto.offset.reset is provided but will be overridden from earliest to earliest
2025-08-20 14:46:27.137 [main] WARN  [o.a.f.r.s.t.DefaultDelegationTokenManager:248] - No tokens obtained so skipping notifications
2025-08-20 14:46:27.245 [main] WARN  [o.a.f.r.w.WebMonitorUtils:76] - Log file environment variable 'log.file' is not set.
2025-08-20 14:46:27.245 [main] WARN  [o.a.f.r.w.WebMonitorUtils:82] - JobManager log files are unavailable in the web dashboard. Log file location not found in environment variable 'log.file' or configuration key 'web.log.path'.
2025-08-20 14:46:27.305 [flink-pekko.actor.default-dispatcher-4] WARN  [o.a.f.r.s.t.DefaultDelegationTokenManager:320] - No tokens obtained so skipping notifications
2025-08-20 14:46:27.305 [flink-pekko.actor.default-dispatcher-4] WARN  [o.a.f.r.s.t.DefaultDelegationTokenManager:335] - Tokens update task not started because either no tokens obtained or none of the tokens specified its renewal date
2025-08-20 14:46:27.410 [SourceCoordinator-Source: Kafka-组合订单] WARN  [o.a.k.c.a.AdminClientConfig:385] - These configurations '[key.deserializer, value.deserializer, max.poll.records, client.id.prefix, group.id, partition.discovery.interval.ms, heartbeat.interval.ms, enable.auto.commit, fetch.max.wait.ms, fetch.min.bytes, session.timeout.ms, auto.offset.reset]' were supplied but are not used yet.
2025-08-20 14:46:27.410 [SourceCoordinator-Source: Kafka-交易] WARN  [o.a.k.c.a.AdminClientConfig:385] - These configurations '[key.deserializer, value.deserializer, max.poll.records, client.id.prefix, group.id, partition.discovery.interval.ms, heartbeat.interval.ms, enable.auto.commit, fetch.max.wait.ms, fetch.min.bytes, session.timeout.ms, auto.offset.reset]' were supplied but are not used yet.
2025-08-20 14:46:27.410 [SourceCoordinator-Source: Kafka-单腿订单] WARN  [o.a.k.c.a.AdminClientConfig:385] - These configurations '[key.deserializer, value.deserializer, max.poll.records, client.id.prefix, group.id, partition.discovery.interval.ms, heartbeat.interval.ms, enable.auto.commit, fetch.max.wait.ms, fetch.min.bytes, session.timeout.ms, auto.offset.reset]' were supplied but are not used yet.
2025-08-20 14:46:27.411 [SourceCoordinator-Source: Kafka-单腿订单] WARN  [o.a.k.c.u.AppInfoParser:68] - Error registering AppInfo mbean
javax.management.InstanceAlreadyExistsException: kafka.admin.client:type=app-info,id=orderbook-local-test-group-enumerator-admin-client
	at com.sun.jmx.mbeanserver.Repository.addMBean(Repository.java:436) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerWithRepository(DefaultMBeanServerInterceptor.java:1865) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerDynamicMBean(DefaultMBeanServerInterceptor.java:960) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerObject(DefaultMBeanServerInterceptor.java:895) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerMBean(DefaultMBeanServerInterceptor.java:320) ~[?:?]
	at com.sun.jmx.mbeanserver.JmxMBeanServer.registerMBean(JmxMBeanServer.java:523) ~[?:?]
	at org.apache.kafka.common.utils.AppInfoParser.registerAppInfo(AppInfoParser.java:64) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.admin.KafkaAdminClient.<init>(KafkaAdminClient.java:602) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.admin.KafkaAdminClient.createInternal(KafkaAdminClient.java:544) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.admin.KafkaAdminClient.createInternal(KafkaAdminClient.java:488) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.admin.Admin.create(Admin.java:134) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.admin.AdminClient.create(AdminClient.java:39) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.flink.connector.kafka.source.enumerator.KafkaSourceEnumerator.getKafkaAdminClient(KafkaSourceEnumerator.java:441) ~[flink-connector-kafka-3.4.0-1.20.jar:3.4.0-1.20]
	at org.apache.flink.connector.kafka.source.enumerator.KafkaSourceEnumerator.start(KafkaSourceEnumerator.java:164) ~[flink-connector-kafka-3.4.0-1.20.jar:3.4.0-1.20]
	at org.apache.flink.runtime.source.coordinator.SourceCoordinator.lambda$start$1(SourceCoordinator.java:244) ~[flink-runtime-1.20.0.jar:1.20.0]
	at org.apache.flink.runtime.source.coordinator.SourceCoordinator.lambda$runInEventLoop$10(SourceCoordinator.java:533) ~[flink-runtime-1.20.0.jar:1.20.0]
	at org.apache.flink.util.ThrowableCatchingRunnable.run(ThrowableCatchingRunnable.java:40) [flink-core-1.20.0.jar:1.20.0]
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:539) [?:?]
	at java.util.concurrent.FutureTask.run(FutureTask.java:264) [?:?]
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:304) [?:?]
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136) [?:?]
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635) [?:?]
	at java.lang.Thread.run(Thread.java:840) [?:?]
2025-08-20 14:46:27.414 [SourceCoordinator-Source: Kafka-交易] WARN  [o.a.k.c.u.AppInfoParser:68] - Error registering AppInfo mbean
javax.management.InstanceAlreadyExistsException: kafka.admin.client:type=app-info,id=orderbook-local-test-group-enumerator-admin-client
	at com.sun.jmx.mbeanserver.Repository.addMBean(Repository.java:436) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerWithRepository(DefaultMBeanServerInterceptor.java:1865) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerDynamicMBean(DefaultMBeanServerInterceptor.java:960) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerObject(DefaultMBeanServerInterceptor.java:895) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerMBean(DefaultMBeanServerInterceptor.java:320) ~[?:?]
	at com.sun.jmx.mbeanserver.JmxMBeanServer.registerMBean(JmxMBeanServer.java:523) ~[?:?]
	at org.apache.kafka.common.utils.AppInfoParser.registerAppInfo(AppInfoParser.java:64) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.admin.KafkaAdminClient.<init>(KafkaAdminClient.java:602) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.admin.KafkaAdminClient.createInternal(KafkaAdminClient.java:544) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.admin.KafkaAdminClient.createInternal(KafkaAdminClient.java:488) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.admin.Admin.create(Admin.java:134) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.admin.AdminClient.create(AdminClient.java:39) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.flink.connector.kafka.source.enumerator.KafkaSourceEnumerator.getKafkaAdminClient(KafkaSourceEnumerator.java:441) ~[flink-connector-kafka-3.4.0-1.20.jar:3.4.0-1.20]
	at org.apache.flink.connector.kafka.source.enumerator.KafkaSourceEnumerator.start(KafkaSourceEnumerator.java:164) ~[flink-connector-kafka-3.4.0-1.20.jar:3.4.0-1.20]
	at org.apache.flink.runtime.source.coordinator.SourceCoordinator.lambda$start$1(SourceCoordinator.java:244) ~[flink-runtime-1.20.0.jar:1.20.0]
	at org.apache.flink.runtime.source.coordinator.SourceCoordinator.lambda$runInEventLoop$10(SourceCoordinator.java:533) ~[flink-runtime-1.20.0.jar:1.20.0]
	at org.apache.flink.util.ThrowableCatchingRunnable.run(ThrowableCatchingRunnable.java:40) [flink-core-1.20.0.jar:1.20.0]
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:539) [?:?]
	at java.util.concurrent.FutureTask.run(FutureTask.java:264) [?:?]
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:304) [?:?]
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136) [?:?]
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635) [?:?]
	at java.lang.Thread.run(Thread.java:840) [?:?]
2025-08-20 14:46:27.563 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] WARN  [o.a.k.c.c.ConsumerConfig:385] - These configurations '[client.id.prefix, partition.discovery.interval.ms]' were supplied but are not used yet.
2025-08-20 14:46:27.563 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] WARN  [o.a.k.c.c.ConsumerConfig:385] - These configurations '[client.id.prefix, partition.discovery.interval.ms]' were supplied but are not used yet.
2025-08-20 14:46:27.563 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] WARN  [o.a.k.c.c.ConsumerConfig:385] - These configurations '[client.id.prefix, partition.discovery.interval.ms]' were supplied but are not used yet.
2025-08-20 14:46:27.563 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] WARN  [o.a.k.c.u.AppInfoParser:68] - Error registering AppInfo mbean
javax.management.InstanceAlreadyExistsException: kafka.consumer:type=app-info,id=orderbook-local-test-group-0
	at com.sun.jmx.mbeanserver.Repository.addMBean(Repository.java:436) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerWithRepository(DefaultMBeanServerInterceptor.java:1865) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerDynamicMBean(DefaultMBeanServerInterceptor.java:960) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerObject(DefaultMBeanServerInterceptor.java:895) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerMBean(DefaultMBeanServerInterceptor.java:320) ~[?:?]
	at com.sun.jmx.mbeanserver.JmxMBeanServer.registerMBean(JmxMBeanServer.java:523) ~[?:?]
	at org.apache.kafka.common.utils.AppInfoParser.registerAppInfo(AppInfoParser.java:64) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.consumer.KafkaConsumer.<init>(KafkaConsumer.java:821) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.consumer.KafkaConsumer.<init>(KafkaConsumer.java:665) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.consumer.KafkaConsumer.<init>(KafkaConsumer.java:646) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.consumer.KafkaConsumer.<init>(KafkaConsumer.java:626) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.flink.connector.kafka.source.reader.KafkaPartitionSplitReader.<init>(KafkaPartitionSplitReader.java:97) ~[flink-connector-kafka-3.4.0-1.20.jar:3.4.0-1.20]
	at org.apache.flink.connector.kafka.source.KafkaSource.lambda$createReader$1(KafkaSource.java:185) ~[flink-connector-kafka-3.4.0-1.20.jar:3.4.0-1.20]
	at org.apache.flink.connector.base.source.reader.fetcher.SplitFetcherManager.createSplitFetcher(SplitFetcherManager.java:259) ~[flink-connector-base-1.20.0.jar:1.20.0]
	at org.apache.flink.connector.base.source.reader.fetcher.SingleThreadFetcherManager.addSplits(SingleThreadFetcherManager.java:148) ~[flink-connector-base-1.20.0.jar:1.20.0]
	at org.apache.flink.connector.base.source.reader.SourceReaderBase.addSplits(SourceReaderBase.java:315) ~[flink-connector-base-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.api.operators.SourceOperator.handleAddSplitsEvent(SourceOperator.java:606) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.api.operators.SourceOperator.handleOperatorEvent(SourceOperator.java:575) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.OperatorEventDispatcherImpl.dispatchEventToHandlers(OperatorEventDispatcherImpl.java:72) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.RegularOperatorChain.dispatchOperatorEvent(RegularOperatorChain.java:80) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.StreamTask.lambda$dispatchOperatorEvent$24(StreamTask.java:1609) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.StreamTaskActionExecutor$1.runThrowing(StreamTaskActionExecutor.java:50) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.mailbox.Mail.run(Mail.java:101) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.mailbox.MailboxProcessor.runMail(MailboxProcessor.java:414) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.mailbox.MailboxProcessor.processMailsWhenDefaultActionUnavailable(MailboxProcessor.java:383) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.mailbox.MailboxProcessor.processMail(MailboxProcessor.java:368) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.mailbox.MailboxProcessor.runMailboxLoop(MailboxProcessor.java:229) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.StreamTask.runMailboxLoop(StreamTask.java:973) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.StreamTask.invoke(StreamTask.java:917) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.runtime.taskmanager.Task.runWithSystemExitMonitoring(Task.java:970) [flink-runtime-1.20.0.jar:1.20.0]
	at org.apache.flink.runtime.taskmanager.Task.restoreAndInvoke(Task.java:949) [flink-runtime-1.20.0.jar:1.20.0]
	at org.apache.flink.runtime.taskmanager.Task.doRun(Task.java:763) [flink-runtime-1.20.0.jar:1.20.0]
	at org.apache.flink.runtime.taskmanager.Task.run(Task.java:575) [flink-runtime-1.20.0.jar:1.20.0]
	at java.lang.Thread.run(Thread.java:840) [?:?]
2025-08-20 14:46:27.564 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] WARN  [o.a.k.c.u.AppInfoParser:68] - Error registering AppInfo mbean
javax.management.InstanceAlreadyExistsException: kafka.consumer:type=app-info,id=orderbook-local-test-group-0
	at com.sun.jmx.mbeanserver.Repository.addMBean(Repository.java:436) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerWithRepository(DefaultMBeanServerInterceptor.java:1865) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerDynamicMBean(DefaultMBeanServerInterceptor.java:960) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerObject(DefaultMBeanServerInterceptor.java:895) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerMBean(DefaultMBeanServerInterceptor.java:320) ~[?:?]
	at com.sun.jmx.mbeanserver.JmxMBeanServer.registerMBean(JmxMBeanServer.java:523) ~[?:?]
	at org.apache.kafka.common.utils.AppInfoParser.registerAppInfo(AppInfoParser.java:64) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.consumer.KafkaConsumer.<init>(KafkaConsumer.java:821) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.consumer.KafkaConsumer.<init>(KafkaConsumer.java:665) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.consumer.KafkaConsumer.<init>(KafkaConsumer.java:646) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.consumer.KafkaConsumer.<init>(KafkaConsumer.java:626) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.flink.connector.kafka.source.reader.KafkaPartitionSplitReader.<init>(KafkaPartitionSplitReader.java:97) ~[flink-connector-kafka-3.4.0-1.20.jar:3.4.0-1.20]
	at org.apache.flink.connector.kafka.source.KafkaSource.lambda$createReader$1(KafkaSource.java:185) ~[flink-connector-kafka-3.4.0-1.20.jar:3.4.0-1.20]
	at org.apache.flink.connector.base.source.reader.fetcher.SplitFetcherManager.createSplitFetcher(SplitFetcherManager.java:259) ~[flink-connector-base-1.20.0.jar:1.20.0]
	at org.apache.flink.connector.base.source.reader.fetcher.SingleThreadFetcherManager.addSplits(SingleThreadFetcherManager.java:148) ~[flink-connector-base-1.20.0.jar:1.20.0]
	at org.apache.flink.connector.base.source.reader.SourceReaderBase.addSplits(SourceReaderBase.java:315) ~[flink-connector-base-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.api.operators.SourceOperator.handleAddSplitsEvent(SourceOperator.java:606) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.api.operators.SourceOperator.handleOperatorEvent(SourceOperator.java:575) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.OperatorEventDispatcherImpl.dispatchEventToHandlers(OperatorEventDispatcherImpl.java:72) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.RegularOperatorChain.dispatchOperatorEvent(RegularOperatorChain.java:80) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.StreamTask.lambda$dispatchOperatorEvent$24(StreamTask.java:1609) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.StreamTaskActionExecutor$1.runThrowing(StreamTaskActionExecutor.java:50) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.mailbox.Mail.run(Mail.java:101) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.mailbox.MailboxProcessor.runMail(MailboxProcessor.java:414) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.mailbox.MailboxProcessor.processMailsWhenDefaultActionUnavailable(MailboxProcessor.java:383) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.mailbox.MailboxProcessor.processMail(MailboxProcessor.java:368) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.mailbox.MailboxProcessor.runMailboxLoop(MailboxProcessor.java:229) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.StreamTask.runMailboxLoop(StreamTask.java:973) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.StreamTask.invoke(StreamTask.java:917) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.runtime.taskmanager.Task.runWithSystemExitMonitoring(Task.java:970) [flink-runtime-1.20.0.jar:1.20.0]
	at org.apache.flink.runtime.taskmanager.Task.restoreAndInvoke(Task.java:949) [flink-runtime-1.20.0.jar:1.20.0]
	at org.apache.flink.runtime.taskmanager.Task.doRun(Task.java:763) [flink-runtime-1.20.0.jar:1.20.0]
	at org.apache.flink.runtime.taskmanager.Task.run(Task.java:575) [flink-runtime-1.20.0.jar:1.20.0]
	at java.lang.Thread.run(Thread.java:840) [?:?]
2025-08-20 14:46:27.701 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] WARN  [c.f.f.PnLCalculationFunction:180] - 发现无持仓的平仓交易 - 发送到异常流: 交易号=101355625, 合约=JD2504-C-3200, 方向=S, 数量=5
2025-08-20 14:46:27.702 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] WARN  [c.f.f.PnLCalculationFunction:180] - 发现无持仓的平仓交易 - 发送到异常流: 交易号=101355626, 合约=JD2504-C-3200, 方向=S, 数量=5
2025-08-20 14:46:27.703 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] WARN  [c.f.f.PnLCalculationFunction:180] - 发现无持仓的平仓交易 - 发送到异常流: 交易号=101355700, 合约=EB2505, 方向=B, 数量=10
2025-08-20 14:46:27.703 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] WARN  [c.f.f.PnLCalculationFunction:180] - 发现无持仓的平仓交易 - 发送到异常流: 交易号=48060, 合约=SH2505, 方向=S, 数量=1
2025-08-20 14:46:27.703 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] WARN  [c.f.f.PnLCalculationFunction:180] - 发现无持仓的平仓交易 - 发送到异常流: 交易号=100052597, 合约=EB2505, 方向=B, 数量=8
2025-08-20 14:46:27.703 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] WARN  [c.f.f.PnLCalculationFunction:180] - 发现无持仓的平仓交易 - 发送到异常流: 交易号=100052597, 合约=EB2505, 方向=S, 数量=8
2025-08-20 14:46:27.703 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] WARN  [c.f.f.PnLCalculationFunction:180] - 发现无持仓的平仓交易 - 发送到异常流: 交易号=100052678, 合约=EB2505, 方向=B, 数量=1
2025-08-20 14:46:27.704 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] WARN  [c.f.f.PnLCalculationFunction:180] - 发现无持仓的平仓交易 - 发送到异常流: 交易号=100052678, 合约=EB2505, 方向=S, 数量=1
2025-08-20 14:46:27.704 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] WARN  [c.f.f.PnLCalculationFunction:180] - 发现无持仓的平仓交易 - 发送到异常流: 交易号=100052768, 合约=EB2505, 方向=B, 数量=1
2025-08-20 14:46:27.705 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] WARN  [c.f.f.PnLCalculationFunction:180] - 发现无持仓的平仓交易 - 发送到异常流: 交易号=100052770, 合约=EB2505, 方向=B, 数量=1
2025-08-20 14:46:27.706 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] WARN  [c.f.f.PnLCalculationFunction:180] - 发现无持仓的平仓交易 - 发送到异常流: 交易号=100052795, 合约=EB2505, 方向=B, 数量=1
2025-08-20 14:46:27.707 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] WARN  [c.f.f.PnLCalculationFunction:180] - 发现无持仓的平仓交易 - 发送到异常流: 交易号=100052821, 合约=EB2505, 方向=B, 数量=1
2025-08-20 14:46:27.708 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] WARN  [c.f.f.PnLCalculationFunction:180] - 发现无持仓的平仓交易 - 发送到异常流: 交易号=100052822, 合约=EB2505, 方向=B, 数量=2
2025-08-20 14:46:27.708 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] WARN  [c.f.f.PnLCalculationFunction:180] - 发现无持仓的平仓交易 - 发送到异常流: 交易号=100052833, 合约=EB2505, 方向=B, 数量=2
2025-08-20 14:46:27.709 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] WARN  [c.f.f.PnLCalculationFunction:180] - 发现无持仓的平仓交易 - 发送到异常流: 交易号=100052846, 合约=EB2504, 方向=B, 数量=1
2025-08-20 14:46:27.709 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] WARN  [c.f.f.PnLCalculationFunction:180] - 发现无持仓的平仓交易 - 发送到异常流: 交易号=100052847, 合约=EB2505, 方向=B, 数量=1
2025-08-20 14:46:27.710 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] WARN  [c.f.f.PnLCalculationFunction:180] - 发现无持仓的平仓交易 - 发送到异常流: 交易号=100052849, 合约=EB2504, 方向=S, 数量=10
2025-08-20 14:47:43.994 [main] WARN  [o.a.f.c.k.s.KafkaSourceBuilder:497] - Property auto.offset.reset is provided but will be overridden from earliest to earliest
2025-08-20 14:47:44.010 [main] WARN  [o.a.f.c.k.s.KafkaSourceBuilder:497] - Property auto.offset.reset is provided but will be overridden from earliest to earliest
2025-08-20 14:47:44.010 [main] WARN  [o.a.f.c.k.s.KafkaSourceBuilder:497] - Property auto.offset.reset is provided but will be overridden from earliest to earliest
2025-08-20 14:47:44.484 [main] WARN  [o.a.f.r.s.t.DefaultDelegationTokenManager:248] - No tokens obtained so skipping notifications
2025-08-20 14:47:44.596 [main] WARN  [o.a.f.r.w.WebMonitorUtils:76] - Log file environment variable 'log.file' is not set.
2025-08-20 14:47:44.596 [main] WARN  [o.a.f.r.w.WebMonitorUtils:82] - JobManager log files are unavailable in the web dashboard. Log file location not found in environment variable 'log.file' or configuration key 'web.log.path'.
2025-08-20 14:47:44.654 [flink-pekko.actor.default-dispatcher-5] WARN  [o.a.f.r.s.t.DefaultDelegationTokenManager:320] - No tokens obtained so skipping notifications
2025-08-20 14:47:44.654 [flink-pekko.actor.default-dispatcher-5] WARN  [o.a.f.r.s.t.DefaultDelegationTokenManager:335] - Tokens update task not started because either no tokens obtained or none of the tokens specified its renewal date
2025-08-20 14:47:44.758 [SourceCoordinator-Source: Kafka-交易] WARN  [o.a.k.c.a.AdminClientConfig:385] - These configurations '[key.deserializer, value.deserializer, max.poll.records, client.id.prefix, group.id, partition.discovery.interval.ms, heartbeat.interval.ms, enable.auto.commit, fetch.max.wait.ms, fetch.min.bytes, session.timeout.ms, auto.offset.reset]' were supplied but are not used yet.
2025-08-20 14:47:44.758 [SourceCoordinator-Source: Kafka-组合订单] WARN  [o.a.k.c.a.AdminClientConfig:385] - These configurations '[key.deserializer, value.deserializer, max.poll.records, client.id.prefix, group.id, partition.discovery.interval.ms, heartbeat.interval.ms, enable.auto.commit, fetch.max.wait.ms, fetch.min.bytes, session.timeout.ms, auto.offset.reset]' were supplied but are not used yet.
2025-08-20 14:47:44.758 [SourceCoordinator-Source: Kafka-单腿订单] WARN  [o.a.k.c.a.AdminClientConfig:385] - These configurations '[key.deserializer, value.deserializer, max.poll.records, client.id.prefix, group.id, partition.discovery.interval.ms, heartbeat.interval.ms, enable.auto.commit, fetch.max.wait.ms, fetch.min.bytes, session.timeout.ms, auto.offset.reset]' were supplied but are not used yet.
2025-08-20 14:47:44.759 [SourceCoordinator-Source: Kafka-单腿订单] WARN  [o.a.k.c.u.AppInfoParser:68] - Error registering AppInfo mbean
javax.management.InstanceAlreadyExistsException: kafka.admin.client:type=app-info,id=orderbook-local-test-group-enumerator-admin-client
	at com.sun.jmx.mbeanserver.Repository.addMBean(Repository.java:436) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerWithRepository(DefaultMBeanServerInterceptor.java:1865) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerDynamicMBean(DefaultMBeanServerInterceptor.java:960) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerObject(DefaultMBeanServerInterceptor.java:895) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerMBean(DefaultMBeanServerInterceptor.java:320) ~[?:?]
	at com.sun.jmx.mbeanserver.JmxMBeanServer.registerMBean(JmxMBeanServer.java:523) ~[?:?]
	at org.apache.kafka.common.utils.AppInfoParser.registerAppInfo(AppInfoParser.java:64) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.admin.KafkaAdminClient.<init>(KafkaAdminClient.java:602) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.admin.KafkaAdminClient.createInternal(KafkaAdminClient.java:544) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.admin.KafkaAdminClient.createInternal(KafkaAdminClient.java:488) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.admin.Admin.create(Admin.java:134) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.admin.AdminClient.create(AdminClient.java:39) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.flink.connector.kafka.source.enumerator.KafkaSourceEnumerator.getKafkaAdminClient(KafkaSourceEnumerator.java:441) ~[flink-connector-kafka-3.4.0-1.20.jar:3.4.0-1.20]
	at org.apache.flink.connector.kafka.source.enumerator.KafkaSourceEnumerator.start(KafkaSourceEnumerator.java:164) ~[flink-connector-kafka-3.4.0-1.20.jar:3.4.0-1.20]
	at org.apache.flink.runtime.source.coordinator.SourceCoordinator.lambda$start$1(SourceCoordinator.java:244) ~[flink-runtime-1.20.0.jar:1.20.0]
	at org.apache.flink.runtime.source.coordinator.SourceCoordinator.lambda$runInEventLoop$10(SourceCoordinator.java:533) ~[flink-runtime-1.20.0.jar:1.20.0]
	at org.apache.flink.util.ThrowableCatchingRunnable.run(ThrowableCatchingRunnable.java:40) [flink-core-1.20.0.jar:1.20.0]
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:539) [?:?]
	at java.util.concurrent.FutureTask.run(FutureTask.java:264) [?:?]
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:304) [?:?]
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136) [?:?]
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635) [?:?]
	at java.lang.Thread.run(Thread.java:840) [?:?]
2025-08-20 14:47:44.762 [SourceCoordinator-Source: Kafka-组合订单] WARN  [o.a.k.c.u.AppInfoParser:68] - Error registering AppInfo mbean
javax.management.InstanceAlreadyExistsException: kafka.admin.client:type=app-info,id=orderbook-local-test-group-enumerator-admin-client
	at com.sun.jmx.mbeanserver.Repository.addMBean(Repository.java:436) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerWithRepository(DefaultMBeanServerInterceptor.java:1865) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerDynamicMBean(DefaultMBeanServerInterceptor.java:960) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerObject(DefaultMBeanServerInterceptor.java:895) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerMBean(DefaultMBeanServerInterceptor.java:320) ~[?:?]
	at com.sun.jmx.mbeanserver.JmxMBeanServer.registerMBean(JmxMBeanServer.java:523) ~[?:?]
	at org.apache.kafka.common.utils.AppInfoParser.registerAppInfo(AppInfoParser.java:64) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.admin.KafkaAdminClient.<init>(KafkaAdminClient.java:602) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.admin.KafkaAdminClient.createInternal(KafkaAdminClient.java:544) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.admin.KafkaAdminClient.createInternal(KafkaAdminClient.java:488) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.admin.Admin.create(Admin.java:134) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.admin.AdminClient.create(AdminClient.java:39) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.flink.connector.kafka.source.enumerator.KafkaSourceEnumerator.getKafkaAdminClient(KafkaSourceEnumerator.java:441) ~[flink-connector-kafka-3.4.0-1.20.jar:3.4.0-1.20]
	at org.apache.flink.connector.kafka.source.enumerator.KafkaSourceEnumerator.start(KafkaSourceEnumerator.java:164) ~[flink-connector-kafka-3.4.0-1.20.jar:3.4.0-1.20]
	at org.apache.flink.runtime.source.coordinator.SourceCoordinator.lambda$start$1(SourceCoordinator.java:244) ~[flink-runtime-1.20.0.jar:1.20.0]
	at org.apache.flink.runtime.source.coordinator.SourceCoordinator.lambda$runInEventLoop$10(SourceCoordinator.java:533) ~[flink-runtime-1.20.0.jar:1.20.0]
	at org.apache.flink.util.ThrowableCatchingRunnable.run(ThrowableCatchingRunnable.java:40) [flink-core-1.20.0.jar:1.20.0]
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:539) [?:?]
	at java.util.concurrent.FutureTask.run(FutureTask.java:264) [?:?]
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:304) [?:?]
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136) [?:?]
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635) [?:?]
	at java.lang.Thread.run(Thread.java:840) [?:?]
2025-08-20 14:47:44.906 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] WARN  [o.a.k.c.c.ConsumerConfig:385] - These configurations '[client.id.prefix, partition.discovery.interval.ms]' were supplied but are not used yet.
2025-08-20 14:47:44.907 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] WARN  [o.a.k.c.c.ConsumerConfig:385] - These configurations '[client.id.prefix, partition.discovery.interval.ms]' were supplied but are not used yet.
2025-08-20 14:47:44.906 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] WARN  [o.a.k.c.c.ConsumerConfig:385] - These configurations '[client.id.prefix, partition.discovery.interval.ms]' were supplied but are not used yet.
2025-08-20 14:47:44.907 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] WARN  [o.a.k.c.u.AppInfoParser:68] - Error registering AppInfo mbean
javax.management.InstanceAlreadyExistsException: kafka.consumer:type=app-info,id=orderbook-local-test-group-0
	at com.sun.jmx.mbeanserver.Repository.addMBean(Repository.java:436) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerWithRepository(DefaultMBeanServerInterceptor.java:1865) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerDynamicMBean(DefaultMBeanServerInterceptor.java:960) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerObject(DefaultMBeanServerInterceptor.java:895) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerMBean(DefaultMBeanServerInterceptor.java:320) ~[?:?]
	at com.sun.jmx.mbeanserver.JmxMBeanServer.registerMBean(JmxMBeanServer.java:523) ~[?:?]
	at org.apache.kafka.common.utils.AppInfoParser.registerAppInfo(AppInfoParser.java:64) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.consumer.KafkaConsumer.<init>(KafkaConsumer.java:821) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.consumer.KafkaConsumer.<init>(KafkaConsumer.java:665) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.consumer.KafkaConsumer.<init>(KafkaConsumer.java:646) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.consumer.KafkaConsumer.<init>(KafkaConsumer.java:626) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.flink.connector.kafka.source.reader.KafkaPartitionSplitReader.<init>(KafkaPartitionSplitReader.java:97) ~[flink-connector-kafka-3.4.0-1.20.jar:3.4.0-1.20]
	at org.apache.flink.connector.kafka.source.KafkaSource.lambda$createReader$1(KafkaSource.java:185) ~[flink-connector-kafka-3.4.0-1.20.jar:3.4.0-1.20]
	at org.apache.flink.connector.base.source.reader.fetcher.SplitFetcherManager.createSplitFetcher(SplitFetcherManager.java:259) ~[flink-connector-base-1.20.0.jar:1.20.0]
	at org.apache.flink.connector.base.source.reader.fetcher.SingleThreadFetcherManager.addSplits(SingleThreadFetcherManager.java:148) ~[flink-connector-base-1.20.0.jar:1.20.0]
	at org.apache.flink.connector.base.source.reader.SourceReaderBase.addSplits(SourceReaderBase.java:315) ~[flink-connector-base-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.api.operators.SourceOperator.handleAddSplitsEvent(SourceOperator.java:606) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.api.operators.SourceOperator.handleOperatorEvent(SourceOperator.java:575) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.OperatorEventDispatcherImpl.dispatchEventToHandlers(OperatorEventDispatcherImpl.java:72) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.RegularOperatorChain.dispatchOperatorEvent(RegularOperatorChain.java:80) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.StreamTask.lambda$dispatchOperatorEvent$24(StreamTask.java:1609) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.StreamTaskActionExecutor$1.runThrowing(StreamTaskActionExecutor.java:50) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.mailbox.Mail.run(Mail.java:101) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.mailbox.MailboxProcessor.runMail(MailboxProcessor.java:414) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.mailbox.MailboxProcessor.processMailsWhenDefaultActionUnavailable(MailboxProcessor.java:383) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.mailbox.MailboxProcessor.processMail(MailboxProcessor.java:368) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.mailbox.MailboxProcessor.runMailboxLoop(MailboxProcessor.java:229) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.StreamTask.runMailboxLoop(StreamTask.java:973) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.StreamTask.invoke(StreamTask.java:917) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.runtime.taskmanager.Task.runWithSystemExitMonitoring(Task.java:970) [flink-runtime-1.20.0.jar:1.20.0]
	at org.apache.flink.runtime.taskmanager.Task.restoreAndInvoke(Task.java:949) [flink-runtime-1.20.0.jar:1.20.0]
	at org.apache.flink.runtime.taskmanager.Task.doRun(Task.java:763) [flink-runtime-1.20.0.jar:1.20.0]
	at org.apache.flink.runtime.taskmanager.Task.run(Task.java:575) [flink-runtime-1.20.0.jar:1.20.0]
	at java.lang.Thread.run(Thread.java:840) [?:?]
2025-08-20 14:47:44.907 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] WARN  [o.a.k.c.u.AppInfoParser:68] - Error registering AppInfo mbean
javax.management.InstanceAlreadyExistsException: kafka.consumer:type=app-info,id=orderbook-local-test-group-0
	at com.sun.jmx.mbeanserver.Repository.addMBean(Repository.java:436) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerWithRepository(DefaultMBeanServerInterceptor.java:1865) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerDynamicMBean(DefaultMBeanServerInterceptor.java:960) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerObject(DefaultMBeanServerInterceptor.java:895) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerMBean(DefaultMBeanServerInterceptor.java:320) ~[?:?]
	at com.sun.jmx.mbeanserver.JmxMBeanServer.registerMBean(JmxMBeanServer.java:523) ~[?:?]
	at org.apache.kafka.common.utils.AppInfoParser.registerAppInfo(AppInfoParser.java:64) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.consumer.KafkaConsumer.<init>(KafkaConsumer.java:821) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.consumer.KafkaConsumer.<init>(KafkaConsumer.java:665) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.consumer.KafkaConsumer.<init>(KafkaConsumer.java:646) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.consumer.KafkaConsumer.<init>(KafkaConsumer.java:626) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.flink.connector.kafka.source.reader.KafkaPartitionSplitReader.<init>(KafkaPartitionSplitReader.java:97) ~[flink-connector-kafka-3.4.0-1.20.jar:3.4.0-1.20]
	at org.apache.flink.connector.kafka.source.KafkaSource.lambda$createReader$1(KafkaSource.java:185) ~[flink-connector-kafka-3.4.0-1.20.jar:3.4.0-1.20]
	at org.apache.flink.connector.base.source.reader.fetcher.SplitFetcherManager.createSplitFetcher(SplitFetcherManager.java:259) ~[flink-connector-base-1.20.0.jar:1.20.0]
	at org.apache.flink.connector.base.source.reader.fetcher.SingleThreadFetcherManager.addSplits(SingleThreadFetcherManager.java:148) ~[flink-connector-base-1.20.0.jar:1.20.0]
	at org.apache.flink.connector.base.source.reader.SourceReaderBase.addSplits(SourceReaderBase.java:315) ~[flink-connector-base-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.api.operators.SourceOperator.handleAddSplitsEvent(SourceOperator.java:606) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.api.operators.SourceOperator.handleOperatorEvent(SourceOperator.java:575) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.OperatorEventDispatcherImpl.dispatchEventToHandlers(OperatorEventDispatcherImpl.java:72) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.RegularOperatorChain.dispatchOperatorEvent(RegularOperatorChain.java:80) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.StreamTask.lambda$dispatchOperatorEvent$24(StreamTask.java:1609) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.StreamTaskActionExecutor$1.runThrowing(StreamTaskActionExecutor.java:50) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.mailbox.Mail.run(Mail.java:101) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.mailbox.MailboxProcessor.runMail(MailboxProcessor.java:414) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.mailbox.MailboxProcessor.processMailsWhenDefaultActionUnavailable(MailboxProcessor.java:383) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.mailbox.MailboxProcessor.processMail(MailboxProcessor.java:368) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.mailbox.MailboxProcessor.runMailboxLoop(MailboxProcessor.java:229) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.StreamTask.runMailboxLoop(StreamTask.java:973) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.StreamTask.invoke(StreamTask.java:917) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.runtime.taskmanager.Task.runWithSystemExitMonitoring(Task.java:970) [flink-runtime-1.20.0.jar:1.20.0]
	at org.apache.flink.runtime.taskmanager.Task.restoreAndInvoke(Task.java:949) [flink-runtime-1.20.0.jar:1.20.0]
	at org.apache.flink.runtime.taskmanager.Task.doRun(Task.java:763) [flink-runtime-1.20.0.jar:1.20.0]
	at org.apache.flink.runtime.taskmanager.Task.run(Task.java:575) [flink-runtime-1.20.0.jar:1.20.0]
	at java.lang.Thread.run(Thread.java:840) [?:?]
2025-08-20 14:47:45.048 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] WARN  [c.f.f.PnLCalculationFunction:180] - 发现无持仓的平仓交易 - 发送到异常流: 交易号=101355625, 合约=JD2504-C-3200, 方向=S, 数量=5
2025-08-20 14:47:45.049 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] WARN  [c.f.f.PnLCalculationFunction:180] - 发现无持仓的平仓交易 - 发送到异常流: 交易号=101355626, 合约=JD2504-C-3200, 方向=S, 数量=5
2025-08-20 14:47:45.050 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] WARN  [c.f.f.PnLCalculationFunction:180] - 发现无持仓的平仓交易 - 发送到异常流: 交易号=101355700, 合约=EB2505, 方向=B, 数量=10
2025-08-20 14:47:45.051 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] WARN  [c.f.f.PnLCalculationFunction:180] - 发现无持仓的平仓交易 - 发送到异常流: 交易号=48060, 合约=SH2505, 方向=S, 数量=1
2025-08-20 14:47:45.051 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] WARN  [c.f.f.PnLCalculationFunction:180] - 发现无持仓的平仓交易 - 发送到异常流: 交易号=100052597, 合约=EB2505, 方向=B, 数量=8
2025-08-20 14:47:45.051 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] WARN  [c.f.f.PnLCalculationFunction:180] - 发现无持仓的平仓交易 - 发送到异常流: 交易号=100052597, 合约=EB2505, 方向=S, 数量=8
2025-08-20 14:47:45.052 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] WARN  [c.f.f.PnLCalculationFunction:180] - 发现无持仓的平仓交易 - 发送到异常流: 交易号=100052678, 合约=EB2505, 方向=B, 数量=1
2025-08-20 14:47:45.053 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] WARN  [c.f.f.PnLCalculationFunction:180] - 发现无持仓的平仓交易 - 发送到异常流: 交易号=100052678, 合约=EB2505, 方向=S, 数量=1
2025-08-20 14:47:45.053 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] WARN  [c.f.f.PnLCalculationFunction:180] - 发现无持仓的平仓交易 - 发送到异常流: 交易号=100052768, 合约=EB2505, 方向=B, 数量=1
2025-08-20 14:47:45.054 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] WARN  [c.f.f.PnLCalculationFunction:180] - 发现无持仓的平仓交易 - 发送到异常流: 交易号=100052770, 合约=EB2505, 方向=B, 数量=1
2025-08-20 14:47:45.055 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] WARN  [c.f.f.PnLCalculationFunction:180] - 发现无持仓的平仓交易 - 发送到异常流: 交易号=100052795, 合约=EB2505, 方向=B, 数量=1
2025-08-20 14:47:45.057 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] WARN  [c.f.f.PnLCalculationFunction:180] - 发现无持仓的平仓交易 - 发送到异常流: 交易号=100052821, 合约=EB2505, 方向=B, 数量=1
2025-08-20 14:47:45.057 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] WARN  [c.f.f.PnLCalculationFunction:180] - 发现无持仓的平仓交易 - 发送到异常流: 交易号=100052822, 合约=EB2505, 方向=B, 数量=2
2025-08-20 14:47:45.058 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] WARN  [c.f.f.PnLCalculationFunction:180] - 发现无持仓的平仓交易 - 发送到异常流: 交易号=100052833, 合约=EB2505, 方向=B, 数量=2
2025-08-20 14:47:45.058 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] WARN  [c.f.f.PnLCalculationFunction:180] - 发现无持仓的平仓交易 - 发送到异常流: 交易号=100052846, 合约=EB2504, 方向=B, 数量=1
2025-08-20 14:47:45.059 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] WARN  [c.f.f.PnLCalculationFunction:180] - 发现无持仓的平仓交易 - 发送到异常流: 交易号=100052847, 合约=EB2505, 方向=B, 数量=1
2025-08-20 14:47:45.059 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] WARN  [c.f.f.PnLCalculationFunction:180] - 发现无持仓的平仓交易 - 发送到异常流: 交易号=100052849, 合约=EB2504, 方向=S, 数量=10
2025-08-20 15:15:03.319 [main] WARN  [o.a.f.c.k.s.KafkaSourceBuilder:497] - Property auto.offset.reset is provided but will be overridden from earliest to earliest
2025-08-20 15:15:03.336 [main] WARN  [o.a.f.c.k.s.KafkaSourceBuilder:497] - Property auto.offset.reset is provided but will be overridden from earliest to earliest
2025-08-20 15:15:03.337 [main] WARN  [o.a.f.c.k.s.KafkaSourceBuilder:497] - Property auto.offset.reset is provided but will be overridden from earliest to earliest
2025-08-20 15:15:03.781 [main] WARN  [o.a.f.r.s.t.DefaultDelegationTokenManager:248] - No tokens obtained so skipping notifications
2025-08-20 15:15:03.898 [main] WARN  [o.a.f.r.w.WebMonitorUtils:76] - Log file environment variable 'log.file' is not set.
2025-08-20 15:15:03.898 [main] WARN  [o.a.f.r.w.WebMonitorUtils:82] - JobManager log files are unavailable in the web dashboard. Log file location not found in environment variable 'log.file' or configuration key 'web.log.path'.
2025-08-20 15:15:03.952 [flink-pekko.actor.default-dispatcher-5] WARN  [o.a.f.r.s.t.DefaultDelegationTokenManager:320] - No tokens obtained so skipping notifications
2025-08-20 15:15:03.953 [flink-pekko.actor.default-dispatcher-5] WARN  [o.a.f.r.s.t.DefaultDelegationTokenManager:335] - Tokens update task not started because either no tokens obtained or none of the tokens specified its renewal date
2025-08-20 15:15:04.062 [SourceCoordinator-Source: Kafka-交易] WARN  [o.a.k.c.a.AdminClientConfig:385] - These configurations '[key.deserializer, value.deserializer, max.poll.records, client.id.prefix, group.id, partition.discovery.interval.ms, heartbeat.interval.ms, enable.auto.commit, fetch.max.wait.ms, fetch.min.bytes, session.timeout.ms, auto.offset.reset]' were supplied but are not used yet.
2025-08-20 15:15:04.062 [SourceCoordinator-Source: Kafka-单腿订单] WARN  [o.a.k.c.a.AdminClientConfig:385] - These configurations '[key.deserializer, value.deserializer, max.poll.records, client.id.prefix, group.id, partition.discovery.interval.ms, heartbeat.interval.ms, enable.auto.commit, fetch.max.wait.ms, fetch.min.bytes, session.timeout.ms, auto.offset.reset]' were supplied but are not used yet.
2025-08-20 15:15:04.062 [SourceCoordinator-Source: Kafka-组合订单] WARN  [o.a.k.c.a.AdminClientConfig:385] - These configurations '[key.deserializer, value.deserializer, max.poll.records, client.id.prefix, group.id, partition.discovery.interval.ms, heartbeat.interval.ms, enable.auto.commit, fetch.max.wait.ms, fetch.min.bytes, session.timeout.ms, auto.offset.reset]' were supplied but are not used yet.
2025-08-20 15:15:04.063 [SourceCoordinator-Source: Kafka-交易] WARN  [o.a.k.c.u.AppInfoParser:68] - Error registering AppInfo mbean
javax.management.InstanceAlreadyExistsException: kafka.admin.client:type=app-info,id=orderbook-local-test-group-enumerator-admin-client
	at com.sun.jmx.mbeanserver.Repository.addMBean(Repository.java:436) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerWithRepository(DefaultMBeanServerInterceptor.java:1865) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerDynamicMBean(DefaultMBeanServerInterceptor.java:960) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerObject(DefaultMBeanServerInterceptor.java:895) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerMBean(DefaultMBeanServerInterceptor.java:320) ~[?:?]
	at com.sun.jmx.mbeanserver.JmxMBeanServer.registerMBean(JmxMBeanServer.java:523) ~[?:?]
	at org.apache.kafka.common.utils.AppInfoParser.registerAppInfo(AppInfoParser.java:64) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.admin.KafkaAdminClient.<init>(KafkaAdminClient.java:602) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.admin.KafkaAdminClient.createInternal(KafkaAdminClient.java:544) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.admin.KafkaAdminClient.createInternal(KafkaAdminClient.java:488) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.admin.Admin.create(Admin.java:134) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.admin.AdminClient.create(AdminClient.java:39) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.flink.connector.kafka.source.enumerator.KafkaSourceEnumerator.getKafkaAdminClient(KafkaSourceEnumerator.java:441) ~[flink-connector-kafka-3.4.0-1.20.jar:3.4.0-1.20]
	at org.apache.flink.connector.kafka.source.enumerator.KafkaSourceEnumerator.start(KafkaSourceEnumerator.java:164) ~[flink-connector-kafka-3.4.0-1.20.jar:3.4.0-1.20]
	at org.apache.flink.runtime.source.coordinator.SourceCoordinator.lambda$start$1(SourceCoordinator.java:244) ~[flink-runtime-1.20.0.jar:1.20.0]
	at org.apache.flink.runtime.source.coordinator.SourceCoordinator.lambda$runInEventLoop$10(SourceCoordinator.java:533) ~[flink-runtime-1.20.0.jar:1.20.0]
	at org.apache.flink.util.ThrowableCatchingRunnable.run(ThrowableCatchingRunnable.java:40) [flink-core-1.20.0.jar:1.20.0]
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:539) [?:?]
	at java.util.concurrent.FutureTask.run(FutureTask.java:264) [?:?]
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:304) [?:?]
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136) [?:?]
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635) [?:?]
	at java.lang.Thread.run(Thread.java:840) [?:?]
2025-08-20 15:15:04.066 [SourceCoordinator-Source: Kafka-单腿订单] WARN  [o.a.k.c.u.AppInfoParser:68] - Error registering AppInfo mbean
javax.management.InstanceAlreadyExistsException: kafka.admin.client:type=app-info,id=orderbook-local-test-group-enumerator-admin-client
	at com.sun.jmx.mbeanserver.Repository.addMBean(Repository.java:436) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerWithRepository(DefaultMBeanServerInterceptor.java:1865) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerDynamicMBean(DefaultMBeanServerInterceptor.java:960) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerObject(DefaultMBeanServerInterceptor.java:895) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerMBean(DefaultMBeanServerInterceptor.java:320) ~[?:?]
	at com.sun.jmx.mbeanserver.JmxMBeanServer.registerMBean(JmxMBeanServer.java:523) ~[?:?]
	at org.apache.kafka.common.utils.AppInfoParser.registerAppInfo(AppInfoParser.java:64) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.admin.KafkaAdminClient.<init>(KafkaAdminClient.java:602) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.admin.KafkaAdminClient.createInternal(KafkaAdminClient.java:544) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.admin.KafkaAdminClient.createInternal(KafkaAdminClient.java:488) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.admin.Admin.create(Admin.java:134) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.admin.AdminClient.create(AdminClient.java:39) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.flink.connector.kafka.source.enumerator.KafkaSourceEnumerator.getKafkaAdminClient(KafkaSourceEnumerator.java:441) ~[flink-connector-kafka-3.4.0-1.20.jar:3.4.0-1.20]
	at org.apache.flink.connector.kafka.source.enumerator.KafkaSourceEnumerator.start(KafkaSourceEnumerator.java:164) ~[flink-connector-kafka-3.4.0-1.20.jar:3.4.0-1.20]
	at org.apache.flink.runtime.source.coordinator.SourceCoordinator.lambda$start$1(SourceCoordinator.java:244) ~[flink-runtime-1.20.0.jar:1.20.0]
	at org.apache.flink.runtime.source.coordinator.SourceCoordinator.lambda$runInEventLoop$10(SourceCoordinator.java:533) ~[flink-runtime-1.20.0.jar:1.20.0]
	at org.apache.flink.util.ThrowableCatchingRunnable.run(ThrowableCatchingRunnable.java:40) [flink-core-1.20.0.jar:1.20.0]
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:539) [?:?]
	at java.util.concurrent.FutureTask.run(FutureTask.java:264) [?:?]
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:304) [?:?]
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136) [?:?]
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635) [?:?]
	at java.lang.Thread.run(Thread.java:840) [?:?]
2025-08-20 15:15:04.239 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] WARN  [o.a.k.c.c.ConsumerConfig:385] - These configurations '[client.id.prefix, partition.discovery.interval.ms]' were supplied but are not used yet.
2025-08-20 15:15:04.239 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] WARN  [o.a.k.c.c.ConsumerConfig:385] - These configurations '[client.id.prefix, partition.discovery.interval.ms]' were supplied but are not used yet.
2025-08-20 15:15:04.239 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] WARN  [o.a.k.c.c.ConsumerConfig:385] - These configurations '[client.id.prefix, partition.discovery.interval.ms]' were supplied but are not used yet.
2025-08-20 15:15:04.240 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] WARN  [o.a.k.c.u.AppInfoParser:68] - Error registering AppInfo mbean
javax.management.InstanceAlreadyExistsException: kafka.consumer:type=app-info,id=orderbook-local-test-group-0
	at com.sun.jmx.mbeanserver.Repository.addMBean(Repository.java:436) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerWithRepository(DefaultMBeanServerInterceptor.java:1865) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerDynamicMBean(DefaultMBeanServerInterceptor.java:960) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerObject(DefaultMBeanServerInterceptor.java:895) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerMBean(DefaultMBeanServerInterceptor.java:320) ~[?:?]
	at com.sun.jmx.mbeanserver.JmxMBeanServer.registerMBean(JmxMBeanServer.java:523) ~[?:?]
	at org.apache.kafka.common.utils.AppInfoParser.registerAppInfo(AppInfoParser.java:64) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.consumer.KafkaConsumer.<init>(KafkaConsumer.java:821) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.consumer.KafkaConsumer.<init>(KafkaConsumer.java:665) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.consumer.KafkaConsumer.<init>(KafkaConsumer.java:646) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.consumer.KafkaConsumer.<init>(KafkaConsumer.java:626) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.flink.connector.kafka.source.reader.KafkaPartitionSplitReader.<init>(KafkaPartitionSplitReader.java:97) ~[flink-connector-kafka-3.4.0-1.20.jar:3.4.0-1.20]
	at org.apache.flink.connector.kafka.source.KafkaSource.lambda$createReader$1(KafkaSource.java:185) ~[flink-connector-kafka-3.4.0-1.20.jar:3.4.0-1.20]
	at org.apache.flink.connector.base.source.reader.fetcher.SplitFetcherManager.createSplitFetcher(SplitFetcherManager.java:259) ~[flink-connector-base-1.20.0.jar:1.20.0]
	at org.apache.flink.connector.base.source.reader.fetcher.SingleThreadFetcherManager.addSplits(SingleThreadFetcherManager.java:148) ~[flink-connector-base-1.20.0.jar:1.20.0]
	at org.apache.flink.connector.base.source.reader.SourceReaderBase.addSplits(SourceReaderBase.java:315) ~[flink-connector-base-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.api.operators.SourceOperator.handleAddSplitsEvent(SourceOperator.java:606) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.api.operators.SourceOperator.handleOperatorEvent(SourceOperator.java:575) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.OperatorEventDispatcherImpl.dispatchEventToHandlers(OperatorEventDispatcherImpl.java:72) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.RegularOperatorChain.dispatchOperatorEvent(RegularOperatorChain.java:80) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.StreamTask.lambda$dispatchOperatorEvent$24(StreamTask.java:1609) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.StreamTaskActionExecutor$1.runThrowing(StreamTaskActionExecutor.java:50) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.mailbox.Mail.run(Mail.java:101) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.mailbox.MailboxProcessor.runMail(MailboxProcessor.java:414) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.mailbox.MailboxProcessor.processMailsWhenDefaultActionUnavailable(MailboxProcessor.java:383) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.mailbox.MailboxProcessor.processMail(MailboxProcessor.java:368) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.mailbox.MailboxProcessor.runMailboxLoop(MailboxProcessor.java:229) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.StreamTask.runMailboxLoop(StreamTask.java:973) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.StreamTask.invoke(StreamTask.java:917) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.runtime.taskmanager.Task.runWithSystemExitMonitoring(Task.java:970) [flink-runtime-1.20.0.jar:1.20.0]
	at org.apache.flink.runtime.taskmanager.Task.restoreAndInvoke(Task.java:949) [flink-runtime-1.20.0.jar:1.20.0]
	at org.apache.flink.runtime.taskmanager.Task.doRun(Task.java:763) [flink-runtime-1.20.0.jar:1.20.0]
	at org.apache.flink.runtime.taskmanager.Task.run(Task.java:575) [flink-runtime-1.20.0.jar:1.20.0]
	at java.lang.Thread.run(Thread.java:840) [?:?]
2025-08-20 15:15:04.240 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] WARN  [o.a.k.c.u.AppInfoParser:68] - Error registering AppInfo mbean
javax.management.InstanceAlreadyExistsException: kafka.consumer:type=app-info,id=orderbook-local-test-group-0
	at com.sun.jmx.mbeanserver.Repository.addMBean(Repository.java:436) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerWithRepository(DefaultMBeanServerInterceptor.java:1865) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerDynamicMBean(DefaultMBeanServerInterceptor.java:960) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerObject(DefaultMBeanServerInterceptor.java:895) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerMBean(DefaultMBeanServerInterceptor.java:320) ~[?:?]
	at com.sun.jmx.mbeanserver.JmxMBeanServer.registerMBean(JmxMBeanServer.java:523) ~[?:?]
	at org.apache.kafka.common.utils.AppInfoParser.registerAppInfo(AppInfoParser.java:64) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.consumer.KafkaConsumer.<init>(KafkaConsumer.java:821) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.consumer.KafkaConsumer.<init>(KafkaConsumer.java:665) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.consumer.KafkaConsumer.<init>(KafkaConsumer.java:646) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.consumer.KafkaConsumer.<init>(KafkaConsumer.java:626) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.flink.connector.kafka.source.reader.KafkaPartitionSplitReader.<init>(KafkaPartitionSplitReader.java:97) ~[flink-connector-kafka-3.4.0-1.20.jar:3.4.0-1.20]
	at org.apache.flink.connector.kafka.source.KafkaSource.lambda$createReader$1(KafkaSource.java:185) ~[flink-connector-kafka-3.4.0-1.20.jar:3.4.0-1.20]
	at org.apache.flink.connector.base.source.reader.fetcher.SplitFetcherManager.createSplitFetcher(SplitFetcherManager.java:259) ~[flink-connector-base-1.20.0.jar:1.20.0]
	at org.apache.flink.connector.base.source.reader.fetcher.SingleThreadFetcherManager.addSplits(SingleThreadFetcherManager.java:148) ~[flink-connector-base-1.20.0.jar:1.20.0]
	at org.apache.flink.connector.base.source.reader.SourceReaderBase.addSplits(SourceReaderBase.java:315) ~[flink-connector-base-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.api.operators.SourceOperator.handleAddSplitsEvent(SourceOperator.java:606) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.api.operators.SourceOperator.handleOperatorEvent(SourceOperator.java:575) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.OperatorEventDispatcherImpl.dispatchEventToHandlers(OperatorEventDispatcherImpl.java:72) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.RegularOperatorChain.dispatchOperatorEvent(RegularOperatorChain.java:80) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.StreamTask.lambda$dispatchOperatorEvent$24(StreamTask.java:1609) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.StreamTaskActionExecutor$1.runThrowing(StreamTaskActionExecutor.java:50) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.mailbox.Mail.run(Mail.java:101) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.mailbox.MailboxProcessor.runMail(MailboxProcessor.java:414) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.mailbox.MailboxProcessor.processMailsWhenDefaultActionUnavailable(MailboxProcessor.java:383) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.mailbox.MailboxProcessor.processMail(MailboxProcessor.java:368) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.mailbox.MailboxProcessor.runMailboxLoop(MailboxProcessor.java:229) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.StreamTask.runMailboxLoop(StreamTask.java:973) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.StreamTask.invoke(StreamTask.java:917) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.runtime.taskmanager.Task.runWithSystemExitMonitoring(Task.java:970) [flink-runtime-1.20.0.jar:1.20.0]
	at org.apache.flink.runtime.taskmanager.Task.restoreAndInvoke(Task.java:949) [flink-runtime-1.20.0.jar:1.20.0]
	at org.apache.flink.runtime.taskmanager.Task.doRun(Task.java:763) [flink-runtime-1.20.0.jar:1.20.0]
	at org.apache.flink.runtime.taskmanager.Task.run(Task.java:575) [flink-runtime-1.20.0.jar:1.20.0]
	at java.lang.Thread.run(Thread.java:840) [?:?]
2025-08-20 15:17:23.899 [main] WARN  [o.a.f.c.k.s.KafkaSourceBuilder:497] - Property auto.offset.reset is provided but will be overridden from earliest to earliest
2025-08-20 15:17:23.917 [main] WARN  [o.a.f.c.k.s.KafkaSourceBuilder:497] - Property auto.offset.reset is provided but will be overridden from earliest to earliest
2025-08-20 15:17:23.918 [main] WARN  [o.a.f.c.k.s.KafkaSourceBuilder:497] - Property auto.offset.reset is provided but will be overridden from earliest to earliest
2025-08-20 15:17:24.384 [main] WARN  [o.a.f.r.s.t.DefaultDelegationTokenManager:248] - No tokens obtained so skipping notifications
2025-08-20 15:17:24.496 [main] WARN  [o.a.f.r.w.WebMonitorUtils:76] - Log file environment variable 'log.file' is not set.
2025-08-20 15:17:24.496 [main] WARN  [o.a.f.r.w.WebMonitorUtils:82] - JobManager log files are unavailable in the web dashboard. Log file location not found in environment variable 'log.file' or configuration key 'web.log.path'.
2025-08-20 15:17:24.556 [flink-pekko.actor.default-dispatcher-4] WARN  [o.a.f.r.s.t.DefaultDelegationTokenManager:320] - No tokens obtained so skipping notifications
2025-08-20 15:17:24.557 [flink-pekko.actor.default-dispatcher-4] WARN  [o.a.f.r.s.t.DefaultDelegationTokenManager:335] - Tokens update task not started because either no tokens obtained or none of the tokens specified its renewal date
2025-08-20 15:17:24.664 [SourceCoordinator-Source: Kafka-组合订单] WARN  [o.a.k.c.a.AdminClientConfig:385] - These configurations '[key.deserializer, value.deserializer, max.poll.records, client.id.prefix, group.id, partition.discovery.interval.ms, heartbeat.interval.ms, enable.auto.commit, fetch.max.wait.ms, fetch.min.bytes, session.timeout.ms, auto.offset.reset]' were supplied but are not used yet.
2025-08-20 15:17:24.664 [SourceCoordinator-Source: Kafka-交易] WARN  [o.a.k.c.a.AdminClientConfig:385] - These configurations '[key.deserializer, value.deserializer, max.poll.records, client.id.prefix, group.id, partition.discovery.interval.ms, heartbeat.interval.ms, enable.auto.commit, fetch.max.wait.ms, fetch.min.bytes, session.timeout.ms, auto.offset.reset]' were supplied but are not used yet.
2025-08-20 15:17:24.664 [SourceCoordinator-Source: Kafka-单腿订单] WARN  [o.a.k.c.a.AdminClientConfig:385] - These configurations '[key.deserializer, value.deserializer, max.poll.records, client.id.prefix, group.id, partition.discovery.interval.ms, heartbeat.interval.ms, enable.auto.commit, fetch.max.wait.ms, fetch.min.bytes, session.timeout.ms, auto.offset.reset]' were supplied but are not used yet.
2025-08-20 15:17:24.665 [SourceCoordinator-Source: Kafka-单腿订单] WARN  [o.a.k.c.u.AppInfoParser:68] - Error registering AppInfo mbean
javax.management.InstanceAlreadyExistsException: kafka.admin.client:type=app-info,id=orderbook-local-test-group-enumerator-admin-client
	at com.sun.jmx.mbeanserver.Repository.addMBean(Repository.java:436) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerWithRepository(DefaultMBeanServerInterceptor.java:1865) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerDynamicMBean(DefaultMBeanServerInterceptor.java:960) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerObject(DefaultMBeanServerInterceptor.java:895) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerMBean(DefaultMBeanServerInterceptor.java:320) ~[?:?]
	at com.sun.jmx.mbeanserver.JmxMBeanServer.registerMBean(JmxMBeanServer.java:523) ~[?:?]
	at org.apache.kafka.common.utils.AppInfoParser.registerAppInfo(AppInfoParser.java:64) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.admin.KafkaAdminClient.<init>(KafkaAdminClient.java:602) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.admin.KafkaAdminClient.createInternal(KafkaAdminClient.java:544) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.admin.KafkaAdminClient.createInternal(KafkaAdminClient.java:488) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.admin.Admin.create(Admin.java:134) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.admin.AdminClient.create(AdminClient.java:39) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.flink.connector.kafka.source.enumerator.KafkaSourceEnumerator.getKafkaAdminClient(KafkaSourceEnumerator.java:441) ~[flink-connector-kafka-3.4.0-1.20.jar:3.4.0-1.20]
	at org.apache.flink.connector.kafka.source.enumerator.KafkaSourceEnumerator.start(KafkaSourceEnumerator.java:164) ~[flink-connector-kafka-3.4.0-1.20.jar:3.4.0-1.20]
	at org.apache.flink.runtime.source.coordinator.SourceCoordinator.lambda$start$1(SourceCoordinator.java:244) ~[flink-runtime-1.20.0.jar:1.20.0]
	at org.apache.flink.runtime.source.coordinator.SourceCoordinator.lambda$runInEventLoop$10(SourceCoordinator.java:533) ~[flink-runtime-1.20.0.jar:1.20.0]
	at org.apache.flink.util.ThrowableCatchingRunnable.run(ThrowableCatchingRunnable.java:40) [flink-core-1.20.0.jar:1.20.0]
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:539) [?:?]
	at java.util.concurrent.FutureTask.run(FutureTask.java:264) [?:?]
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:304) [?:?]
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136) [?:?]
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635) [?:?]
	at java.lang.Thread.run(Thread.java:840) [?:?]
2025-08-20 15:17:24.668 [SourceCoordinator-Source: Kafka-交易] WARN  [o.a.k.c.u.AppInfoParser:68] - Error registering AppInfo mbean
javax.management.InstanceAlreadyExistsException: kafka.admin.client:type=app-info,id=orderbook-local-test-group-enumerator-admin-client
	at com.sun.jmx.mbeanserver.Repository.addMBean(Repository.java:436) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerWithRepository(DefaultMBeanServerInterceptor.java:1865) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerDynamicMBean(DefaultMBeanServerInterceptor.java:960) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerObject(DefaultMBeanServerInterceptor.java:895) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerMBean(DefaultMBeanServerInterceptor.java:320) ~[?:?]
	at com.sun.jmx.mbeanserver.JmxMBeanServer.registerMBean(JmxMBeanServer.java:523) ~[?:?]
	at org.apache.kafka.common.utils.AppInfoParser.registerAppInfo(AppInfoParser.java:64) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.admin.KafkaAdminClient.<init>(KafkaAdminClient.java:602) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.admin.KafkaAdminClient.createInternal(KafkaAdminClient.java:544) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.admin.KafkaAdminClient.createInternal(KafkaAdminClient.java:488) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.admin.Admin.create(Admin.java:134) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.admin.AdminClient.create(AdminClient.java:39) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.flink.connector.kafka.source.enumerator.KafkaSourceEnumerator.getKafkaAdminClient(KafkaSourceEnumerator.java:441) ~[flink-connector-kafka-3.4.0-1.20.jar:3.4.0-1.20]
	at org.apache.flink.connector.kafka.source.enumerator.KafkaSourceEnumerator.start(KafkaSourceEnumerator.java:164) ~[flink-connector-kafka-3.4.0-1.20.jar:3.4.0-1.20]
	at org.apache.flink.runtime.source.coordinator.SourceCoordinator.lambda$start$1(SourceCoordinator.java:244) ~[flink-runtime-1.20.0.jar:1.20.0]
	at org.apache.flink.runtime.source.coordinator.SourceCoordinator.lambda$runInEventLoop$10(SourceCoordinator.java:533) ~[flink-runtime-1.20.0.jar:1.20.0]
	at org.apache.flink.util.ThrowableCatchingRunnable.run(ThrowableCatchingRunnable.java:40) [flink-core-1.20.0.jar:1.20.0]
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:539) [?:?]
	at java.util.concurrent.FutureTask.run(FutureTask.java:264) [?:?]
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:304) [?:?]
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136) [?:?]
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635) [?:?]
	at java.lang.Thread.run(Thread.java:840) [?:?]
2025-08-20 15:17:24.816 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] WARN  [o.a.k.c.c.ConsumerConfig:385] - These configurations '[client.id.prefix, partition.discovery.interval.ms]' were supplied but are not used yet.
2025-08-20 15:17:24.816 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] WARN  [o.a.k.c.c.ConsumerConfig:385] - These configurations '[client.id.prefix, partition.discovery.interval.ms]' were supplied but are not used yet.
2025-08-20 15:17:24.816 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] WARN  [o.a.k.c.c.ConsumerConfig:385] - These configurations '[client.id.prefix, partition.discovery.interval.ms]' were supplied but are not used yet.
2025-08-20 15:17:24.817 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] WARN  [o.a.k.c.u.AppInfoParser:68] - Error registering AppInfo mbean
javax.management.InstanceAlreadyExistsException: kafka.consumer:type=app-info,id=orderbook-local-test-group-0
	at com.sun.jmx.mbeanserver.Repository.addMBean(Repository.java:436) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerWithRepository(DefaultMBeanServerInterceptor.java:1865) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerDynamicMBean(DefaultMBeanServerInterceptor.java:960) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerObject(DefaultMBeanServerInterceptor.java:895) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerMBean(DefaultMBeanServerInterceptor.java:320) ~[?:?]
	at com.sun.jmx.mbeanserver.JmxMBeanServer.registerMBean(JmxMBeanServer.java:523) ~[?:?]
	at org.apache.kafka.common.utils.AppInfoParser.registerAppInfo(AppInfoParser.java:64) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.consumer.KafkaConsumer.<init>(KafkaConsumer.java:821) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.consumer.KafkaConsumer.<init>(KafkaConsumer.java:665) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.consumer.KafkaConsumer.<init>(KafkaConsumer.java:646) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.consumer.KafkaConsumer.<init>(KafkaConsumer.java:626) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.flink.connector.kafka.source.reader.KafkaPartitionSplitReader.<init>(KafkaPartitionSplitReader.java:97) ~[flink-connector-kafka-3.4.0-1.20.jar:3.4.0-1.20]
	at org.apache.flink.connector.kafka.source.KafkaSource.lambda$createReader$1(KafkaSource.java:185) ~[flink-connector-kafka-3.4.0-1.20.jar:3.4.0-1.20]
	at org.apache.flink.connector.base.source.reader.fetcher.SplitFetcherManager.createSplitFetcher(SplitFetcherManager.java:259) ~[flink-connector-base-1.20.0.jar:1.20.0]
	at org.apache.flink.connector.base.source.reader.fetcher.SingleThreadFetcherManager.addSplits(SingleThreadFetcherManager.java:148) ~[flink-connector-base-1.20.0.jar:1.20.0]
	at org.apache.flink.connector.base.source.reader.SourceReaderBase.addSplits(SourceReaderBase.java:315) ~[flink-connector-base-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.api.operators.SourceOperator.handleAddSplitsEvent(SourceOperator.java:606) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.api.operators.SourceOperator.handleOperatorEvent(SourceOperator.java:575) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.OperatorEventDispatcherImpl.dispatchEventToHandlers(OperatorEventDispatcherImpl.java:72) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.RegularOperatorChain.dispatchOperatorEvent(RegularOperatorChain.java:80) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.StreamTask.lambda$dispatchOperatorEvent$24(StreamTask.java:1609) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.StreamTaskActionExecutor$1.runThrowing(StreamTaskActionExecutor.java:50) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.mailbox.Mail.run(Mail.java:101) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.mailbox.MailboxProcessor.runMail(MailboxProcessor.java:414) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.mailbox.MailboxProcessor.processMailsWhenDefaultActionUnavailable(MailboxProcessor.java:383) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.mailbox.MailboxProcessor.processMail(MailboxProcessor.java:368) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.mailbox.MailboxProcessor.runMailboxLoop(MailboxProcessor.java:229) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.StreamTask.runMailboxLoop(StreamTask.java:973) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.StreamTask.invoke(StreamTask.java:917) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.runtime.taskmanager.Task.runWithSystemExitMonitoring(Task.java:970) [flink-runtime-1.20.0.jar:1.20.0]
	at org.apache.flink.runtime.taskmanager.Task.restoreAndInvoke(Task.java:949) [flink-runtime-1.20.0.jar:1.20.0]
	at org.apache.flink.runtime.taskmanager.Task.doRun(Task.java:763) [flink-runtime-1.20.0.jar:1.20.0]
	at org.apache.flink.runtime.taskmanager.Task.run(Task.java:575) [flink-runtime-1.20.0.jar:1.20.0]
	at java.lang.Thread.run(Thread.java:840) [?:?]
2025-08-20 15:17:24.817 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] WARN  [o.a.k.c.u.AppInfoParser:68] - Error registering AppInfo mbean
javax.management.InstanceAlreadyExistsException: kafka.consumer:type=app-info,id=orderbook-local-test-group-0
	at com.sun.jmx.mbeanserver.Repository.addMBean(Repository.java:436) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerWithRepository(DefaultMBeanServerInterceptor.java:1865) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerDynamicMBean(DefaultMBeanServerInterceptor.java:960) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerObject(DefaultMBeanServerInterceptor.java:895) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerMBean(DefaultMBeanServerInterceptor.java:320) ~[?:?]
	at com.sun.jmx.mbeanserver.JmxMBeanServer.registerMBean(JmxMBeanServer.java:523) ~[?:?]
	at org.apache.kafka.common.utils.AppInfoParser.registerAppInfo(AppInfoParser.java:64) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.consumer.KafkaConsumer.<init>(KafkaConsumer.java:821) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.consumer.KafkaConsumer.<init>(KafkaConsumer.java:665) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.consumer.KafkaConsumer.<init>(KafkaConsumer.java:646) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.consumer.KafkaConsumer.<init>(KafkaConsumer.java:626) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.flink.connector.kafka.source.reader.KafkaPartitionSplitReader.<init>(KafkaPartitionSplitReader.java:97) ~[flink-connector-kafka-3.4.0-1.20.jar:3.4.0-1.20]
	at org.apache.flink.connector.kafka.source.KafkaSource.lambda$createReader$1(KafkaSource.java:185) ~[flink-connector-kafka-3.4.0-1.20.jar:3.4.0-1.20]
	at org.apache.flink.connector.base.source.reader.fetcher.SplitFetcherManager.createSplitFetcher(SplitFetcherManager.java:259) ~[flink-connector-base-1.20.0.jar:1.20.0]
	at org.apache.flink.connector.base.source.reader.fetcher.SingleThreadFetcherManager.addSplits(SingleThreadFetcherManager.java:148) ~[flink-connector-base-1.20.0.jar:1.20.0]
	at org.apache.flink.connector.base.source.reader.SourceReaderBase.addSplits(SourceReaderBase.java:315) ~[flink-connector-base-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.api.operators.SourceOperator.handleAddSplitsEvent(SourceOperator.java:606) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.api.operators.SourceOperator.handleOperatorEvent(SourceOperator.java:575) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.OperatorEventDispatcherImpl.dispatchEventToHandlers(OperatorEventDispatcherImpl.java:72) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.RegularOperatorChain.dispatchOperatorEvent(RegularOperatorChain.java:80) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.StreamTask.lambda$dispatchOperatorEvent$24(StreamTask.java:1609) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.StreamTaskActionExecutor$1.runThrowing(StreamTaskActionExecutor.java:50) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.mailbox.Mail.run(Mail.java:101) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.mailbox.MailboxProcessor.runMail(MailboxProcessor.java:414) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.mailbox.MailboxProcessor.processMailsWhenDefaultActionUnavailable(MailboxProcessor.java:383) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.mailbox.MailboxProcessor.processMail(MailboxProcessor.java:368) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.mailbox.MailboxProcessor.runMailboxLoop(MailboxProcessor.java:229) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.StreamTask.runMailboxLoop(StreamTask.java:973) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.StreamTask.invoke(StreamTask.java:917) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.runtime.taskmanager.Task.runWithSystemExitMonitoring(Task.java:970) [flink-runtime-1.20.0.jar:1.20.0]
	at org.apache.flink.runtime.taskmanager.Task.restoreAndInvoke(Task.java:949) [flink-runtime-1.20.0.jar:1.20.0]
	at org.apache.flink.runtime.taskmanager.Task.doRun(Task.java:763) [flink-runtime-1.20.0.jar:1.20.0]
	at org.apache.flink.runtime.taskmanager.Task.run(Task.java:575) [flink-runtime-1.20.0.jar:1.20.0]
	at java.lang.Thread.run(Thread.java:840) [?:?]
2025-08-20 15:17:59.545 [main] WARN  [o.a.f.c.k.s.KafkaSourceBuilder:497] - Property auto.offset.reset is provided but will be overridden from earliest to earliest
2025-08-20 15:17:59.560 [main] WARN  [o.a.f.c.k.s.KafkaSourceBuilder:497] - Property auto.offset.reset is provided but will be overridden from earliest to earliest
2025-08-20 15:17:59.561 [main] WARN  [o.a.f.c.k.s.KafkaSourceBuilder:497] - Property auto.offset.reset is provided but will be overridden from earliest to earliest
2025-08-20 15:17:59.983 [main] WARN  [o.a.f.r.s.t.DefaultDelegationTokenManager:248] - No tokens obtained so skipping notifications
2025-08-20 15:18:00.084 [main] WARN  [o.a.f.r.w.WebMonitorUtils:76] - Log file environment variable 'log.file' is not set.
2025-08-20 15:18:00.085 [main] WARN  [o.a.f.r.w.WebMonitorUtils:82] - JobManager log files are unavailable in the web dashboard. Log file location not found in environment variable 'log.file' or configuration key 'web.log.path'.
2025-08-20 15:18:00.141 [flink-pekko.actor.default-dispatcher-4] WARN  [o.a.f.r.s.t.DefaultDelegationTokenManager:320] - No tokens obtained so skipping notifications
2025-08-20 15:18:00.141 [flink-pekko.actor.default-dispatcher-4] WARN  [o.a.f.r.s.t.DefaultDelegationTokenManager:335] - Tokens update task not started because either no tokens obtained or none of the tokens specified its renewal date
2025-08-20 15:18:00.241 [SourceCoordinator-Source: Kafka-组合订单] WARN  [o.a.k.c.a.AdminClientConfig:385] - These configurations '[key.deserializer, value.deserializer, max.poll.records, client.id.prefix, group.id, partition.discovery.interval.ms, heartbeat.interval.ms, enable.auto.commit, fetch.max.wait.ms, fetch.min.bytes, session.timeout.ms, auto.offset.reset]' were supplied but are not used yet.
2025-08-20 15:18:00.241 [SourceCoordinator-Source: Kafka-交易] WARN  [o.a.k.c.a.AdminClientConfig:385] - These configurations '[key.deserializer, value.deserializer, max.poll.records, client.id.prefix, group.id, partition.discovery.interval.ms, heartbeat.interval.ms, enable.auto.commit, fetch.max.wait.ms, fetch.min.bytes, session.timeout.ms, auto.offset.reset]' were supplied but are not used yet.
2025-08-20 15:18:00.241 [SourceCoordinator-Source: Kafka-单腿订单] WARN  [o.a.k.c.a.AdminClientConfig:385] - These configurations '[key.deserializer, value.deserializer, max.poll.records, client.id.prefix, group.id, partition.discovery.interval.ms, heartbeat.interval.ms, enable.auto.commit, fetch.max.wait.ms, fetch.min.bytes, session.timeout.ms, auto.offset.reset]' were supplied but are not used yet.
2025-08-20 15:18:00.243 [SourceCoordinator-Source: Kafka-单腿订单] WARN  [o.a.k.c.u.AppInfoParser:68] - Error registering AppInfo mbean
javax.management.InstanceAlreadyExistsException: kafka.admin.client:type=app-info,id=orderbook-local-test-group-enumerator-admin-client
	at com.sun.jmx.mbeanserver.Repository.addMBean(Repository.java:436) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerWithRepository(DefaultMBeanServerInterceptor.java:1865) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerDynamicMBean(DefaultMBeanServerInterceptor.java:960) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerObject(DefaultMBeanServerInterceptor.java:895) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerMBean(DefaultMBeanServerInterceptor.java:320) ~[?:?]
	at com.sun.jmx.mbeanserver.JmxMBeanServer.registerMBean(JmxMBeanServer.java:523) ~[?:?]
	at org.apache.kafka.common.utils.AppInfoParser.registerAppInfo(AppInfoParser.java:64) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.admin.KafkaAdminClient.<init>(KafkaAdminClient.java:602) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.admin.KafkaAdminClient.createInternal(KafkaAdminClient.java:544) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.admin.KafkaAdminClient.createInternal(KafkaAdminClient.java:488) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.admin.Admin.create(Admin.java:134) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.admin.AdminClient.create(AdminClient.java:39) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.flink.connector.kafka.source.enumerator.KafkaSourceEnumerator.getKafkaAdminClient(KafkaSourceEnumerator.java:441) ~[flink-connector-kafka-3.4.0-1.20.jar:3.4.0-1.20]
	at org.apache.flink.connector.kafka.source.enumerator.KafkaSourceEnumerator.start(KafkaSourceEnumerator.java:164) ~[flink-connector-kafka-3.4.0-1.20.jar:3.4.0-1.20]
	at org.apache.flink.runtime.source.coordinator.SourceCoordinator.lambda$start$1(SourceCoordinator.java:244) ~[flink-runtime-1.20.0.jar:1.20.0]
	at org.apache.flink.runtime.source.coordinator.SourceCoordinator.lambda$runInEventLoop$10(SourceCoordinator.java:533) ~[flink-runtime-1.20.0.jar:1.20.0]
	at org.apache.flink.util.ThrowableCatchingRunnable.run(ThrowableCatchingRunnable.java:40) [flink-core-1.20.0.jar:1.20.0]
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:539) [?:?]
	at java.util.concurrent.FutureTask.run(FutureTask.java:264) [?:?]
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:304) [?:?]
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136) [?:?]
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635) [?:?]
	at java.lang.Thread.run(Thread.java:840) [?:?]
2025-08-20 15:18:00.245 [SourceCoordinator-Source: Kafka-交易] WARN  [o.a.k.c.u.AppInfoParser:68] - Error registering AppInfo mbean
javax.management.InstanceAlreadyExistsException: kafka.admin.client:type=app-info,id=orderbook-local-test-group-enumerator-admin-client
	at com.sun.jmx.mbeanserver.Repository.addMBean(Repository.java:436) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerWithRepository(DefaultMBeanServerInterceptor.java:1865) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerDynamicMBean(DefaultMBeanServerInterceptor.java:960) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerObject(DefaultMBeanServerInterceptor.java:895) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerMBean(DefaultMBeanServerInterceptor.java:320) ~[?:?]
	at com.sun.jmx.mbeanserver.JmxMBeanServer.registerMBean(JmxMBeanServer.java:523) ~[?:?]
	at org.apache.kafka.common.utils.AppInfoParser.registerAppInfo(AppInfoParser.java:64) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.admin.KafkaAdminClient.<init>(KafkaAdminClient.java:602) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.admin.KafkaAdminClient.createInternal(KafkaAdminClient.java:544) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.admin.KafkaAdminClient.createInternal(KafkaAdminClient.java:488) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.admin.Admin.create(Admin.java:134) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.admin.AdminClient.create(AdminClient.java:39) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.flink.connector.kafka.source.enumerator.KafkaSourceEnumerator.getKafkaAdminClient(KafkaSourceEnumerator.java:441) ~[flink-connector-kafka-3.4.0-1.20.jar:3.4.0-1.20]
	at org.apache.flink.connector.kafka.source.enumerator.KafkaSourceEnumerator.start(KafkaSourceEnumerator.java:164) ~[flink-connector-kafka-3.4.0-1.20.jar:3.4.0-1.20]
	at org.apache.flink.runtime.source.coordinator.SourceCoordinator.lambda$start$1(SourceCoordinator.java:244) ~[flink-runtime-1.20.0.jar:1.20.0]
	at org.apache.flink.runtime.source.coordinator.SourceCoordinator.lambda$runInEventLoop$10(SourceCoordinator.java:533) ~[flink-runtime-1.20.0.jar:1.20.0]
	at org.apache.flink.util.ThrowableCatchingRunnable.run(ThrowableCatchingRunnable.java:40) [flink-core-1.20.0.jar:1.20.0]
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:539) [?:?]
	at java.util.concurrent.FutureTask.run(FutureTask.java:264) [?:?]
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:304) [?:?]
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136) [?:?]
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635) [?:?]
	at java.lang.Thread.run(Thread.java:840) [?:?]
2025-08-20 15:18:00.398 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] WARN  [o.a.k.c.c.ConsumerConfig:385] - These configurations '[client.id.prefix, partition.discovery.interval.ms]' were supplied but are not used yet.
2025-08-20 15:18:00.398 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] WARN  [o.a.k.c.c.ConsumerConfig:385] - These configurations '[client.id.prefix, partition.discovery.interval.ms]' were supplied but are not used yet.
2025-08-20 15:18:00.398 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] WARN  [o.a.k.c.c.ConsumerConfig:385] - These configurations '[client.id.prefix, partition.discovery.interval.ms]' were supplied but are not used yet.
2025-08-20 15:18:00.399 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] WARN  [o.a.k.c.u.AppInfoParser:68] - Error registering AppInfo mbean
javax.management.InstanceAlreadyExistsException: kafka.consumer:type=app-info,id=orderbook-local-test-group-0
	at com.sun.jmx.mbeanserver.Repository.addMBean(Repository.java:436) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerWithRepository(DefaultMBeanServerInterceptor.java:1865) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerDynamicMBean(DefaultMBeanServerInterceptor.java:960) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerObject(DefaultMBeanServerInterceptor.java:895) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerMBean(DefaultMBeanServerInterceptor.java:320) ~[?:?]
	at com.sun.jmx.mbeanserver.JmxMBeanServer.registerMBean(JmxMBeanServer.java:523) ~[?:?]
	at org.apache.kafka.common.utils.AppInfoParser.registerAppInfo(AppInfoParser.java:64) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.consumer.KafkaConsumer.<init>(KafkaConsumer.java:821) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.consumer.KafkaConsumer.<init>(KafkaConsumer.java:665) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.consumer.KafkaConsumer.<init>(KafkaConsumer.java:646) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.consumer.KafkaConsumer.<init>(KafkaConsumer.java:626) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.flink.connector.kafka.source.reader.KafkaPartitionSplitReader.<init>(KafkaPartitionSplitReader.java:97) ~[flink-connector-kafka-3.4.0-1.20.jar:3.4.0-1.20]
	at org.apache.flink.connector.kafka.source.KafkaSource.lambda$createReader$1(KafkaSource.java:185) ~[flink-connector-kafka-3.4.0-1.20.jar:3.4.0-1.20]
	at org.apache.flink.connector.base.source.reader.fetcher.SplitFetcherManager.createSplitFetcher(SplitFetcherManager.java:259) ~[flink-connector-base-1.20.0.jar:1.20.0]
	at org.apache.flink.connector.base.source.reader.fetcher.SingleThreadFetcherManager.addSplits(SingleThreadFetcherManager.java:148) ~[flink-connector-base-1.20.0.jar:1.20.0]
	at org.apache.flink.connector.base.source.reader.SourceReaderBase.addSplits(SourceReaderBase.java:315) ~[flink-connector-base-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.api.operators.SourceOperator.handleAddSplitsEvent(SourceOperator.java:606) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.api.operators.SourceOperator.handleOperatorEvent(SourceOperator.java:575) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.OperatorEventDispatcherImpl.dispatchEventToHandlers(OperatorEventDispatcherImpl.java:72) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.RegularOperatorChain.dispatchOperatorEvent(RegularOperatorChain.java:80) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.StreamTask.lambda$dispatchOperatorEvent$24(StreamTask.java:1609) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.StreamTaskActionExecutor$1.runThrowing(StreamTaskActionExecutor.java:50) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.mailbox.Mail.run(Mail.java:101) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.mailbox.MailboxProcessor.runMail(MailboxProcessor.java:414) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.mailbox.MailboxProcessor.processMailsWhenDefaultActionUnavailable(MailboxProcessor.java:383) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.mailbox.MailboxProcessor.processMail(MailboxProcessor.java:368) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.mailbox.MailboxProcessor.runMailboxLoop(MailboxProcessor.java:229) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.StreamTask.runMailboxLoop(StreamTask.java:973) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.StreamTask.invoke(StreamTask.java:917) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.runtime.taskmanager.Task.runWithSystemExitMonitoring(Task.java:970) [flink-runtime-1.20.0.jar:1.20.0]
	at org.apache.flink.runtime.taskmanager.Task.restoreAndInvoke(Task.java:949) [flink-runtime-1.20.0.jar:1.20.0]
	at org.apache.flink.runtime.taskmanager.Task.doRun(Task.java:763) [flink-runtime-1.20.0.jar:1.20.0]
	at org.apache.flink.runtime.taskmanager.Task.run(Task.java:575) [flink-runtime-1.20.0.jar:1.20.0]
	at java.lang.Thread.run(Thread.java:840) [?:?]
2025-08-20 15:18:00.399 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] WARN  [o.a.k.c.u.AppInfoParser:68] - Error registering AppInfo mbean
javax.management.InstanceAlreadyExistsException: kafka.consumer:type=app-info,id=orderbook-local-test-group-0
	at com.sun.jmx.mbeanserver.Repository.addMBean(Repository.java:436) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerWithRepository(DefaultMBeanServerInterceptor.java:1865) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerDynamicMBean(DefaultMBeanServerInterceptor.java:960) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerObject(DefaultMBeanServerInterceptor.java:895) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerMBean(DefaultMBeanServerInterceptor.java:320) ~[?:?]
	at com.sun.jmx.mbeanserver.JmxMBeanServer.registerMBean(JmxMBeanServer.java:523) ~[?:?]
	at org.apache.kafka.common.utils.AppInfoParser.registerAppInfo(AppInfoParser.java:64) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.consumer.KafkaConsumer.<init>(KafkaConsumer.java:821) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.consumer.KafkaConsumer.<init>(KafkaConsumer.java:665) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.consumer.KafkaConsumer.<init>(KafkaConsumer.java:646) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.consumer.KafkaConsumer.<init>(KafkaConsumer.java:626) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.flink.connector.kafka.source.reader.KafkaPartitionSplitReader.<init>(KafkaPartitionSplitReader.java:97) ~[flink-connector-kafka-3.4.0-1.20.jar:3.4.0-1.20]
	at org.apache.flink.connector.kafka.source.KafkaSource.lambda$createReader$1(KafkaSource.java:185) ~[flink-connector-kafka-3.4.0-1.20.jar:3.4.0-1.20]
	at org.apache.flink.connector.base.source.reader.fetcher.SplitFetcherManager.createSplitFetcher(SplitFetcherManager.java:259) ~[flink-connector-base-1.20.0.jar:1.20.0]
	at org.apache.flink.connector.base.source.reader.fetcher.SingleThreadFetcherManager.addSplits(SingleThreadFetcherManager.java:148) ~[flink-connector-base-1.20.0.jar:1.20.0]
	at org.apache.flink.connector.base.source.reader.SourceReaderBase.addSplits(SourceReaderBase.java:315) ~[flink-connector-base-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.api.operators.SourceOperator.handleAddSplitsEvent(SourceOperator.java:606) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.api.operators.SourceOperator.handleOperatorEvent(SourceOperator.java:575) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.OperatorEventDispatcherImpl.dispatchEventToHandlers(OperatorEventDispatcherImpl.java:72) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.RegularOperatorChain.dispatchOperatorEvent(RegularOperatorChain.java:80) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.StreamTask.lambda$dispatchOperatorEvent$24(StreamTask.java:1609) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.StreamTaskActionExecutor$1.runThrowing(StreamTaskActionExecutor.java:50) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.mailbox.Mail.run(Mail.java:101) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.mailbox.MailboxProcessor.runMail(MailboxProcessor.java:414) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.mailbox.MailboxProcessor.processMailsWhenDefaultActionUnavailable(MailboxProcessor.java:383) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.mailbox.MailboxProcessor.processMail(MailboxProcessor.java:368) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.mailbox.MailboxProcessor.runMailboxLoop(MailboxProcessor.java:229) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.StreamTask.runMailboxLoop(StreamTask.java:973) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.StreamTask.invoke(StreamTask.java:917) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.runtime.taskmanager.Task.runWithSystemExitMonitoring(Task.java:970) [flink-runtime-1.20.0.jar:1.20.0]
	at org.apache.flink.runtime.taskmanager.Task.restoreAndInvoke(Task.java:949) [flink-runtime-1.20.0.jar:1.20.0]
	at org.apache.flink.runtime.taskmanager.Task.doRun(Task.java:763) [flink-runtime-1.20.0.jar:1.20.0]
	at org.apache.flink.runtime.taskmanager.Task.run(Task.java:575) [flink-runtime-1.20.0.jar:1.20.0]
	at java.lang.Thread.run(Thread.java:840) [?:?]
2025-08-20 15:20:09.304 [main] WARN  [o.a.f.r.s.t.DefaultDelegationTokenManager:248] - No tokens obtained so skipping notifications
2025-08-20 15:20:09.418 [main] WARN  [o.a.f.r.w.WebMonitorUtils:76] - Log file environment variable 'log.file' is not set.
2025-08-20 15:20:09.418 [main] WARN  [o.a.f.r.w.WebMonitorUtils:82] - JobManager log files are unavailable in the web dashboard. Log file location not found in environment variable 'log.file' or configuration key 'web.log.path'.
2025-08-20 15:20:09.482 [flink-pekko.actor.default-dispatcher-4] WARN  [o.a.f.r.s.t.DefaultDelegationTokenManager:320] - No tokens obtained so skipping notifications
2025-08-20 15:20:09.482 [flink-pekko.actor.default-dispatcher-4] WARN  [o.a.f.r.s.t.DefaultDelegationTokenManager:335] - Tokens update task not started because either no tokens obtained or none of the tokens specified its renewal date
2025-08-20 15:20:09.618 [SourceCoordinator-Source: Kafka-单腿订单] WARN  [o.a.k.c.a.AdminClientConfig:385] - These configurations '[key.deserializer, value.deserializer, enable.auto.commit, client.id.prefix, group.id, partition.discovery.interval.ms, auto.offset.reset]' were supplied but are not used yet.
2025-08-20 15:20:09.618 [SourceCoordinator-Source: Kafka-组合订单] WARN  [o.a.k.c.a.AdminClientConfig:385] - These configurations '[key.deserializer, value.deserializer, enable.auto.commit, client.id.prefix, group.id, partition.discovery.interval.ms, auto.offset.reset]' were supplied but are not used yet.
2025-08-20 15:20:09.618 [SourceCoordinator-Source: Kafka-交易] WARN  [o.a.k.c.a.AdminClientConfig:385] - These configurations '[key.deserializer, value.deserializer, enable.auto.commit, client.id.prefix, group.id, partition.discovery.interval.ms, auto.offset.reset]' were supplied but are not used yet.
2025-08-20 15:20:09.620 [SourceCoordinator-Source: Kafka-交易] WARN  [o.a.k.c.u.AppInfoParser:68] - Error registering AppInfo mbean
javax.management.InstanceAlreadyExistsException: kafka.admin.client:type=app-info,id=orderbook-debug-group-enumerator-admin-client
	at com.sun.jmx.mbeanserver.Repository.addMBean(Repository.java:436) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerWithRepository(DefaultMBeanServerInterceptor.java:1865) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerDynamicMBean(DefaultMBeanServerInterceptor.java:960) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerObject(DefaultMBeanServerInterceptor.java:895) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerMBean(DefaultMBeanServerInterceptor.java:320) ~[?:?]
	at com.sun.jmx.mbeanserver.JmxMBeanServer.registerMBean(JmxMBeanServer.java:523) ~[?:?]
	at org.apache.kafka.common.utils.AppInfoParser.registerAppInfo(AppInfoParser.java:64) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.admin.KafkaAdminClient.<init>(KafkaAdminClient.java:602) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.admin.KafkaAdminClient.createInternal(KafkaAdminClient.java:544) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.admin.KafkaAdminClient.createInternal(KafkaAdminClient.java:488) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.admin.Admin.create(Admin.java:134) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.admin.AdminClient.create(AdminClient.java:39) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.flink.connector.kafka.source.enumerator.KafkaSourceEnumerator.getKafkaAdminClient(KafkaSourceEnumerator.java:441) ~[flink-connector-kafka-3.4.0-1.20.jar:3.4.0-1.20]
	at org.apache.flink.connector.kafka.source.enumerator.KafkaSourceEnumerator.start(KafkaSourceEnumerator.java:164) ~[flink-connector-kafka-3.4.0-1.20.jar:3.4.0-1.20]
	at org.apache.flink.runtime.source.coordinator.SourceCoordinator.lambda$start$1(SourceCoordinator.java:244) ~[flink-runtime-1.20.0.jar:1.20.0]
	at org.apache.flink.runtime.source.coordinator.SourceCoordinator.lambda$runInEventLoop$10(SourceCoordinator.java:533) ~[flink-runtime-1.20.0.jar:1.20.0]
	at org.apache.flink.util.ThrowableCatchingRunnable.run(ThrowableCatchingRunnable.java:40) [flink-core-1.20.0.jar:1.20.0]
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:539) [?:?]
	at java.util.concurrent.FutureTask.run(FutureTask.java:264) [?:?]
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:304) [?:?]
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136) [?:?]
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635) [?:?]
	at java.lang.Thread.run(Thread.java:840) [?:?]
2025-08-20 15:20:09.623 [SourceCoordinator-Source: Kafka-组合订单] WARN  [o.a.k.c.u.AppInfoParser:68] - Error registering AppInfo mbean
javax.management.InstanceAlreadyExistsException: kafka.admin.client:type=app-info,id=orderbook-debug-group-enumerator-admin-client
	at com.sun.jmx.mbeanserver.Repository.addMBean(Repository.java:436) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerWithRepository(DefaultMBeanServerInterceptor.java:1865) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerDynamicMBean(DefaultMBeanServerInterceptor.java:960) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerObject(DefaultMBeanServerInterceptor.java:895) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerMBean(DefaultMBeanServerInterceptor.java:320) ~[?:?]
	at com.sun.jmx.mbeanserver.JmxMBeanServer.registerMBean(JmxMBeanServer.java:523) ~[?:?]
	at org.apache.kafka.common.utils.AppInfoParser.registerAppInfo(AppInfoParser.java:64) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.admin.KafkaAdminClient.<init>(KafkaAdminClient.java:602) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.admin.KafkaAdminClient.createInternal(KafkaAdminClient.java:544) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.admin.KafkaAdminClient.createInternal(KafkaAdminClient.java:488) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.admin.Admin.create(Admin.java:134) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.admin.AdminClient.create(AdminClient.java:39) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.flink.connector.kafka.source.enumerator.KafkaSourceEnumerator.getKafkaAdminClient(KafkaSourceEnumerator.java:441) ~[flink-connector-kafka-3.4.0-1.20.jar:3.4.0-1.20]
	at org.apache.flink.connector.kafka.source.enumerator.KafkaSourceEnumerator.start(KafkaSourceEnumerator.java:164) ~[flink-connector-kafka-3.4.0-1.20.jar:3.4.0-1.20]
	at org.apache.flink.runtime.source.coordinator.SourceCoordinator.lambda$start$1(SourceCoordinator.java:244) ~[flink-runtime-1.20.0.jar:1.20.0]
	at org.apache.flink.runtime.source.coordinator.SourceCoordinator.lambda$runInEventLoop$10(SourceCoordinator.java:533) ~[flink-runtime-1.20.0.jar:1.20.0]
	at org.apache.flink.util.ThrowableCatchingRunnable.run(ThrowableCatchingRunnable.java:40) [flink-core-1.20.0.jar:1.20.0]
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:539) [?:?]
	at java.util.concurrent.FutureTask.run(FutureTask.java:264) [?:?]
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:304) [?:?]
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136) [?:?]
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635) [?:?]
	at java.lang.Thread.run(Thread.java:840) [?:?]
2025-08-20 15:20:09.803 [SourceCoordinator-Source: Kafka-组合订单] ERROR [o.a.f.r.s.c.SourceCoordinatorContext:441] - Exception while handling result from async call in SourceCoordinator-Source: Kafka-组合订单. Triggering job failover.
org.apache.flink.util.FlinkRuntimeException: Failed to list subscribed topic partitions due to 
	at org.apache.flink.connector.kafka.source.enumerator.KafkaSourceEnumerator.checkPartitionChanges(KafkaSourceEnumerator.java:248) ~[flink-connector-kafka-3.4.0-1.20.jar:3.4.0-1.20]
	at org.apache.flink.runtime.source.coordinator.ExecutorNotifier.lambda$null$1(ExecutorNotifier.java:83) ~[flink-runtime-1.20.0.jar:1.20.0]
	at org.apache.flink.util.ThrowableCatchingRunnable.run(ThrowableCatchingRunnable.java:40) [flink-core-1.20.0.jar:1.20.0]
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:539) [?:?]
	at java.util.concurrent.FutureTask.run(FutureTask.java:264) [?:?]
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:304) [?:?]
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136) [?:?]
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635) [?:?]
	at java.lang.Thread.run(Thread.java:840) [?:?]
Caused by: java.lang.RuntimeException: Failed to get metadata for topics [combination_orders].
	at org.apache.flink.connector.kafka.source.enumerator.subscriber.KafkaSubscriberUtils.getTopicMetadata(KafkaSubscriberUtils.java:65) ~[flink-connector-kafka-3.4.0-1.20.jar:3.4.0-1.20]
	at org.apache.flink.connector.kafka.source.enumerator.subscriber.TopicListSubscriber.getSubscribedTopicPartitions(TopicListSubscriber.java:56) ~[flink-connector-kafka-3.4.0-1.20.jar:3.4.0-1.20]
	at org.apache.flink.connector.kafka.source.enumerator.KafkaSourceEnumerator.getSubscribedTopicPartitions(KafkaSourceEnumerator.java:233) ~[flink-connector-kafka-3.4.0-1.20.jar:3.4.0-1.20]
	at org.apache.flink.runtime.source.coordinator.ExecutorNotifier.lambda$notifyReadyAsync$2(ExecutorNotifier.java:80) ~[flink-runtime-1.20.0.jar:1.20.0]
	... 6 more
Caused by: java.util.concurrent.ExecutionException: org.apache.kafka.common.errors.UnknownTopicOrPartitionException: This server does not host this topic-partition.
	at java.util.concurrent.CompletableFuture.reportGet(CompletableFuture.java:396) ~[?:?]
	at java.util.concurrent.CompletableFuture.get(CompletableFuture.java:2073) ~[?:?]
	at org.apache.kafka.common.internals.KafkaFutureImpl.get(KafkaFutureImpl.java:165) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.flink.connector.kafka.source.enumerator.subscriber.KafkaSubscriberUtils.getTopicMetadata(KafkaSubscriberUtils.java:62) ~[flink-connector-kafka-3.4.0-1.20.jar:3.4.0-1.20]
	at org.apache.flink.connector.kafka.source.enumerator.subscriber.TopicListSubscriber.getSubscribedTopicPartitions(TopicListSubscriber.java:56) ~[flink-connector-kafka-3.4.0-1.20.jar:3.4.0-1.20]
	at org.apache.flink.connector.kafka.source.enumerator.KafkaSourceEnumerator.getSubscribedTopicPartitions(KafkaSourceEnumerator.java:233) ~[flink-connector-kafka-3.4.0-1.20.jar:3.4.0-1.20]
	at org.apache.flink.runtime.source.coordinator.ExecutorNotifier.lambda$notifyReadyAsync$2(ExecutorNotifier.java:80) ~[flink-runtime-1.20.0.jar:1.20.0]
	... 6 more
Caused by: org.apache.kafka.common.errors.UnknownTopicOrPartitionException: This server does not host this topic-partition.
2025-08-20 15:20:09.803 [SourceCoordinator-Source: Kafka-单腿订单] ERROR [o.a.f.r.s.c.SourceCoordinatorContext:441] - Exception while handling result from async call in SourceCoordinator-Source: Kafka-单腿订单. Triggering job failover.
org.apache.flink.util.FlinkRuntimeException: Failed to list subscribed topic partitions due to 
	at org.apache.flink.connector.kafka.source.enumerator.KafkaSourceEnumerator.checkPartitionChanges(KafkaSourceEnumerator.java:248) ~[flink-connector-kafka-3.4.0-1.20.jar:3.4.0-1.20]
	at org.apache.flink.runtime.source.coordinator.ExecutorNotifier.lambda$null$1(ExecutorNotifier.java:83) ~[flink-runtime-1.20.0.jar:1.20.0]
	at org.apache.flink.util.ThrowableCatchingRunnable.run(ThrowableCatchingRunnable.java:40) [flink-core-1.20.0.jar:1.20.0]
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:539) [?:?]
	at java.util.concurrent.FutureTask.run(FutureTask.java:264) [?:?]
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:304) [?:?]
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136) [?:?]
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635) [?:?]
	at java.lang.Thread.run(Thread.java:840) [?:?]
Caused by: java.lang.RuntimeException: Failed to get metadata for topics [single_leg_orders].
	at org.apache.flink.connector.kafka.source.enumerator.subscriber.KafkaSubscriberUtils.getTopicMetadata(KafkaSubscriberUtils.java:65) ~[flink-connector-kafka-3.4.0-1.20.jar:3.4.0-1.20]
	at org.apache.flink.connector.kafka.source.enumerator.subscriber.TopicListSubscriber.getSubscribedTopicPartitions(TopicListSubscriber.java:56) ~[flink-connector-kafka-3.4.0-1.20.jar:3.4.0-1.20]
	at org.apache.flink.connector.kafka.source.enumerator.KafkaSourceEnumerator.getSubscribedTopicPartitions(KafkaSourceEnumerator.java:233) ~[flink-connector-kafka-3.4.0-1.20.jar:3.4.0-1.20]
	at org.apache.flink.runtime.source.coordinator.ExecutorNotifier.lambda$notifyReadyAsync$2(ExecutorNotifier.java:80) ~[flink-runtime-1.20.0.jar:1.20.0]
	... 6 more
Caused by: java.util.concurrent.ExecutionException: org.apache.kafka.common.errors.UnknownTopicOrPartitionException: This server does not host this topic-partition.
	at java.util.concurrent.CompletableFuture.reportGet(CompletableFuture.java:396) ~[?:?]
	at java.util.concurrent.CompletableFuture.get(CompletableFuture.java:2073) ~[?:?]
	at org.apache.kafka.common.internals.KafkaFutureImpl.get(KafkaFutureImpl.java:165) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.flink.connector.kafka.source.enumerator.subscriber.KafkaSubscriberUtils.getTopicMetadata(KafkaSubscriberUtils.java:62) ~[flink-connector-kafka-3.4.0-1.20.jar:3.4.0-1.20]
	at org.apache.flink.connector.kafka.source.enumerator.subscriber.TopicListSubscriber.getSubscribedTopicPartitions(TopicListSubscriber.java:56) ~[flink-connector-kafka-3.4.0-1.20.jar:3.4.0-1.20]
	at org.apache.flink.connector.kafka.source.enumerator.KafkaSourceEnumerator.getSubscribedTopicPartitions(KafkaSourceEnumerator.java:233) ~[flink-connector-kafka-3.4.0-1.20.jar:3.4.0-1.20]
	at org.apache.flink.runtime.source.coordinator.ExecutorNotifier.lambda$notifyReadyAsync$2(ExecutorNotifier.java:80) ~[flink-runtime-1.20.0.jar:1.20.0]
	... 6 more
Caused by: org.apache.kafka.common.errors.UnknownTopicOrPartitionException: This server does not host this topic-partition.
2025-08-20 15:20:09.803 [SourceCoordinator-Source: Kafka-交易] ERROR [o.a.f.r.s.c.SourceCoordinatorContext:441] - Exception while handling result from async call in SourceCoordinator-Source: Kafka-交易. Triggering job failover.
org.apache.flink.util.FlinkRuntimeException: Failed to list subscribed topic partitions due to 
	at org.apache.flink.connector.kafka.source.enumerator.KafkaSourceEnumerator.checkPartitionChanges(KafkaSourceEnumerator.java:248) ~[flink-connector-kafka-3.4.0-1.20.jar:3.4.0-1.20]
	at org.apache.flink.runtime.source.coordinator.ExecutorNotifier.lambda$null$1(ExecutorNotifier.java:83) ~[flink-runtime-1.20.0.jar:1.20.0]
	at org.apache.flink.util.ThrowableCatchingRunnable.run(ThrowableCatchingRunnable.java:40) [flink-core-1.20.0.jar:1.20.0]
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:539) [?:?]
	at java.util.concurrent.FutureTask.run(FutureTask.java:264) [?:?]
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:304) [?:?]
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136) [?:?]
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635) [?:?]
	at java.lang.Thread.run(Thread.java:840) [?:?]
Caused by: java.lang.RuntimeException: Failed to get metadata for topics [trades].
	at org.apache.flink.connector.kafka.source.enumerator.subscriber.KafkaSubscriberUtils.getTopicMetadata(KafkaSubscriberUtils.java:65) ~[flink-connector-kafka-3.4.0-1.20.jar:3.4.0-1.20]
	at org.apache.flink.connector.kafka.source.enumerator.subscriber.TopicListSubscriber.getSubscribedTopicPartitions(TopicListSubscriber.java:56) ~[flink-connector-kafka-3.4.0-1.20.jar:3.4.0-1.20]
	at org.apache.flink.connector.kafka.source.enumerator.KafkaSourceEnumerator.getSubscribedTopicPartitions(KafkaSourceEnumerator.java:233) ~[flink-connector-kafka-3.4.0-1.20.jar:3.4.0-1.20]
	at org.apache.flink.runtime.source.coordinator.ExecutorNotifier.lambda$notifyReadyAsync$2(ExecutorNotifier.java:80) ~[flink-runtime-1.20.0.jar:1.20.0]
	... 6 more
Caused by: java.util.concurrent.ExecutionException: org.apache.kafka.common.errors.UnknownTopicOrPartitionException: This server does not host this topic-partition.
	at java.util.concurrent.CompletableFuture.reportGet(CompletableFuture.java:396) ~[?:?]
	at java.util.concurrent.CompletableFuture.get(CompletableFuture.java:2073) ~[?:?]
	at org.apache.kafka.common.internals.KafkaFutureImpl.get(KafkaFutureImpl.java:165) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.flink.connector.kafka.source.enumerator.subscriber.KafkaSubscriberUtils.getTopicMetadata(KafkaSubscriberUtils.java:62) ~[flink-connector-kafka-3.4.0-1.20.jar:3.4.0-1.20]
	at org.apache.flink.connector.kafka.source.enumerator.subscriber.TopicListSubscriber.getSubscribedTopicPartitions(TopicListSubscriber.java:56) ~[flink-connector-kafka-3.4.0-1.20.jar:3.4.0-1.20]
	at org.apache.flink.connector.kafka.source.enumerator.KafkaSourceEnumerator.getSubscribedTopicPartitions(KafkaSourceEnumerator.java:233) ~[flink-connector-kafka-3.4.0-1.20.jar:3.4.0-1.20]
	at org.apache.flink.runtime.source.coordinator.ExecutorNotifier.lambda$notifyReadyAsync$2(ExecutorNotifier.java:80) ~[flink-runtime-1.20.0.jar:1.20.0]
	... 6 more
Caused by: org.apache.kafka.common.errors.UnknownTopicOrPartitionException: This server does not host this topic-partition.
2025-08-20 15:24:09.241 [main] WARN  [o.a.f.r.s.t.DefaultDelegationTokenManager:248] - No tokens obtained so skipping notifications
2025-08-20 15:24:09.362 [main] WARN  [o.a.f.r.w.WebMonitorUtils:76] - Log file environment variable 'log.file' is not set.
2025-08-20 15:24:09.362 [main] WARN  [o.a.f.r.w.WebMonitorUtils:82] - JobManager log files are unavailable in the web dashboard. Log file location not found in environment variable 'log.file' or configuration key 'web.log.path'.
2025-08-20 15:24:09.421 [flink-pekko.actor.default-dispatcher-4] WARN  [o.a.f.r.s.t.DefaultDelegationTokenManager:320] - No tokens obtained so skipping notifications
2025-08-20 15:24:09.421 [flink-pekko.actor.default-dispatcher-4] WARN  [o.a.f.r.s.t.DefaultDelegationTokenManager:335] - Tokens update task not started because either no tokens obtained or none of the tokens specified its renewal date
2025-08-20 15:24:09.526 [SourceCoordinator-Source: Kafka-组合订单] WARN  [o.a.k.c.a.AdminClientConfig:385] - These configurations '[key.deserializer, value.deserializer, enable.auto.commit, client.id.prefix, group.id, partition.discovery.interval.ms, auto.offset.reset]' were supplied but are not used yet.
2025-08-20 15:24:09.526 [SourceCoordinator-Source: Kafka-单腿订单] WARN  [o.a.k.c.a.AdminClientConfig:385] - These configurations '[key.deserializer, value.deserializer, enable.auto.commit, client.id.prefix, group.id, partition.discovery.interval.ms, auto.offset.reset]' were supplied but are not used yet.
2025-08-20 15:24:09.526 [SourceCoordinator-Source: Kafka-交易] WARN  [o.a.k.c.a.AdminClientConfig:385] - These configurations '[key.deserializer, value.deserializer, enable.auto.commit, client.id.prefix, group.id, partition.discovery.interval.ms, auto.offset.reset]' were supplied but are not used yet.
2025-08-20 15:24:09.528 [SourceCoordinator-Source: Kafka-交易] WARN  [o.a.k.c.u.AppInfoParser:68] - Error registering AppInfo mbean
javax.management.InstanceAlreadyExistsException: kafka.admin.client:type=app-info,id=orderbook-debug-group-enumerator-admin-client
	at com.sun.jmx.mbeanserver.Repository.addMBean(Repository.java:436) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerWithRepository(DefaultMBeanServerInterceptor.java:1865) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerDynamicMBean(DefaultMBeanServerInterceptor.java:960) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerObject(DefaultMBeanServerInterceptor.java:895) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerMBean(DefaultMBeanServerInterceptor.java:320) ~[?:?]
	at com.sun.jmx.mbeanserver.JmxMBeanServer.registerMBean(JmxMBeanServer.java:523) ~[?:?]
	at org.apache.kafka.common.utils.AppInfoParser.registerAppInfo(AppInfoParser.java:64) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.admin.KafkaAdminClient.<init>(KafkaAdminClient.java:602) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.admin.KafkaAdminClient.createInternal(KafkaAdminClient.java:544) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.admin.KafkaAdminClient.createInternal(KafkaAdminClient.java:488) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.admin.Admin.create(Admin.java:134) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.admin.AdminClient.create(AdminClient.java:39) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.flink.connector.kafka.source.enumerator.KafkaSourceEnumerator.getKafkaAdminClient(KafkaSourceEnumerator.java:441) ~[flink-connector-kafka-3.4.0-1.20.jar:3.4.0-1.20]
	at org.apache.flink.connector.kafka.source.enumerator.KafkaSourceEnumerator.start(KafkaSourceEnumerator.java:164) ~[flink-connector-kafka-3.4.0-1.20.jar:3.4.0-1.20]
	at org.apache.flink.runtime.source.coordinator.SourceCoordinator.lambda$start$1(SourceCoordinator.java:244) ~[flink-runtime-1.20.0.jar:1.20.0]
	at org.apache.flink.runtime.source.coordinator.SourceCoordinator.lambda$runInEventLoop$10(SourceCoordinator.java:533) ~[flink-runtime-1.20.0.jar:1.20.0]
	at org.apache.flink.util.ThrowableCatchingRunnable.run(ThrowableCatchingRunnable.java:40) [flink-core-1.20.0.jar:1.20.0]
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:539) [?:?]
	at java.util.concurrent.FutureTask.run(FutureTask.java:264) [?:?]
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:304) [?:?]
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136) [?:?]
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635) [?:?]
	at java.lang.Thread.run(Thread.java:840) [?:?]
2025-08-20 15:24:09.530 [SourceCoordinator-Source: Kafka-单腿订单] WARN  [o.a.k.c.u.AppInfoParser:68] - Error registering AppInfo mbean
javax.management.InstanceAlreadyExistsException: kafka.admin.client:type=app-info,id=orderbook-debug-group-enumerator-admin-client
	at com.sun.jmx.mbeanserver.Repository.addMBean(Repository.java:436) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerWithRepository(DefaultMBeanServerInterceptor.java:1865) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerDynamicMBean(DefaultMBeanServerInterceptor.java:960) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerObject(DefaultMBeanServerInterceptor.java:895) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerMBean(DefaultMBeanServerInterceptor.java:320) ~[?:?]
	at com.sun.jmx.mbeanserver.JmxMBeanServer.registerMBean(JmxMBeanServer.java:523) ~[?:?]
	at org.apache.kafka.common.utils.AppInfoParser.registerAppInfo(AppInfoParser.java:64) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.admin.KafkaAdminClient.<init>(KafkaAdminClient.java:602) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.admin.KafkaAdminClient.createInternal(KafkaAdminClient.java:544) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.admin.KafkaAdminClient.createInternal(KafkaAdminClient.java:488) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.admin.Admin.create(Admin.java:134) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.admin.AdminClient.create(AdminClient.java:39) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.flink.connector.kafka.source.enumerator.KafkaSourceEnumerator.getKafkaAdminClient(KafkaSourceEnumerator.java:441) ~[flink-connector-kafka-3.4.0-1.20.jar:3.4.0-1.20]
	at org.apache.flink.connector.kafka.source.enumerator.KafkaSourceEnumerator.start(KafkaSourceEnumerator.java:164) ~[flink-connector-kafka-3.4.0-1.20.jar:3.4.0-1.20]
	at org.apache.flink.runtime.source.coordinator.SourceCoordinator.lambda$start$1(SourceCoordinator.java:244) ~[flink-runtime-1.20.0.jar:1.20.0]
	at org.apache.flink.runtime.source.coordinator.SourceCoordinator.lambda$runInEventLoop$10(SourceCoordinator.java:533) ~[flink-runtime-1.20.0.jar:1.20.0]
	at org.apache.flink.util.ThrowableCatchingRunnable.run(ThrowableCatchingRunnable.java:40) [flink-core-1.20.0.jar:1.20.0]
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:539) [?:?]
	at java.util.concurrent.FutureTask.run(FutureTask.java:264) [?:?]
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:304) [?:?]
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136) [?:?]
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635) [?:?]
	at java.lang.Thread.run(Thread.java:840) [?:?]
2025-08-20 15:24:09.680 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] WARN  [o.a.k.c.c.ConsumerConfig:385] - These configurations '[client.id.prefix, partition.discovery.interval.ms]' were supplied but are not used yet.
2025-08-20 15:24:09.679 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> 拆分组合订单 -> (Map, Map) (1/1)#0] WARN  [o.a.k.c.c.ConsumerConfig:385] - These configurations '[client.id.prefix, partition.discovery.interval.ms]' were supplied but are not used yet.
2025-08-20 15:24:09.679 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] WARN  [o.a.k.c.c.ConsumerConfig:385] - These configurations '[client.id.prefix, partition.discovery.interval.ms]' were supplied but are not used yet.
2025-08-20 15:24:09.680 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] WARN  [o.a.k.c.u.AppInfoParser:68] - Error registering AppInfo mbean
javax.management.InstanceAlreadyExistsException: kafka.consumer:type=app-info,id=orderbook-debug-group-0
	at com.sun.jmx.mbeanserver.Repository.addMBean(Repository.java:436) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerWithRepository(DefaultMBeanServerInterceptor.java:1865) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerDynamicMBean(DefaultMBeanServerInterceptor.java:960) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerObject(DefaultMBeanServerInterceptor.java:895) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerMBean(DefaultMBeanServerInterceptor.java:320) ~[?:?]
	at com.sun.jmx.mbeanserver.JmxMBeanServer.registerMBean(JmxMBeanServer.java:523) ~[?:?]
	at org.apache.kafka.common.utils.AppInfoParser.registerAppInfo(AppInfoParser.java:64) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.consumer.KafkaConsumer.<init>(KafkaConsumer.java:821) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.consumer.KafkaConsumer.<init>(KafkaConsumer.java:665) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.consumer.KafkaConsumer.<init>(KafkaConsumer.java:646) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.consumer.KafkaConsumer.<init>(KafkaConsumer.java:626) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.flink.connector.kafka.source.reader.KafkaPartitionSplitReader.<init>(KafkaPartitionSplitReader.java:97) ~[flink-connector-kafka-3.4.0-1.20.jar:3.4.0-1.20]
	at org.apache.flink.connector.kafka.source.KafkaSource.lambda$createReader$1(KafkaSource.java:185) ~[flink-connector-kafka-3.4.0-1.20.jar:3.4.0-1.20]
	at org.apache.flink.connector.base.source.reader.fetcher.SplitFetcherManager.createSplitFetcher(SplitFetcherManager.java:259) ~[flink-connector-base-1.20.0.jar:1.20.0]
	at org.apache.flink.connector.base.source.reader.fetcher.SingleThreadFetcherManager.addSplits(SingleThreadFetcherManager.java:148) ~[flink-connector-base-1.20.0.jar:1.20.0]
	at org.apache.flink.connector.base.source.reader.SourceReaderBase.addSplits(SourceReaderBase.java:315) ~[flink-connector-base-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.api.operators.SourceOperator.handleAddSplitsEvent(SourceOperator.java:606) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.api.operators.SourceOperator.handleOperatorEvent(SourceOperator.java:575) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.OperatorEventDispatcherImpl.dispatchEventToHandlers(OperatorEventDispatcherImpl.java:72) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.RegularOperatorChain.dispatchOperatorEvent(RegularOperatorChain.java:80) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.StreamTask.lambda$dispatchOperatorEvent$24(StreamTask.java:1609) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.StreamTaskActionExecutor$1.runThrowing(StreamTaskActionExecutor.java:50) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.mailbox.Mail.run(Mail.java:101) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.mailbox.MailboxProcessor.runMail(MailboxProcessor.java:414) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.mailbox.MailboxProcessor.processMailsNonBlocking(MailboxProcessor.java:399) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.mailbox.MailboxProcessor.processMail(MailboxProcessor.java:361) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.mailbox.MailboxProcessor.runMailboxLoop(MailboxProcessor.java:229) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.StreamTask.runMailboxLoop(StreamTask.java:973) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.StreamTask.invoke(StreamTask.java:917) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.runtime.taskmanager.Task.runWithSystemExitMonitoring(Task.java:970) [flink-runtime-1.20.0.jar:1.20.0]
	at org.apache.flink.runtime.taskmanager.Task.restoreAndInvoke(Task.java:949) [flink-runtime-1.20.0.jar:1.20.0]
	at org.apache.flink.runtime.taskmanager.Task.doRun(Task.java:763) [flink-runtime-1.20.0.jar:1.20.0]
	at org.apache.flink.runtime.taskmanager.Task.run(Task.java:575) [flink-runtime-1.20.0.jar:1.20.0]
	at java.lang.Thread.run(Thread.java:840) [?:?]
2025-08-20 15:24:09.680 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> 拆分组合订单 -> (Map, Map) (1/1)#0] WARN  [o.a.k.c.u.AppInfoParser:68] - Error registering AppInfo mbean
javax.management.InstanceAlreadyExistsException: kafka.consumer:type=app-info,id=orderbook-debug-group-0
	at com.sun.jmx.mbeanserver.Repository.addMBean(Repository.java:436) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerWithRepository(DefaultMBeanServerInterceptor.java:1865) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerDynamicMBean(DefaultMBeanServerInterceptor.java:960) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerObject(DefaultMBeanServerInterceptor.java:895) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerMBean(DefaultMBeanServerInterceptor.java:320) ~[?:?]
	at com.sun.jmx.mbeanserver.JmxMBeanServer.registerMBean(JmxMBeanServer.java:523) ~[?:?]
	at org.apache.kafka.common.utils.AppInfoParser.registerAppInfo(AppInfoParser.java:64) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.consumer.KafkaConsumer.<init>(KafkaConsumer.java:821) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.consumer.KafkaConsumer.<init>(KafkaConsumer.java:665) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.consumer.KafkaConsumer.<init>(KafkaConsumer.java:646) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.consumer.KafkaConsumer.<init>(KafkaConsumer.java:626) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.flink.connector.kafka.source.reader.KafkaPartitionSplitReader.<init>(KafkaPartitionSplitReader.java:97) ~[flink-connector-kafka-3.4.0-1.20.jar:3.4.0-1.20]
	at org.apache.flink.connector.kafka.source.KafkaSource.lambda$createReader$1(KafkaSource.java:185) ~[flink-connector-kafka-3.4.0-1.20.jar:3.4.0-1.20]
	at org.apache.flink.connector.base.source.reader.fetcher.SplitFetcherManager.createSplitFetcher(SplitFetcherManager.java:259) ~[flink-connector-base-1.20.0.jar:1.20.0]
	at org.apache.flink.connector.base.source.reader.fetcher.SingleThreadFetcherManager.addSplits(SingleThreadFetcherManager.java:148) ~[flink-connector-base-1.20.0.jar:1.20.0]
	at org.apache.flink.connector.base.source.reader.SourceReaderBase.addSplits(SourceReaderBase.java:315) ~[flink-connector-base-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.api.operators.SourceOperator.handleAddSplitsEvent(SourceOperator.java:606) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.api.operators.SourceOperator.handleOperatorEvent(SourceOperator.java:575) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.OperatorEventDispatcherImpl.dispatchEventToHandlers(OperatorEventDispatcherImpl.java:72) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.RegularOperatorChain.dispatchOperatorEvent(RegularOperatorChain.java:80) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.StreamTask.lambda$dispatchOperatorEvent$24(StreamTask.java:1609) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.StreamTaskActionExecutor$1.runThrowing(StreamTaskActionExecutor.java:50) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.mailbox.Mail.run(Mail.java:101) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.mailbox.MailboxProcessor.runMail(MailboxProcessor.java:414) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.mailbox.MailboxProcessor.processMailsNonBlocking(MailboxProcessor.java:399) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.mailbox.MailboxProcessor.processMail(MailboxProcessor.java:361) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.mailbox.MailboxProcessor.runMailboxLoop(MailboxProcessor.java:229) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.StreamTask.runMailboxLoop(StreamTask.java:973) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.StreamTask.invoke(StreamTask.java:917) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.runtime.taskmanager.Task.runWithSystemExitMonitoring(Task.java:970) [flink-runtime-1.20.0.jar:1.20.0]
	at org.apache.flink.runtime.taskmanager.Task.restoreAndInvoke(Task.java:949) [flink-runtime-1.20.0.jar:1.20.0]
	at org.apache.flink.runtime.taskmanager.Task.doRun(Task.java:763) [flink-runtime-1.20.0.jar:1.20.0]
	at org.apache.flink.runtime.taskmanager.Task.run(Task.java:575) [flink-runtime-1.20.0.jar:1.20.0]
	at java.lang.Thread.run(Thread.java:840) [?:?]
2025-08-20 15:27:42.830 [main] WARN  [o.a.f.r.s.t.DefaultDelegationTokenManager:248] - No tokens obtained so skipping notifications
2025-08-20 15:27:42.931 [main] WARN  [o.a.f.r.w.WebMonitorUtils:76] - Log file environment variable 'log.file' is not set.
2025-08-20 15:27:42.931 [main] WARN  [o.a.f.r.w.WebMonitorUtils:82] - JobManager log files are unavailable in the web dashboard. Log file location not found in environment variable 'log.file' or configuration key 'web.log.path'.
2025-08-20 15:27:42.988 [flink-pekko.actor.default-dispatcher-4] WARN  [o.a.f.r.s.t.DefaultDelegationTokenManager:320] - No tokens obtained so skipping notifications
2025-08-20 15:27:42.988 [flink-pekko.actor.default-dispatcher-4] WARN  [o.a.f.r.s.t.DefaultDelegationTokenManager:335] - Tokens update task not started because either no tokens obtained or none of the tokens specified its renewal date
2025-08-20 15:27:43.241 [SourceCoordinator-Source: Kafka-组合订单] WARN  [o.a.k.c.a.AdminClientConfig:385] - These configurations '[key.deserializer, value.deserializer, enable.auto.commit, client.id.prefix, group.id, partition.discovery.interval.ms, auto.offset.reset]' were supplied but are not used yet.
2025-08-20 15:27:43.241 [SourceCoordinator-Source: Kafka-交易] WARN  [o.a.k.c.a.AdminClientConfig:385] - These configurations '[key.deserializer, value.deserializer, enable.auto.commit, client.id.prefix, group.id, partition.discovery.interval.ms, auto.offset.reset]' were supplied but are not used yet.
2025-08-20 15:27:43.241 [SourceCoordinator-Source: Kafka-单腿订单] WARN  [o.a.k.c.a.AdminClientConfig:385] - These configurations '[key.deserializer, value.deserializer, enable.auto.commit, client.id.prefix, group.id, partition.discovery.interval.ms, auto.offset.reset]' were supplied but are not used yet.
2025-08-20 15:27:43.243 [SourceCoordinator-Source: Kafka-单腿订单] WARN  [o.a.k.c.u.AppInfoParser:68] - Error registering AppInfo mbean
javax.management.InstanceAlreadyExistsException: kafka.admin.client:type=app-info,id=orderbook-debug-group-enumerator-admin-client
	at com.sun.jmx.mbeanserver.Repository.addMBean(Repository.java:436) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerWithRepository(DefaultMBeanServerInterceptor.java:1865) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerDynamicMBean(DefaultMBeanServerInterceptor.java:960) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerObject(DefaultMBeanServerInterceptor.java:895) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerMBean(DefaultMBeanServerInterceptor.java:320) ~[?:?]
	at com.sun.jmx.mbeanserver.JmxMBeanServer.registerMBean(JmxMBeanServer.java:523) ~[?:?]
	at org.apache.kafka.common.utils.AppInfoParser.registerAppInfo(AppInfoParser.java:64) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.admin.KafkaAdminClient.<init>(KafkaAdminClient.java:602) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.admin.KafkaAdminClient.createInternal(KafkaAdminClient.java:544) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.admin.KafkaAdminClient.createInternal(KafkaAdminClient.java:488) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.admin.Admin.create(Admin.java:134) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.admin.AdminClient.create(AdminClient.java:39) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.flink.connector.kafka.source.enumerator.KafkaSourceEnumerator.getKafkaAdminClient(KafkaSourceEnumerator.java:441) ~[flink-connector-kafka-3.4.0-1.20.jar:3.4.0-1.20]
	at org.apache.flink.connector.kafka.source.enumerator.KafkaSourceEnumerator.start(KafkaSourceEnumerator.java:164) ~[flink-connector-kafka-3.4.0-1.20.jar:3.4.0-1.20]
	at org.apache.flink.runtime.source.coordinator.SourceCoordinator.lambda$start$1(SourceCoordinator.java:244) ~[flink-runtime-1.20.0.jar:1.20.0]
	at org.apache.flink.runtime.source.coordinator.SourceCoordinator.lambda$runInEventLoop$10(SourceCoordinator.java:533) ~[flink-runtime-1.20.0.jar:1.20.0]
	at org.apache.flink.util.ThrowableCatchingRunnable.run(ThrowableCatchingRunnable.java:40) [flink-core-1.20.0.jar:1.20.0]
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:539) [?:?]
	at java.util.concurrent.FutureTask.run(FutureTask.java:264) [?:?]
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:304) [?:?]
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136) [?:?]
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635) [?:?]
	at java.lang.Thread.run(Thread.java:840) [?:?]
2025-08-20 15:27:43.245 [SourceCoordinator-Source: Kafka-交易] WARN  [o.a.k.c.u.AppInfoParser:68] - Error registering AppInfo mbean
javax.management.InstanceAlreadyExistsException: kafka.admin.client:type=app-info,id=orderbook-debug-group-enumerator-admin-client
	at com.sun.jmx.mbeanserver.Repository.addMBean(Repository.java:436) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerWithRepository(DefaultMBeanServerInterceptor.java:1865) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerDynamicMBean(DefaultMBeanServerInterceptor.java:960) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerObject(DefaultMBeanServerInterceptor.java:895) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerMBean(DefaultMBeanServerInterceptor.java:320) ~[?:?]
	at com.sun.jmx.mbeanserver.JmxMBeanServer.registerMBean(JmxMBeanServer.java:523) ~[?:?]
	at org.apache.kafka.common.utils.AppInfoParser.registerAppInfo(AppInfoParser.java:64) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.admin.KafkaAdminClient.<init>(KafkaAdminClient.java:602) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.admin.KafkaAdminClient.createInternal(KafkaAdminClient.java:544) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.admin.KafkaAdminClient.createInternal(KafkaAdminClient.java:488) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.admin.Admin.create(Admin.java:134) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.admin.AdminClient.create(AdminClient.java:39) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.flink.connector.kafka.source.enumerator.KafkaSourceEnumerator.getKafkaAdminClient(KafkaSourceEnumerator.java:441) ~[flink-connector-kafka-3.4.0-1.20.jar:3.4.0-1.20]
	at org.apache.flink.connector.kafka.source.enumerator.KafkaSourceEnumerator.start(KafkaSourceEnumerator.java:164) ~[flink-connector-kafka-3.4.0-1.20.jar:3.4.0-1.20]
	at org.apache.flink.runtime.source.coordinator.SourceCoordinator.lambda$start$1(SourceCoordinator.java:244) ~[flink-runtime-1.20.0.jar:1.20.0]
	at org.apache.flink.runtime.source.coordinator.SourceCoordinator.lambda$runInEventLoop$10(SourceCoordinator.java:533) ~[flink-runtime-1.20.0.jar:1.20.0]
	at org.apache.flink.util.ThrowableCatchingRunnable.run(ThrowableCatchingRunnable.java:40) [flink-core-1.20.0.jar:1.20.0]
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:539) [?:?]
	at java.util.concurrent.FutureTask.run(FutureTask.java:264) [?:?]
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:304) [?:?]
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136) [?:?]
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635) [?:?]
	at java.lang.Thread.run(Thread.java:840) [?:?]
2025-08-20 15:27:43.397 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> 拆分组合订单 -> (Map, Map) (1/1)#0] WARN  [o.a.k.c.c.ConsumerConfig:385] - These configurations '[client.id.prefix, partition.discovery.interval.ms]' were supplied but are not used yet.
2025-08-20 15:27:43.397 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] WARN  [o.a.k.c.c.ConsumerConfig:385] - These configurations '[client.id.prefix, partition.discovery.interval.ms]' were supplied but are not used yet.
2025-08-20 15:27:43.397 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] WARN  [o.a.k.c.c.ConsumerConfig:385] - These configurations '[client.id.prefix, partition.discovery.interval.ms]' were supplied but are not used yet.
2025-08-20 15:27:43.397 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] WARN  [o.a.k.c.u.AppInfoParser:68] - Error registering AppInfo mbean
javax.management.InstanceAlreadyExistsException: kafka.consumer:type=app-info,id=orderbook-debug-group-0
	at com.sun.jmx.mbeanserver.Repository.addMBean(Repository.java:436) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerWithRepository(DefaultMBeanServerInterceptor.java:1865) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerDynamicMBean(DefaultMBeanServerInterceptor.java:960) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerObject(DefaultMBeanServerInterceptor.java:895) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerMBean(DefaultMBeanServerInterceptor.java:320) ~[?:?]
	at com.sun.jmx.mbeanserver.JmxMBeanServer.registerMBean(JmxMBeanServer.java:523) ~[?:?]
	at org.apache.kafka.common.utils.AppInfoParser.registerAppInfo(AppInfoParser.java:64) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.consumer.KafkaConsumer.<init>(KafkaConsumer.java:821) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.consumer.KafkaConsumer.<init>(KafkaConsumer.java:665) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.consumer.KafkaConsumer.<init>(KafkaConsumer.java:646) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.consumer.KafkaConsumer.<init>(KafkaConsumer.java:626) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.flink.connector.kafka.source.reader.KafkaPartitionSplitReader.<init>(KafkaPartitionSplitReader.java:97) ~[flink-connector-kafka-3.4.0-1.20.jar:3.4.0-1.20]
	at org.apache.flink.connector.kafka.source.KafkaSource.lambda$createReader$1(KafkaSource.java:185) ~[flink-connector-kafka-3.4.0-1.20.jar:3.4.0-1.20]
	at org.apache.flink.connector.base.source.reader.fetcher.SplitFetcherManager.createSplitFetcher(SplitFetcherManager.java:259) ~[flink-connector-base-1.20.0.jar:1.20.0]
	at org.apache.flink.connector.base.source.reader.fetcher.SingleThreadFetcherManager.addSplits(SingleThreadFetcherManager.java:148) ~[flink-connector-base-1.20.0.jar:1.20.0]
	at org.apache.flink.connector.base.source.reader.SourceReaderBase.addSplits(SourceReaderBase.java:315) ~[flink-connector-base-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.api.operators.SourceOperator.handleAddSplitsEvent(SourceOperator.java:606) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.api.operators.SourceOperator.handleOperatorEvent(SourceOperator.java:575) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.OperatorEventDispatcherImpl.dispatchEventToHandlers(OperatorEventDispatcherImpl.java:72) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.RegularOperatorChain.dispatchOperatorEvent(RegularOperatorChain.java:80) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.StreamTask.lambda$dispatchOperatorEvent$24(StreamTask.java:1609) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.StreamTaskActionExecutor$1.runThrowing(StreamTaskActionExecutor.java:50) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.mailbox.Mail.run(Mail.java:101) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.mailbox.MailboxProcessor.runMail(MailboxProcessor.java:414) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.mailbox.MailboxProcessor.processMailsWhenDefaultActionUnavailable(MailboxProcessor.java:383) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.mailbox.MailboxProcessor.processMail(MailboxProcessor.java:368) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.mailbox.MailboxProcessor.runMailboxLoop(MailboxProcessor.java:229) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.StreamTask.runMailboxLoop(StreamTask.java:973) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.StreamTask.invoke(StreamTask.java:917) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.runtime.taskmanager.Task.runWithSystemExitMonitoring(Task.java:970) [flink-runtime-1.20.0.jar:1.20.0]
	at org.apache.flink.runtime.taskmanager.Task.restoreAndInvoke(Task.java:949) [flink-runtime-1.20.0.jar:1.20.0]
	at org.apache.flink.runtime.taskmanager.Task.doRun(Task.java:763) [flink-runtime-1.20.0.jar:1.20.0]
	at org.apache.flink.runtime.taskmanager.Task.run(Task.java:575) [flink-runtime-1.20.0.jar:1.20.0]
	at java.lang.Thread.run(Thread.java:840) [?:?]
2025-08-20 15:27:43.398 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] WARN  [o.a.k.c.u.AppInfoParser:68] - Error registering AppInfo mbean
javax.management.InstanceAlreadyExistsException: kafka.consumer:type=app-info,id=orderbook-debug-group-0
	at com.sun.jmx.mbeanserver.Repository.addMBean(Repository.java:436) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerWithRepository(DefaultMBeanServerInterceptor.java:1865) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerDynamicMBean(DefaultMBeanServerInterceptor.java:960) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerObject(DefaultMBeanServerInterceptor.java:895) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerMBean(DefaultMBeanServerInterceptor.java:320) ~[?:?]
	at com.sun.jmx.mbeanserver.JmxMBeanServer.registerMBean(JmxMBeanServer.java:523) ~[?:?]
	at org.apache.kafka.common.utils.AppInfoParser.registerAppInfo(AppInfoParser.java:64) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.consumer.KafkaConsumer.<init>(KafkaConsumer.java:821) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.consumer.KafkaConsumer.<init>(KafkaConsumer.java:665) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.consumer.KafkaConsumer.<init>(KafkaConsumer.java:646) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.consumer.KafkaConsumer.<init>(KafkaConsumer.java:626) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.flink.connector.kafka.source.reader.KafkaPartitionSplitReader.<init>(KafkaPartitionSplitReader.java:97) ~[flink-connector-kafka-3.4.0-1.20.jar:3.4.0-1.20]
	at org.apache.flink.connector.kafka.source.KafkaSource.lambda$createReader$1(KafkaSource.java:185) ~[flink-connector-kafka-3.4.0-1.20.jar:3.4.0-1.20]
	at org.apache.flink.connector.base.source.reader.fetcher.SplitFetcherManager.createSplitFetcher(SplitFetcherManager.java:259) ~[flink-connector-base-1.20.0.jar:1.20.0]
	at org.apache.flink.connector.base.source.reader.fetcher.SingleThreadFetcherManager.addSplits(SingleThreadFetcherManager.java:148) ~[flink-connector-base-1.20.0.jar:1.20.0]
	at org.apache.flink.connector.base.source.reader.SourceReaderBase.addSplits(SourceReaderBase.java:315) ~[flink-connector-base-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.api.operators.SourceOperator.handleAddSplitsEvent(SourceOperator.java:606) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.api.operators.SourceOperator.handleOperatorEvent(SourceOperator.java:575) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.OperatorEventDispatcherImpl.dispatchEventToHandlers(OperatorEventDispatcherImpl.java:72) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.RegularOperatorChain.dispatchOperatorEvent(RegularOperatorChain.java:80) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.StreamTask.lambda$dispatchOperatorEvent$24(StreamTask.java:1609) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.StreamTaskActionExecutor$1.runThrowing(StreamTaskActionExecutor.java:50) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.mailbox.Mail.run(Mail.java:101) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.mailbox.MailboxProcessor.runMail(MailboxProcessor.java:414) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.mailbox.MailboxProcessor.processMailsWhenDefaultActionUnavailable(MailboxProcessor.java:383) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.mailbox.MailboxProcessor.processMail(MailboxProcessor.java:368) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.mailbox.MailboxProcessor.runMailboxLoop(MailboxProcessor.java:229) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.StreamTask.runMailboxLoop(StreamTask.java:973) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.StreamTask.invoke(StreamTask.java:917) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.runtime.taskmanager.Task.runWithSystemExitMonitoring(Task.java:970) [flink-runtime-1.20.0.jar:1.20.0]
	at org.apache.flink.runtime.taskmanager.Task.restoreAndInvoke(Task.java:949) [flink-runtime-1.20.0.jar:1.20.0]
	at org.apache.flink.runtime.taskmanager.Task.doRun(Task.java:763) [flink-runtime-1.20.0.jar:1.20.0]
	at org.apache.flink.runtime.taskmanager.Task.run(Task.java:575) [flink-runtime-1.20.0.jar:1.20.0]
	at java.lang.Thread.run(Thread.java:840) [?:?]
2025-08-20 18:40:16.443 [main] WARN  [o.a.f.c.k.s.KafkaSourceBuilder:497] - Property auto.offset.reset is provided but will be overridden from earliest to earliest
2025-08-20 18:40:16.461 [main] WARN  [o.a.f.c.k.s.KafkaSourceBuilder:497] - Property auto.offset.reset is provided but will be overridden from earliest to earliest
2025-08-20 18:40:16.461 [main] WARN  [o.a.f.c.k.s.KafkaSourceBuilder:497] - Property auto.offset.reset is provided but will be overridden from earliest to earliest
2025-08-20 18:40:16.946 [main] WARN  [o.a.f.r.s.t.DefaultDelegationTokenManager:248] - No tokens obtained so skipping notifications
2025-08-20 18:40:17.071 [main] WARN  [o.a.f.r.w.WebMonitorUtils:76] - Log file environment variable 'log.file' is not set.
2025-08-20 18:40:17.071 [main] WARN  [o.a.f.r.w.WebMonitorUtils:82] - JobManager log files are unavailable in the web dashboard. Log file location not found in environment variable 'log.file' or configuration key 'web.log.path'.
2025-08-20 18:40:17.139 [flink-pekko.actor.default-dispatcher-5] WARN  [o.a.f.r.s.t.DefaultDelegationTokenManager:320] - No tokens obtained so skipping notifications
2025-08-20 18:40:17.139 [flink-pekko.actor.default-dispatcher-5] WARN  [o.a.f.r.s.t.DefaultDelegationTokenManager:335] - Tokens update task not started because either no tokens obtained or none of the tokens specified its renewal date
2025-08-20 18:40:17.256 [SourceCoordinator-Source: Kafka-组合订单] WARN  [o.a.k.c.a.AdminClientConfig:385] - These configurations '[key.deserializer, value.deserializer, max.poll.records, client.id.prefix, group.id, partition.discovery.interval.ms, heartbeat.interval.ms, enable.auto.commit, fetch.max.wait.ms, fetch.min.bytes, session.timeout.ms, auto.offset.reset]' were supplied but are not used yet.
2025-08-20 18:40:17.256 [SourceCoordinator-Source: Kafka-单腿订单] WARN  [o.a.k.c.a.AdminClientConfig:385] - These configurations '[key.deserializer, value.deserializer, max.poll.records, client.id.prefix, group.id, partition.discovery.interval.ms, heartbeat.interval.ms, enable.auto.commit, fetch.max.wait.ms, fetch.min.bytes, session.timeout.ms, auto.offset.reset]' were supplied but are not used yet.
2025-08-20 18:40:17.256 [SourceCoordinator-Source: Kafka-交易] WARN  [o.a.k.c.a.AdminClientConfig:385] - These configurations '[key.deserializer, value.deserializer, max.poll.records, client.id.prefix, group.id, partition.discovery.interval.ms, heartbeat.interval.ms, enable.auto.commit, fetch.max.wait.ms, fetch.min.bytes, session.timeout.ms, auto.offset.reset]' were supplied but are not used yet.
2025-08-20 18:40:17.257 [SourceCoordinator-Source: Kafka-单腿订单] WARN  [o.a.k.c.u.AppInfoParser:68] - Error registering AppInfo mbean
javax.management.InstanceAlreadyExistsException: kafka.admin.client:type=app-info,id=orderbook-local-test-group-enumerator-admin-client
	at com.sun.jmx.mbeanserver.Repository.addMBean(Repository.java:436) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerWithRepository(DefaultMBeanServerInterceptor.java:1865) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerDynamicMBean(DefaultMBeanServerInterceptor.java:960) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerObject(DefaultMBeanServerInterceptor.java:895) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerMBean(DefaultMBeanServerInterceptor.java:320) ~[?:?]
	at com.sun.jmx.mbeanserver.JmxMBeanServer.registerMBean(JmxMBeanServer.java:523) ~[?:?]
	at org.apache.kafka.common.utils.AppInfoParser.registerAppInfo(AppInfoParser.java:64) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.admin.KafkaAdminClient.<init>(KafkaAdminClient.java:602) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.admin.KafkaAdminClient.createInternal(KafkaAdminClient.java:544) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.admin.KafkaAdminClient.createInternal(KafkaAdminClient.java:488) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.admin.Admin.create(Admin.java:134) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.admin.AdminClient.create(AdminClient.java:39) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.flink.connector.kafka.source.enumerator.KafkaSourceEnumerator.getKafkaAdminClient(KafkaSourceEnumerator.java:441) ~[flink-connector-kafka-3.4.0-1.20.jar:3.4.0-1.20]
	at org.apache.flink.connector.kafka.source.enumerator.KafkaSourceEnumerator.start(KafkaSourceEnumerator.java:164) ~[flink-connector-kafka-3.4.0-1.20.jar:3.4.0-1.20]
	at org.apache.flink.runtime.source.coordinator.SourceCoordinator.lambda$start$1(SourceCoordinator.java:244) ~[flink-runtime-1.20.0.jar:1.20.0]
	at org.apache.flink.runtime.source.coordinator.SourceCoordinator.lambda$runInEventLoop$10(SourceCoordinator.java:533) ~[flink-runtime-1.20.0.jar:1.20.0]
	at org.apache.flink.util.ThrowableCatchingRunnable.run(ThrowableCatchingRunnable.java:40) [flink-core-1.20.0.jar:1.20.0]
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:539) [?:?]
	at java.util.concurrent.FutureTask.run(FutureTask.java:264) [?:?]
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:304) [?:?]
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136) [?:?]
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635) [?:?]
	at java.lang.Thread.run(Thread.java:840) [?:?]
2025-08-20 18:40:17.260 [SourceCoordinator-Source: Kafka-交易] WARN  [o.a.k.c.u.AppInfoParser:68] - Error registering AppInfo mbean
javax.management.InstanceAlreadyExistsException: kafka.admin.client:type=app-info,id=orderbook-local-test-group-enumerator-admin-client
	at com.sun.jmx.mbeanserver.Repository.addMBean(Repository.java:436) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerWithRepository(DefaultMBeanServerInterceptor.java:1865) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerDynamicMBean(DefaultMBeanServerInterceptor.java:960) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerObject(DefaultMBeanServerInterceptor.java:895) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerMBean(DefaultMBeanServerInterceptor.java:320) ~[?:?]
	at com.sun.jmx.mbeanserver.JmxMBeanServer.registerMBean(JmxMBeanServer.java:523) ~[?:?]
	at org.apache.kafka.common.utils.AppInfoParser.registerAppInfo(AppInfoParser.java:64) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.admin.KafkaAdminClient.<init>(KafkaAdminClient.java:602) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.admin.KafkaAdminClient.createInternal(KafkaAdminClient.java:544) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.admin.KafkaAdminClient.createInternal(KafkaAdminClient.java:488) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.admin.Admin.create(Admin.java:134) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.admin.AdminClient.create(AdminClient.java:39) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.flink.connector.kafka.source.enumerator.KafkaSourceEnumerator.getKafkaAdminClient(KafkaSourceEnumerator.java:441) ~[flink-connector-kafka-3.4.0-1.20.jar:3.4.0-1.20]
	at org.apache.flink.connector.kafka.source.enumerator.KafkaSourceEnumerator.start(KafkaSourceEnumerator.java:164) ~[flink-connector-kafka-3.4.0-1.20.jar:3.4.0-1.20]
	at org.apache.flink.runtime.source.coordinator.SourceCoordinator.lambda$start$1(SourceCoordinator.java:244) ~[flink-runtime-1.20.0.jar:1.20.0]
	at org.apache.flink.runtime.source.coordinator.SourceCoordinator.lambda$runInEventLoop$10(SourceCoordinator.java:533) ~[flink-runtime-1.20.0.jar:1.20.0]
	at org.apache.flink.util.ThrowableCatchingRunnable.run(ThrowableCatchingRunnable.java:40) [flink-core-1.20.0.jar:1.20.0]
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:539) [?:?]
	at java.util.concurrent.FutureTask.run(FutureTask.java:264) [?:?]
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:304) [?:?]
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136) [?:?]
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635) [?:?]
	at java.lang.Thread.run(Thread.java:840) [?:?]
2025-08-20 18:40:17.420 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] WARN  [o.a.k.c.c.ConsumerConfig:385] - These configurations '[client.id.prefix, partition.discovery.interval.ms]' were supplied but are not used yet.
2025-08-20 18:40:17.420 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] WARN  [o.a.k.c.c.ConsumerConfig:385] - These configurations '[client.id.prefix, partition.discovery.interval.ms]' were supplied but are not used yet.
2025-08-20 18:40:17.420 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] WARN  [o.a.k.c.c.ConsumerConfig:385] - These configurations '[client.id.prefix, partition.discovery.interval.ms]' were supplied but are not used yet.
2025-08-20 18:40:17.421 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] WARN  [o.a.k.c.u.AppInfoParser:68] - Error registering AppInfo mbean
javax.management.InstanceAlreadyExistsException: kafka.consumer:type=app-info,id=orderbook-local-test-group-0
	at com.sun.jmx.mbeanserver.Repository.addMBean(Repository.java:436) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerWithRepository(DefaultMBeanServerInterceptor.java:1865) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerDynamicMBean(DefaultMBeanServerInterceptor.java:960) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerObject(DefaultMBeanServerInterceptor.java:895) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerMBean(DefaultMBeanServerInterceptor.java:320) ~[?:?]
	at com.sun.jmx.mbeanserver.JmxMBeanServer.registerMBean(JmxMBeanServer.java:523) ~[?:?]
	at org.apache.kafka.common.utils.AppInfoParser.registerAppInfo(AppInfoParser.java:64) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.consumer.KafkaConsumer.<init>(KafkaConsumer.java:821) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.consumer.KafkaConsumer.<init>(KafkaConsumer.java:665) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.consumer.KafkaConsumer.<init>(KafkaConsumer.java:646) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.consumer.KafkaConsumer.<init>(KafkaConsumer.java:626) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.flink.connector.kafka.source.reader.KafkaPartitionSplitReader.<init>(KafkaPartitionSplitReader.java:97) ~[flink-connector-kafka-3.4.0-1.20.jar:3.4.0-1.20]
	at org.apache.flink.connector.kafka.source.KafkaSource.lambda$createReader$1(KafkaSource.java:185) ~[flink-connector-kafka-3.4.0-1.20.jar:3.4.0-1.20]
	at org.apache.flink.connector.base.source.reader.fetcher.SplitFetcherManager.createSplitFetcher(SplitFetcherManager.java:259) ~[flink-connector-base-1.20.0.jar:1.20.0]
	at org.apache.flink.connector.base.source.reader.fetcher.SingleThreadFetcherManager.addSplits(SingleThreadFetcherManager.java:148) ~[flink-connector-base-1.20.0.jar:1.20.0]
	at org.apache.flink.connector.base.source.reader.SourceReaderBase.addSplits(SourceReaderBase.java:315) ~[flink-connector-base-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.api.operators.SourceOperator.handleAddSplitsEvent(SourceOperator.java:606) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.api.operators.SourceOperator.handleOperatorEvent(SourceOperator.java:575) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.OperatorEventDispatcherImpl.dispatchEventToHandlers(OperatorEventDispatcherImpl.java:72) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.RegularOperatorChain.dispatchOperatorEvent(RegularOperatorChain.java:80) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.StreamTask.lambda$dispatchOperatorEvent$24(StreamTask.java:1609) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.StreamTaskActionExecutor$1.runThrowing(StreamTaskActionExecutor.java:50) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.mailbox.Mail.run(Mail.java:101) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.mailbox.MailboxProcessor.runMail(MailboxProcessor.java:414) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.mailbox.MailboxProcessor.processMailsWhenDefaultActionUnavailable(MailboxProcessor.java:383) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.mailbox.MailboxProcessor.processMail(MailboxProcessor.java:368) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.mailbox.MailboxProcessor.runMailboxLoop(MailboxProcessor.java:229) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.StreamTask.runMailboxLoop(StreamTask.java:973) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.StreamTask.invoke(StreamTask.java:917) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.runtime.taskmanager.Task.runWithSystemExitMonitoring(Task.java:970) [flink-runtime-1.20.0.jar:1.20.0]
	at org.apache.flink.runtime.taskmanager.Task.restoreAndInvoke(Task.java:949) [flink-runtime-1.20.0.jar:1.20.0]
	at org.apache.flink.runtime.taskmanager.Task.doRun(Task.java:763) [flink-runtime-1.20.0.jar:1.20.0]
	at org.apache.flink.runtime.taskmanager.Task.run(Task.java:575) [flink-runtime-1.20.0.jar:1.20.0]
	at java.lang.Thread.run(Thread.java:840) [?:?]
2025-08-20 18:40:17.421 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] WARN  [o.a.k.c.u.AppInfoParser:68] - Error registering AppInfo mbean
javax.management.InstanceAlreadyExistsException: kafka.consumer:type=app-info,id=orderbook-local-test-group-0
	at com.sun.jmx.mbeanserver.Repository.addMBean(Repository.java:436) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerWithRepository(DefaultMBeanServerInterceptor.java:1865) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerDynamicMBean(DefaultMBeanServerInterceptor.java:960) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerObject(DefaultMBeanServerInterceptor.java:895) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerMBean(DefaultMBeanServerInterceptor.java:320) ~[?:?]
	at com.sun.jmx.mbeanserver.JmxMBeanServer.registerMBean(JmxMBeanServer.java:523) ~[?:?]
	at org.apache.kafka.common.utils.AppInfoParser.registerAppInfo(AppInfoParser.java:64) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.consumer.KafkaConsumer.<init>(KafkaConsumer.java:821) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.consumer.KafkaConsumer.<init>(KafkaConsumer.java:665) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.consumer.KafkaConsumer.<init>(KafkaConsumer.java:646) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.consumer.KafkaConsumer.<init>(KafkaConsumer.java:626) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.flink.connector.kafka.source.reader.KafkaPartitionSplitReader.<init>(KafkaPartitionSplitReader.java:97) ~[flink-connector-kafka-3.4.0-1.20.jar:3.4.0-1.20]
	at org.apache.flink.connector.kafka.source.KafkaSource.lambda$createReader$1(KafkaSource.java:185) ~[flink-connector-kafka-3.4.0-1.20.jar:3.4.0-1.20]
	at org.apache.flink.connector.base.source.reader.fetcher.SplitFetcherManager.createSplitFetcher(SplitFetcherManager.java:259) ~[flink-connector-base-1.20.0.jar:1.20.0]
	at org.apache.flink.connector.base.source.reader.fetcher.SingleThreadFetcherManager.addSplits(SingleThreadFetcherManager.java:148) ~[flink-connector-base-1.20.0.jar:1.20.0]
	at org.apache.flink.connector.base.source.reader.SourceReaderBase.addSplits(SourceReaderBase.java:315) ~[flink-connector-base-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.api.operators.SourceOperator.handleAddSplitsEvent(SourceOperator.java:606) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.api.operators.SourceOperator.handleOperatorEvent(SourceOperator.java:575) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.OperatorEventDispatcherImpl.dispatchEventToHandlers(OperatorEventDispatcherImpl.java:72) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.RegularOperatorChain.dispatchOperatorEvent(RegularOperatorChain.java:80) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.StreamTask.lambda$dispatchOperatorEvent$24(StreamTask.java:1609) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.StreamTaskActionExecutor$1.runThrowing(StreamTaskActionExecutor.java:50) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.mailbox.Mail.run(Mail.java:101) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.mailbox.MailboxProcessor.runMail(MailboxProcessor.java:414) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.mailbox.MailboxProcessor.processMailsWhenDefaultActionUnavailable(MailboxProcessor.java:383) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.mailbox.MailboxProcessor.processMail(MailboxProcessor.java:368) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.mailbox.MailboxProcessor.runMailboxLoop(MailboxProcessor.java:229) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.StreamTask.runMailboxLoop(StreamTask.java:973) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.StreamTask.invoke(StreamTask.java:917) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.runtime.taskmanager.Task.runWithSystemExitMonitoring(Task.java:970) [flink-runtime-1.20.0.jar:1.20.0]
	at org.apache.flink.runtime.taskmanager.Task.restoreAndInvoke(Task.java:949) [flink-runtime-1.20.0.jar:1.20.0]
	at org.apache.flink.runtime.taskmanager.Task.doRun(Task.java:763) [flink-runtime-1.20.0.jar:1.20.0]
	at org.apache.flink.runtime.taskmanager.Task.run(Task.java:575) [flink-runtime-1.20.0.jar:1.20.0]
	at java.lang.Thread.run(Thread.java:840) [?:?]
2025-08-20 18:40:17.551 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] WARN  [c.f.f.PnLCalculationFunction:180] - 发现无持仓的平仓交易 - 发送到异常流: 交易号=101355625, 合约=JD2504-C-3200, 方向=S, 数量=5
2025-08-20 18:40:17.552 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] WARN  [c.f.f.PnLCalculationFunction:180] - 发现无持仓的平仓交易 - 发送到异常流: 交易号=101355626, 合约=JD2504-C-3200, 方向=S, 数量=5
2025-08-20 18:40:17.552 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] WARN  [c.f.f.PnLCalculationFunction:180] - 发现无持仓的平仓交易 - 发送到异常流: 交易号=101355700, 合约=EB2505, 方向=B, 数量=10
2025-08-20 18:40:17.553 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] WARN  [c.f.f.PnLCalculationFunction:180] - 发现无持仓的平仓交易 - 发送到异常流: 交易号=48060, 合约=SH2505, 方向=S, 数量=1
2025-08-20 18:40:17.553 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] WARN  [c.f.f.PnLCalculationFunction:180] - 发现无持仓的平仓交易 - 发送到异常流: 交易号=100052597, 合约=EB2505, 方向=B, 数量=8
2025-08-20 18:40:17.556 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] WARN  [c.f.f.PnLCalculationFunction:180] - 发现无持仓的平仓交易 - 发送到异常流: 交易号=100052597, 合约=EB2505, 方向=S, 数量=8
2025-08-20 18:40:17.556 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] WARN  [c.f.f.PnLCalculationFunction:180] - 发现无持仓的平仓交易 - 发送到异常流: 交易号=100052678, 合约=EB2505, 方向=B, 数量=1
2025-08-20 18:40:17.557 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] WARN  [c.f.f.PnLCalculationFunction:180] - 发现无持仓的平仓交易 - 发送到异常流: 交易号=100052678, 合约=EB2505, 方向=S, 数量=1
2025-08-20 18:40:17.557 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] WARN  [c.f.f.PnLCalculationFunction:180] - 发现无持仓的平仓交易 - 发送到异常流: 交易号=100052768, 合约=EB2505, 方向=B, 数量=1
2025-08-20 18:40:17.560 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] WARN  [c.f.f.PnLCalculationFunction:180] - 发现无持仓的平仓交易 - 发送到异常流: 交易号=100052770, 合约=EB2505, 方向=B, 数量=1
2025-08-20 18:40:17.560 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] WARN  [c.f.f.PnLCalculationFunction:180] - 发现无持仓的平仓交易 - 发送到异常流: 交易号=100052795, 合约=EB2505, 方向=B, 数量=1
2025-08-20 18:40:17.564 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] WARN  [c.f.f.PnLCalculationFunction:180] - 发现无持仓的平仓交易 - 发送到异常流: 交易号=100052821, 合约=EB2505, 方向=B, 数量=1
2025-08-20 18:40:17.565 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] WARN  [c.f.f.PnLCalculationFunction:180] - 发现无持仓的平仓交易 - 发送到异常流: 交易号=100052822, 合约=EB2505, 方向=B, 数量=2
2025-08-20 18:40:17.565 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] WARN  [c.f.f.PnLCalculationFunction:180] - 发现无持仓的平仓交易 - 发送到异常流: 交易号=100052833, 合约=EB2505, 方向=B, 数量=2
2025-08-20 18:40:17.566 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] WARN  [c.f.f.PnLCalculationFunction:180] - 发现无持仓的平仓交易 - 发送到异常流: 交易号=100052846, 合约=EB2504, 方向=B, 数量=1
2025-08-20 18:40:17.567 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] WARN  [c.f.f.PnLCalculationFunction:180] - 发现无持仓的平仓交易 - 发送到异常流: 交易号=100052847, 合约=EB2505, 方向=B, 数量=1
2025-08-20 18:40:17.568 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] WARN  [c.f.f.PnLCalculationFunction:180] - 发现无持仓的平仓交易 - 发送到异常流: 交易号=100052849, 合约=EB2504, 方向=S, 数量=10
2025-08-20 18:40:17.620 [订单簿重建 (1/1)#0] WARN  [c.f.f.OrderBookReconstructionFunction:345] - [基础层] 订单状态1但剩余量为0，移除: 100257007
2025-08-20 18:40:17.621 [订单簿重建 (1/1)#0] WARN  [c.f.f.OrderBookReconstructionFunction:368] - [基础层] 订单状态3但剩余量为0，移除: 100257004
2025-08-20 18:40:17.677 [订单簿重建 (1/1)#0] WARN  [c.f.f.OrderBookReconstructionFunction:345] - [基础层] 订单状态1但剩余量为0，移除: 100257007
2025-08-20 18:40:17.677 [订单簿重建 (1/1)#0] WARN  [c.f.f.OrderBookReconstructionFunction:368] - [基础层] 订单状态3但剩余量为0，移除: 100257004
2025-08-20 18:40:17.681 [订单簿重建 (1/1)#0] WARN  [c.f.f.OrderBookReconstructionFunction:345] - [基础层] 订单状态1但剩余量为0，移除: 100257007
2025-08-20 18:40:17.682 [订单簿重建 (1/1)#0] WARN  [c.f.f.OrderBookReconstructionFunction:368] - [基础层] 订单状态3但剩余量为0，移除: 100257004
2025-08-20 18:40:17.686 [订单簿重建 (1/1)#0] WARN  [c.f.f.OrderBookReconstructionFunction:345] - [基础层] 订单状态1但剩余量为0，移除: 100257007
2025-08-20 18:40:17.686 [订单簿重建 (1/1)#0] WARN  [c.f.f.OrderBookReconstructionFunction:368] - [基础层] 订单状态3但剩余量为0，移除: 100257004
2025-08-20 18:40:17.690 [订单簿重建 (1/1)#0] WARN  [c.f.f.OrderBookReconstructionFunction:345] - [基础层] 订单状态1但剩余量为0，移除: 100257007
2025-08-20 18:40:17.690 [订单簿重建 (1/1)#0] WARN  [c.f.f.OrderBookReconstructionFunction:368] - [基础层] 订单状态3但剩余量为0，移除: 100257004
2025-08-20 18:58:25.963 [main] WARN  [o.a.f.c.k.s.KafkaSourceBuilder:497] - Property auto.offset.reset is provided but will be overridden from earliest to earliest
2025-08-20 18:58:25.980 [main] WARN  [o.a.f.c.k.s.KafkaSourceBuilder:497] - Property auto.offset.reset is provided but will be overridden from earliest to earliest
2025-08-20 18:58:25.981 [main] WARN  [o.a.f.c.k.s.KafkaSourceBuilder:497] - Property auto.offset.reset is provided but will be overridden from earliest to earliest
2025-08-20 18:58:26.460 [main] WARN  [o.a.f.r.s.t.DefaultDelegationTokenManager:248] - No tokens obtained so skipping notifications
2025-08-20 18:58:26.579 [main] WARN  [o.a.f.r.w.WebMonitorUtils:76] - Log file environment variable 'log.file' is not set.
2025-08-20 18:58:26.579 [main] WARN  [o.a.f.r.w.WebMonitorUtils:82] - JobManager log files are unavailable in the web dashboard. Log file location not found in environment variable 'log.file' or configuration key 'web.log.path'.
2025-08-20 18:58:26.639 [flink-pekko.actor.default-dispatcher-4] WARN  [o.a.f.r.s.t.DefaultDelegationTokenManager:320] - No tokens obtained so skipping notifications
2025-08-20 18:58:26.640 [flink-pekko.actor.default-dispatcher-4] WARN  [o.a.f.r.s.t.DefaultDelegationTokenManager:335] - Tokens update task not started because either no tokens obtained or none of the tokens specified its renewal date
2025-08-20 18:58:26.747 [SourceCoordinator-Source: Kafka-交易] WARN  [o.a.k.c.a.AdminClientConfig:385] - These configurations '[key.deserializer, value.deserializer, max.poll.records, client.id.prefix, group.id, partition.discovery.interval.ms, heartbeat.interval.ms, enable.auto.commit, fetch.max.wait.ms, fetch.min.bytes, session.timeout.ms, auto.offset.reset]' were supplied but are not used yet.
2025-08-20 18:58:26.747 [SourceCoordinator-Source: Kafka-单腿订单] WARN  [o.a.k.c.a.AdminClientConfig:385] - These configurations '[key.deserializer, value.deserializer, max.poll.records, client.id.prefix, group.id, partition.discovery.interval.ms, heartbeat.interval.ms, enable.auto.commit, fetch.max.wait.ms, fetch.min.bytes, session.timeout.ms, auto.offset.reset]' were supplied but are not used yet.
2025-08-20 18:58:26.747 [SourceCoordinator-Source: Kafka-组合订单] WARN  [o.a.k.c.a.AdminClientConfig:385] - These configurations '[key.deserializer, value.deserializer, max.poll.records, client.id.prefix, group.id, partition.discovery.interval.ms, heartbeat.interval.ms, enable.auto.commit, fetch.max.wait.ms, fetch.min.bytes, session.timeout.ms, auto.offset.reset]' were supplied but are not used yet.
2025-08-20 18:58:26.748 [SourceCoordinator-Source: Kafka-单腿订单] WARN  [o.a.k.c.u.AppInfoParser:68] - Error registering AppInfo mbean
javax.management.InstanceAlreadyExistsException: kafka.admin.client:type=app-info,id=orderbook-local-test-group-enumerator-admin-client
	at com.sun.jmx.mbeanserver.Repository.addMBean(Repository.java:436) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerWithRepository(DefaultMBeanServerInterceptor.java:1865) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerDynamicMBean(DefaultMBeanServerInterceptor.java:960) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerObject(DefaultMBeanServerInterceptor.java:895) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerMBean(DefaultMBeanServerInterceptor.java:320) ~[?:?]
	at com.sun.jmx.mbeanserver.JmxMBeanServer.registerMBean(JmxMBeanServer.java:523) ~[?:?]
	at org.apache.kafka.common.utils.AppInfoParser.registerAppInfo(AppInfoParser.java:64) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.admin.KafkaAdminClient.<init>(KafkaAdminClient.java:602) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.admin.KafkaAdminClient.createInternal(KafkaAdminClient.java:544) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.admin.KafkaAdminClient.createInternal(KafkaAdminClient.java:488) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.admin.Admin.create(Admin.java:134) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.admin.AdminClient.create(AdminClient.java:39) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.flink.connector.kafka.source.enumerator.KafkaSourceEnumerator.getKafkaAdminClient(KafkaSourceEnumerator.java:441) ~[flink-connector-kafka-3.4.0-1.20.jar:3.4.0-1.20]
	at org.apache.flink.connector.kafka.source.enumerator.KafkaSourceEnumerator.start(KafkaSourceEnumerator.java:164) ~[flink-connector-kafka-3.4.0-1.20.jar:3.4.0-1.20]
	at org.apache.flink.runtime.source.coordinator.SourceCoordinator.lambda$start$1(SourceCoordinator.java:244) ~[flink-runtime-1.20.0.jar:1.20.0]
	at org.apache.flink.runtime.source.coordinator.SourceCoordinator.lambda$runInEventLoop$10(SourceCoordinator.java:533) ~[flink-runtime-1.20.0.jar:1.20.0]
	at org.apache.flink.util.ThrowableCatchingRunnable.run(ThrowableCatchingRunnable.java:40) [flink-core-1.20.0.jar:1.20.0]
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:539) [?:?]
	at java.util.concurrent.FutureTask.run(FutureTask.java:264) [?:?]
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:304) [?:?]
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136) [?:?]
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635) [?:?]
	at java.lang.Thread.run(Thread.java:840) [?:?]
2025-08-20 18:58:26.751 [SourceCoordinator-Source: Kafka-组合订单] WARN  [o.a.k.c.u.AppInfoParser:68] - Error registering AppInfo mbean
javax.management.InstanceAlreadyExistsException: kafka.admin.client:type=app-info,id=orderbook-local-test-group-enumerator-admin-client
	at com.sun.jmx.mbeanserver.Repository.addMBean(Repository.java:436) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerWithRepository(DefaultMBeanServerInterceptor.java:1865) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerDynamicMBean(DefaultMBeanServerInterceptor.java:960) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerObject(DefaultMBeanServerInterceptor.java:895) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerMBean(DefaultMBeanServerInterceptor.java:320) ~[?:?]
	at com.sun.jmx.mbeanserver.JmxMBeanServer.registerMBean(JmxMBeanServer.java:523) ~[?:?]
	at org.apache.kafka.common.utils.AppInfoParser.registerAppInfo(AppInfoParser.java:64) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.admin.KafkaAdminClient.<init>(KafkaAdminClient.java:602) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.admin.KafkaAdminClient.createInternal(KafkaAdminClient.java:544) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.admin.KafkaAdminClient.createInternal(KafkaAdminClient.java:488) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.admin.Admin.create(Admin.java:134) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.admin.AdminClient.create(AdminClient.java:39) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.flink.connector.kafka.source.enumerator.KafkaSourceEnumerator.getKafkaAdminClient(KafkaSourceEnumerator.java:441) ~[flink-connector-kafka-3.4.0-1.20.jar:3.4.0-1.20]
	at org.apache.flink.connector.kafka.source.enumerator.KafkaSourceEnumerator.start(KafkaSourceEnumerator.java:164) ~[flink-connector-kafka-3.4.0-1.20.jar:3.4.0-1.20]
	at org.apache.flink.runtime.source.coordinator.SourceCoordinator.lambda$start$1(SourceCoordinator.java:244) ~[flink-runtime-1.20.0.jar:1.20.0]
	at org.apache.flink.runtime.source.coordinator.SourceCoordinator.lambda$runInEventLoop$10(SourceCoordinator.java:533) ~[flink-runtime-1.20.0.jar:1.20.0]
	at org.apache.flink.util.ThrowableCatchingRunnable.run(ThrowableCatchingRunnable.java:40) [flink-core-1.20.0.jar:1.20.0]
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:539) [?:?]
	at java.util.concurrent.FutureTask.run(FutureTask.java:264) [?:?]
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:304) [?:?]
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136) [?:?]
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635) [?:?]
	at java.lang.Thread.run(Thread.java:840) [?:?]
2025-08-20 18:58:26.903 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] WARN  [o.a.k.c.c.ConsumerConfig:385] - These configurations '[client.id.prefix, partition.discovery.interval.ms]' were supplied but are not used yet.
2025-08-20 18:58:26.903 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] WARN  [o.a.k.c.c.ConsumerConfig:385] - These configurations '[client.id.prefix, partition.discovery.interval.ms]' were supplied but are not used yet.
2025-08-20 18:58:26.903 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] WARN  [o.a.k.c.c.ConsumerConfig:385] - These configurations '[client.id.prefix, partition.discovery.interval.ms]' were supplied but are not used yet.
2025-08-20 18:58:26.903 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] WARN  [o.a.k.c.u.AppInfoParser:68] - Error registering AppInfo mbean
javax.management.InstanceAlreadyExistsException: kafka.consumer:type=app-info,id=orderbook-local-test-group-0
	at com.sun.jmx.mbeanserver.Repository.addMBean(Repository.java:436) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerWithRepository(DefaultMBeanServerInterceptor.java:1865) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerDynamicMBean(DefaultMBeanServerInterceptor.java:960) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerObject(DefaultMBeanServerInterceptor.java:895) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerMBean(DefaultMBeanServerInterceptor.java:320) ~[?:?]
	at com.sun.jmx.mbeanserver.JmxMBeanServer.registerMBean(JmxMBeanServer.java:523) ~[?:?]
	at org.apache.kafka.common.utils.AppInfoParser.registerAppInfo(AppInfoParser.java:64) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.consumer.KafkaConsumer.<init>(KafkaConsumer.java:821) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.consumer.KafkaConsumer.<init>(KafkaConsumer.java:665) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.consumer.KafkaConsumer.<init>(KafkaConsumer.java:646) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.consumer.KafkaConsumer.<init>(KafkaConsumer.java:626) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.flink.connector.kafka.source.reader.KafkaPartitionSplitReader.<init>(KafkaPartitionSplitReader.java:97) ~[flink-connector-kafka-3.4.0-1.20.jar:3.4.0-1.20]
	at org.apache.flink.connector.kafka.source.KafkaSource.lambda$createReader$1(KafkaSource.java:185) ~[flink-connector-kafka-3.4.0-1.20.jar:3.4.0-1.20]
	at org.apache.flink.connector.base.source.reader.fetcher.SplitFetcherManager.createSplitFetcher(SplitFetcherManager.java:259) ~[flink-connector-base-1.20.0.jar:1.20.0]
	at org.apache.flink.connector.base.source.reader.fetcher.SingleThreadFetcherManager.addSplits(SingleThreadFetcherManager.java:148) ~[flink-connector-base-1.20.0.jar:1.20.0]
	at org.apache.flink.connector.base.source.reader.SourceReaderBase.addSplits(SourceReaderBase.java:315) ~[flink-connector-base-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.api.operators.SourceOperator.handleAddSplitsEvent(SourceOperator.java:606) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.api.operators.SourceOperator.handleOperatorEvent(SourceOperator.java:575) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.OperatorEventDispatcherImpl.dispatchEventToHandlers(OperatorEventDispatcherImpl.java:72) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.RegularOperatorChain.dispatchOperatorEvent(RegularOperatorChain.java:80) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.StreamTask.lambda$dispatchOperatorEvent$24(StreamTask.java:1609) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.StreamTaskActionExecutor$1.runThrowing(StreamTaskActionExecutor.java:50) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.mailbox.Mail.run(Mail.java:101) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.mailbox.MailboxProcessor.runMail(MailboxProcessor.java:414) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.mailbox.MailboxProcessor.processMailsWhenDefaultActionUnavailable(MailboxProcessor.java:383) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.mailbox.MailboxProcessor.processMail(MailboxProcessor.java:368) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.mailbox.MailboxProcessor.runMailboxLoop(MailboxProcessor.java:229) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.StreamTask.runMailboxLoop(StreamTask.java:973) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.StreamTask.invoke(StreamTask.java:917) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.runtime.taskmanager.Task.runWithSystemExitMonitoring(Task.java:970) [flink-runtime-1.20.0.jar:1.20.0]
	at org.apache.flink.runtime.taskmanager.Task.restoreAndInvoke(Task.java:949) [flink-runtime-1.20.0.jar:1.20.0]
	at org.apache.flink.runtime.taskmanager.Task.doRun(Task.java:763) [flink-runtime-1.20.0.jar:1.20.0]
	at org.apache.flink.runtime.taskmanager.Task.run(Task.java:575) [flink-runtime-1.20.0.jar:1.20.0]
	at java.lang.Thread.run(Thread.java:840) [?:?]
2025-08-20 18:58:26.904 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] WARN  [o.a.k.c.u.AppInfoParser:68] - Error registering AppInfo mbean
javax.management.InstanceAlreadyExistsException: kafka.consumer:type=app-info,id=orderbook-local-test-group-0
	at com.sun.jmx.mbeanserver.Repository.addMBean(Repository.java:436) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerWithRepository(DefaultMBeanServerInterceptor.java:1865) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerDynamicMBean(DefaultMBeanServerInterceptor.java:960) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerObject(DefaultMBeanServerInterceptor.java:895) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerMBean(DefaultMBeanServerInterceptor.java:320) ~[?:?]
	at com.sun.jmx.mbeanserver.JmxMBeanServer.registerMBean(JmxMBeanServer.java:523) ~[?:?]
	at org.apache.kafka.common.utils.AppInfoParser.registerAppInfo(AppInfoParser.java:64) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.consumer.KafkaConsumer.<init>(KafkaConsumer.java:821) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.consumer.KafkaConsumer.<init>(KafkaConsumer.java:665) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.consumer.KafkaConsumer.<init>(KafkaConsumer.java:646) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.consumer.KafkaConsumer.<init>(KafkaConsumer.java:626) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.flink.connector.kafka.source.reader.KafkaPartitionSplitReader.<init>(KafkaPartitionSplitReader.java:97) ~[flink-connector-kafka-3.4.0-1.20.jar:3.4.0-1.20]
	at org.apache.flink.connector.kafka.source.KafkaSource.lambda$createReader$1(KafkaSource.java:185) ~[flink-connector-kafka-3.4.0-1.20.jar:3.4.0-1.20]
	at org.apache.flink.connector.base.source.reader.fetcher.SplitFetcherManager.createSplitFetcher(SplitFetcherManager.java:259) ~[flink-connector-base-1.20.0.jar:1.20.0]
	at org.apache.flink.connector.base.source.reader.fetcher.SingleThreadFetcherManager.addSplits(SingleThreadFetcherManager.java:148) ~[flink-connector-base-1.20.0.jar:1.20.0]
	at org.apache.flink.connector.base.source.reader.SourceReaderBase.addSplits(SourceReaderBase.java:315) ~[flink-connector-base-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.api.operators.SourceOperator.handleAddSplitsEvent(SourceOperator.java:606) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.api.operators.SourceOperator.handleOperatorEvent(SourceOperator.java:575) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.OperatorEventDispatcherImpl.dispatchEventToHandlers(OperatorEventDispatcherImpl.java:72) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.RegularOperatorChain.dispatchOperatorEvent(RegularOperatorChain.java:80) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.StreamTask.lambda$dispatchOperatorEvent$24(StreamTask.java:1609) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.StreamTaskActionExecutor$1.runThrowing(StreamTaskActionExecutor.java:50) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.mailbox.Mail.run(Mail.java:101) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.mailbox.MailboxProcessor.runMail(MailboxProcessor.java:414) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.mailbox.MailboxProcessor.processMailsWhenDefaultActionUnavailable(MailboxProcessor.java:383) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.mailbox.MailboxProcessor.processMail(MailboxProcessor.java:368) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.mailbox.MailboxProcessor.runMailboxLoop(MailboxProcessor.java:229) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.StreamTask.runMailboxLoop(StreamTask.java:973) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.StreamTask.invoke(StreamTask.java:917) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.runtime.taskmanager.Task.runWithSystemExitMonitoring(Task.java:970) [flink-runtime-1.20.0.jar:1.20.0]
	at org.apache.flink.runtime.taskmanager.Task.restoreAndInvoke(Task.java:949) [flink-runtime-1.20.0.jar:1.20.0]
	at org.apache.flink.runtime.taskmanager.Task.doRun(Task.java:763) [flink-runtime-1.20.0.jar:1.20.0]
	at org.apache.flink.runtime.taskmanager.Task.run(Task.java:575) [flink-runtime-1.20.0.jar:1.20.0]
	at java.lang.Thread.run(Thread.java:840) [?:?]
2025-08-20 18:58:27.028 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] WARN  [c.f.f.PnLCalculationFunction:180] - 发现无持仓的平仓交易 - 发送到异常流: 交易号=101355625, 合约=JD2504-C-3200, 方向=S, 数量=5
2025-08-20 18:58:27.029 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] WARN  [c.f.f.PnLCalculationFunction:180] - 发现无持仓的平仓交易 - 发送到异常流: 交易号=101355626, 合约=JD2504-C-3200, 方向=S, 数量=5
2025-08-20 18:58:27.029 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] WARN  [c.f.f.PnLCalculationFunction:180] - 发现无持仓的平仓交易 - 发送到异常流: 交易号=101355700, 合约=EB2505, 方向=B, 数量=10
2025-08-20 18:58:27.030 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] WARN  [c.f.f.PnLCalculationFunction:180] - 发现无持仓的平仓交易 - 发送到异常流: 交易号=48060, 合约=SH2505, 方向=S, 数量=1
2025-08-20 18:58:27.030 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] WARN  [c.f.f.PnLCalculationFunction:180] - 发现无持仓的平仓交易 - 发送到异常流: 交易号=100052597, 合约=EB2505, 方向=B, 数量=8
2025-08-20 18:58:27.030 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] WARN  [c.f.f.PnLCalculationFunction:180] - 发现无持仓的平仓交易 - 发送到异常流: 交易号=100052597, 合约=EB2505, 方向=S, 数量=8
2025-08-20 18:58:27.031 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] WARN  [c.f.f.PnLCalculationFunction:180] - 发现无持仓的平仓交易 - 发送到异常流: 交易号=100052678, 合约=EB2505, 方向=B, 数量=1
2025-08-20 18:58:27.031 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] WARN  [c.f.f.PnLCalculationFunction:180] - 发现无持仓的平仓交易 - 发送到异常流: 交易号=100052678, 合约=EB2505, 方向=S, 数量=1
2025-08-20 18:58:27.031 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] WARN  [c.f.f.PnLCalculationFunction:180] - 发现无持仓的平仓交易 - 发送到异常流: 交易号=100052768, 合约=EB2505, 方向=B, 数量=1
2025-08-20 18:58:27.033 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] WARN  [c.f.f.PnLCalculationFunction:180] - 发现无持仓的平仓交易 - 发送到异常流: 交易号=100052770, 合约=EB2505, 方向=B, 数量=1
2025-08-20 18:58:27.033 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] WARN  [c.f.f.PnLCalculationFunction:180] - 发现无持仓的平仓交易 - 发送到异常流: 交易号=100052795, 合约=EB2505, 方向=B, 数量=1
2025-08-20 18:58:27.035 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] WARN  [c.f.f.PnLCalculationFunction:180] - 发现无持仓的平仓交易 - 发送到异常流: 交易号=100052821, 合约=EB2505, 方向=B, 数量=1
2025-08-20 18:58:27.036 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] WARN  [c.f.f.PnLCalculationFunction:180] - 发现无持仓的平仓交易 - 发送到异常流: 交易号=100052822, 合约=EB2505, 方向=B, 数量=2
2025-08-20 18:58:27.037 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] WARN  [c.f.f.PnLCalculationFunction:180] - 发现无持仓的平仓交易 - 发送到异常流: 交易号=100052833, 合约=EB2505, 方向=B, 数量=2
2025-08-20 18:58:27.038 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] WARN  [c.f.f.PnLCalculationFunction:180] - 发现无持仓的平仓交易 - 发送到异常流: 交易号=100052846, 合约=EB2504, 方向=B, 数量=1
2025-08-20 18:58:27.039 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] WARN  [c.f.f.PnLCalculationFunction:180] - 发现无持仓的平仓交易 - 发送到异常流: 交易号=100052847, 合约=EB2505, 方向=B, 数量=1
2025-08-20 18:58:27.040 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] WARN  [c.f.f.PnLCalculationFunction:180] - 发现无持仓的平仓交易 - 发送到异常流: 交易号=100052849, 合约=EB2504, 方向=S, 数量=10
2025-08-20 18:58:27.088 [订单簿重建 (1/1)#0] WARN  [c.f.f.OrderBookReconstructionFunction:345] - [基础层] 订单状态1但剩余量为0，移除: 100257007
2025-08-20 18:58:27.089 [订单簿重建 (1/1)#0] WARN  [c.f.f.OrderBookReconstructionFunction:368] - [基础层] 订单状态3但剩余量为0，移除: 100257004
2025-08-20 18:58:27.095 [订单簿重建 (1/1)#0] WARN  [c.f.f.OrderBookReconstructionFunction:345] - [基础层] 订单状态1但剩余量为0，移除: 100257007
2025-08-20 18:58:27.095 [订单簿重建 (1/1)#0] WARN  [c.f.f.OrderBookReconstructionFunction:368] - [基础层] 订单状态3但剩余量为0，移除: 100257004
2025-08-20 18:58:27.101 [订单簿重建 (1/1)#0] WARN  [c.f.f.OrderBookReconstructionFunction:345] - [基础层] 订单状态1但剩余量为0，移除: 100257007
2025-08-20 18:58:27.101 [订单簿重建 (1/1)#0] WARN  [c.f.f.OrderBookReconstructionFunction:368] - [基础层] 订单状态3但剩余量为0，移除: 100257004
2025-08-20 18:58:27.149 [订单簿重建 (1/1)#0] WARN  [c.f.f.OrderBookReconstructionFunction:345] - [基础层] 订单状态1但剩余量为0，移除: 100257007
2025-08-20 18:58:27.149 [订单簿重建 (1/1)#0] WARN  [c.f.f.OrderBookReconstructionFunction:368] - [基础层] 订单状态3但剩余量为0，移除: 100257004
2025-08-20 18:58:27.153 [订单簿重建 (1/1)#0] WARN  [c.f.f.OrderBookReconstructionFunction:345] - [基础层] 订单状态1但剩余量为0，移除: 100257007
2025-08-20 18:58:27.153 [订单簿重建 (1/1)#0] WARN  [c.f.f.OrderBookReconstructionFunction:368] - [基础层] 订单状态3但剩余量为0，移除: 100257004
2025-08-20 19:01:59.118 [com.futures.job.FuturesOrderBookKafkaJob.main()] WARN  [o.a.f.c.k.s.KafkaSourceBuilder:497] - Property auto.offset.reset is provided but will be overridden from earliest to earliest
2025-08-20 19:01:59.140 [com.futures.job.FuturesOrderBookKafkaJob.main()] WARN  [o.a.f.c.k.s.KafkaSourceBuilder:497] - Property auto.offset.reset is provided but will be overridden from earliest to earliest
2025-08-20 19:01:59.141 [com.futures.job.FuturesOrderBookKafkaJob.main()] WARN  [o.a.f.c.k.s.KafkaSourceBuilder:497] - Property auto.offset.reset is provided but will be overridden from earliest to earliest
2025-08-20 19:01:59.543 [com.futures.job.FuturesOrderBookKafkaJob.main()] WARN  [o.a.f.r.s.t.DefaultDelegationTokenManager:248] - No tokens obtained so skipping notifications
2025-08-20 19:01:59.664 [com.futures.job.FuturesOrderBookKafkaJob.main()] WARN  [o.a.f.r.w.WebMonitorUtils:76] - Log file environment variable 'log.file' is not set.
2025-08-20 19:01:59.664 [com.futures.job.FuturesOrderBookKafkaJob.main()] WARN  [o.a.f.r.w.WebMonitorUtils:82] - JobManager log files are unavailable in the web dashboard. Log file location not found in environment variable 'log.file' or configuration key 'web.log.path'.
2025-08-20 19:01:59.719 [flink-pekko.actor.default-dispatcher-6] WARN  [o.a.f.r.s.t.DefaultDelegationTokenManager:320] - No tokens obtained so skipping notifications
2025-08-20 19:01:59.719 [flink-pekko.actor.default-dispatcher-6] WARN  [o.a.f.r.s.t.DefaultDelegationTokenManager:335] - Tokens update task not started because either no tokens obtained or none of the tokens specified its renewal date
2025-08-20 19:02:15.924 [com.futures.job.FuturesOrderBookKafkaJob.main()] WARN  [o.a.f.c.k.s.KafkaSourceBuilder:497] - Property auto.offset.reset is provided but will be overridden from earliest to earliest
2025-08-20 19:02:15.945 [com.futures.job.FuturesOrderBookKafkaJob.main()] WARN  [o.a.f.c.k.s.KafkaSourceBuilder:497] - Property auto.offset.reset is provided but will be overridden from earliest to earliest
2025-08-20 19:02:15.946 [com.futures.job.FuturesOrderBookKafkaJob.main()] WARN  [o.a.f.c.k.s.KafkaSourceBuilder:497] - Property auto.offset.reset is provided but will be overridden from earliest to earliest
2025-08-20 19:02:16.349 [com.futures.job.FuturesOrderBookKafkaJob.main()] WARN  [o.a.f.r.s.t.DefaultDelegationTokenManager:248] - No tokens obtained so skipping notifications
2025-08-20 19:02:16.466 [com.futures.job.FuturesOrderBookKafkaJob.main()] WARN  [o.a.f.r.w.WebMonitorUtils:76] - Log file environment variable 'log.file' is not set.
2025-08-20 19:02:16.466 [com.futures.job.FuturesOrderBookKafkaJob.main()] WARN  [o.a.f.r.w.WebMonitorUtils:82] - JobManager log files are unavailable in the web dashboard. Log file location not found in environment variable 'log.file' or configuration key 'web.log.path'.
2025-08-20 19:02:16.521 [flink-pekko.actor.default-dispatcher-5] WARN  [o.a.f.r.s.t.DefaultDelegationTokenManager:320] - No tokens obtained so skipping notifications
2025-08-20 19:02:16.521 [flink-pekko.actor.default-dispatcher-5] WARN  [o.a.f.r.s.t.DefaultDelegationTokenManager:335] - Tokens update task not started because either no tokens obtained or none of the tokens specified its renewal date
2025-08-20 19:10:19.679 [com.futures.job.FuturesOrderBookKafkaJob.main()] WARN  [o.a.f.c.k.s.KafkaSourceBuilder:497] - Property auto.offset.reset is provided but will be overridden from earliest to earliest
2025-08-20 19:10:19.700 [com.futures.job.FuturesOrderBookKafkaJob.main()] WARN  [o.a.f.c.k.s.KafkaSourceBuilder:497] - Property auto.offset.reset is provided but will be overridden from earliest to earliest
2025-08-20 19:10:19.701 [com.futures.job.FuturesOrderBookKafkaJob.main()] WARN  [o.a.f.c.k.s.KafkaSourceBuilder:497] - Property auto.offset.reset is provided but will be overridden from earliest to earliest
2025-08-20 19:10:20.498 [com.futures.job.FuturesOrderBookKafkaJob.main()] WARN  [o.a.f.r.s.t.DefaultDelegationTokenManager:248] - No tokens obtained so skipping notifications
2025-08-20 19:10:20.853 [com.futures.job.FuturesOrderBookKafkaJob.main()] WARN  [o.a.f.r.w.WebMonitorUtils:76] - Log file environment variable 'log.file' is not set.
2025-08-20 19:10:20.853 [com.futures.job.FuturesOrderBookKafkaJob.main()] WARN  [o.a.f.r.w.WebMonitorUtils:82] - JobManager log files are unavailable in the web dashboard. Log file location not found in environment variable 'log.file' or configuration key 'web.log.path'.
2025-08-20 19:10:21.014 [flink-pekko.actor.default-dispatcher-6] WARN  [o.a.f.r.s.t.DefaultDelegationTokenManager:320] - No tokens obtained so skipping notifications
2025-08-20 19:10:21.017 [flink-pekko.actor.default-dispatcher-6] WARN  [o.a.f.r.s.t.DefaultDelegationTokenManager:335] - Tokens update task not started because either no tokens obtained or none of the tokens specified its renewal date
2025-08-20 19:46:12.363 [main] WARN  [o.a.f.c.k.s.KafkaSourceBuilder:497] - Property auto.offset.reset is provided but will be overridden from earliest to earliest
2025-08-20 19:46:12.382 [main] WARN  [o.a.f.c.k.s.KafkaSourceBuilder:497] - Property auto.offset.reset is provided but will be overridden from earliest to earliest
2025-08-20 19:46:12.382 [main] WARN  [o.a.f.c.k.s.KafkaSourceBuilder:497] - Property auto.offset.reset is provided but will be overridden from earliest to earliest
2025-08-20 19:46:12.849 [main] WARN  [o.a.f.r.s.t.DefaultDelegationTokenManager:248] - No tokens obtained so skipping notifications
2025-08-20 19:46:12.952 [main] WARN  [o.a.f.r.w.WebMonitorUtils:76] - Log file environment variable 'log.file' is not set.
2025-08-20 19:46:12.952 [main] WARN  [o.a.f.r.w.WebMonitorUtils:82] - JobManager log files are unavailable in the web dashboard. Log file location not found in environment variable 'log.file' or configuration key 'web.log.path'.
2025-08-20 19:46:13.011 [flink-pekko.actor.default-dispatcher-4] WARN  [o.a.f.r.s.t.DefaultDelegationTokenManager:320] - No tokens obtained so skipping notifications
2025-08-20 19:46:13.012 [flink-pekko.actor.default-dispatcher-4] WARN  [o.a.f.r.s.t.DefaultDelegationTokenManager:335] - Tokens update task not started because either no tokens obtained or none of the tokens specified its renewal date
2025-08-20 19:46:13.114 [SourceCoordinator-Source: Kafka-单腿订单] WARN  [o.a.k.c.a.AdminClientConfig:385] - These configurations '[key.deserializer, value.deserializer, max.poll.records, client.id.prefix, group.id, partition.discovery.interval.ms, heartbeat.interval.ms, enable.auto.commit, fetch.max.wait.ms, fetch.min.bytes, session.timeout.ms, auto.offset.reset]' were supplied but are not used yet.
2025-08-20 19:46:13.114 [SourceCoordinator-Source: Kafka-交易] WARN  [o.a.k.c.a.AdminClientConfig:385] - These configurations '[key.deserializer, value.deserializer, max.poll.records, client.id.prefix, group.id, partition.discovery.interval.ms, heartbeat.interval.ms, enable.auto.commit, fetch.max.wait.ms, fetch.min.bytes, session.timeout.ms, auto.offset.reset]' were supplied but are not used yet.
2025-08-20 19:46:13.114 [SourceCoordinator-Source: Kafka-组合订单] WARN  [o.a.k.c.a.AdminClientConfig:385] - These configurations '[key.deserializer, value.deserializer, max.poll.records, client.id.prefix, group.id, partition.discovery.interval.ms, heartbeat.interval.ms, enable.auto.commit, fetch.max.wait.ms, fetch.min.bytes, session.timeout.ms, auto.offset.reset]' were supplied but are not used yet.
2025-08-20 19:46:13.116 [SourceCoordinator-Source: Kafka-组合订单] WARN  [o.a.k.c.u.AppInfoParser:68] - Error registering AppInfo mbean
javax.management.InstanceAlreadyExistsException: kafka.admin.client:type=app-info,id=orderbook-local-test-group-enumerator-admin-client
	at com.sun.jmx.mbeanserver.Repository.addMBean(Repository.java:436) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerWithRepository(DefaultMBeanServerInterceptor.java:1865) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerDynamicMBean(DefaultMBeanServerInterceptor.java:960) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerObject(DefaultMBeanServerInterceptor.java:895) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerMBean(DefaultMBeanServerInterceptor.java:320) ~[?:?]
	at com.sun.jmx.mbeanserver.JmxMBeanServer.registerMBean(JmxMBeanServer.java:523) ~[?:?]
	at org.apache.kafka.common.utils.AppInfoParser.registerAppInfo(AppInfoParser.java:64) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.admin.KafkaAdminClient.<init>(KafkaAdminClient.java:602) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.admin.KafkaAdminClient.createInternal(KafkaAdminClient.java:544) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.admin.KafkaAdminClient.createInternal(KafkaAdminClient.java:488) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.admin.Admin.create(Admin.java:134) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.admin.AdminClient.create(AdminClient.java:39) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.flink.connector.kafka.source.enumerator.KafkaSourceEnumerator.getKafkaAdminClient(KafkaSourceEnumerator.java:441) ~[flink-connector-kafka-3.4.0-1.20.jar:3.4.0-1.20]
	at org.apache.flink.connector.kafka.source.enumerator.KafkaSourceEnumerator.start(KafkaSourceEnumerator.java:164) ~[flink-connector-kafka-3.4.0-1.20.jar:3.4.0-1.20]
	at org.apache.flink.runtime.source.coordinator.SourceCoordinator.lambda$start$1(SourceCoordinator.java:244) ~[flink-runtime-1.20.0.jar:1.20.0]
	at org.apache.flink.runtime.source.coordinator.SourceCoordinator.lambda$runInEventLoop$10(SourceCoordinator.java:533) ~[flink-runtime-1.20.0.jar:1.20.0]
	at org.apache.flink.util.ThrowableCatchingRunnable.run(ThrowableCatchingRunnable.java:40) [flink-core-1.20.0.jar:1.20.0]
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:539) [?:?]
	at java.util.concurrent.FutureTask.run(FutureTask.java:264) [?:?]
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:304) [?:?]
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136) [?:?]
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635) [?:?]
	at java.lang.Thread.run(Thread.java:840) [?:?]
2025-08-20 19:46:13.119 [SourceCoordinator-Source: Kafka-交易] WARN  [o.a.k.c.u.AppInfoParser:68] - Error registering AppInfo mbean
javax.management.InstanceAlreadyExistsException: kafka.admin.client:type=app-info,id=orderbook-local-test-group-enumerator-admin-client
	at com.sun.jmx.mbeanserver.Repository.addMBean(Repository.java:436) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerWithRepository(DefaultMBeanServerInterceptor.java:1865) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerDynamicMBean(DefaultMBeanServerInterceptor.java:960) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerObject(DefaultMBeanServerInterceptor.java:895) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerMBean(DefaultMBeanServerInterceptor.java:320) ~[?:?]
	at com.sun.jmx.mbeanserver.JmxMBeanServer.registerMBean(JmxMBeanServer.java:523) ~[?:?]
	at org.apache.kafka.common.utils.AppInfoParser.registerAppInfo(AppInfoParser.java:64) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.admin.KafkaAdminClient.<init>(KafkaAdminClient.java:602) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.admin.KafkaAdminClient.createInternal(KafkaAdminClient.java:544) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.admin.KafkaAdminClient.createInternal(KafkaAdminClient.java:488) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.admin.Admin.create(Admin.java:134) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.admin.AdminClient.create(AdminClient.java:39) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.flink.connector.kafka.source.enumerator.KafkaSourceEnumerator.getKafkaAdminClient(KafkaSourceEnumerator.java:441) ~[flink-connector-kafka-3.4.0-1.20.jar:3.4.0-1.20]
	at org.apache.flink.connector.kafka.source.enumerator.KafkaSourceEnumerator.start(KafkaSourceEnumerator.java:164) ~[flink-connector-kafka-3.4.0-1.20.jar:3.4.0-1.20]
	at org.apache.flink.runtime.source.coordinator.SourceCoordinator.lambda$start$1(SourceCoordinator.java:244) ~[flink-runtime-1.20.0.jar:1.20.0]
	at org.apache.flink.runtime.source.coordinator.SourceCoordinator.lambda$runInEventLoop$10(SourceCoordinator.java:533) ~[flink-runtime-1.20.0.jar:1.20.0]
	at org.apache.flink.util.ThrowableCatchingRunnable.run(ThrowableCatchingRunnable.java:40) [flink-core-1.20.0.jar:1.20.0]
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:539) [?:?]
	at java.util.concurrent.FutureTask.run(FutureTask.java:264) [?:?]
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:304) [?:?]
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136) [?:?]
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635) [?:?]
	at java.lang.Thread.run(Thread.java:840) [?:?]
2025-08-20 19:46:13.273 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 (1/1)#0] WARN  [o.a.k.c.c.ConsumerConfig:385] - These configurations '[client.id.prefix, partition.discovery.interval.ms]' were supplied but are not used yet.
2025-08-20 19:46:13.273 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 (1/1)#0] WARN  [o.a.k.c.c.ConsumerConfig:385] - These configurations '[client.id.prefix, partition.discovery.interval.ms]' were supplied but are not used yet.
2025-08-20 19:46:13.273 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] WARN  [o.a.k.c.c.ConsumerConfig:385] - These configurations '[client.id.prefix, partition.discovery.interval.ms]' were supplied but are not used yet.
2025-08-20 19:46:13.273 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 (1/1)#0] WARN  [o.a.k.c.u.AppInfoParser:68] - Error registering AppInfo mbean
javax.management.InstanceAlreadyExistsException: kafka.consumer:type=app-info,id=orderbook-local-test-group-0
	at com.sun.jmx.mbeanserver.Repository.addMBean(Repository.java:436) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerWithRepository(DefaultMBeanServerInterceptor.java:1865) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerDynamicMBean(DefaultMBeanServerInterceptor.java:960) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerObject(DefaultMBeanServerInterceptor.java:895) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerMBean(DefaultMBeanServerInterceptor.java:320) ~[?:?]
	at com.sun.jmx.mbeanserver.JmxMBeanServer.registerMBean(JmxMBeanServer.java:523) ~[?:?]
	at org.apache.kafka.common.utils.AppInfoParser.registerAppInfo(AppInfoParser.java:64) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.consumer.KafkaConsumer.<init>(KafkaConsumer.java:821) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.consumer.KafkaConsumer.<init>(KafkaConsumer.java:665) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.consumer.KafkaConsumer.<init>(KafkaConsumer.java:646) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.consumer.KafkaConsumer.<init>(KafkaConsumer.java:626) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.flink.connector.kafka.source.reader.KafkaPartitionSplitReader.<init>(KafkaPartitionSplitReader.java:97) ~[flink-connector-kafka-3.4.0-1.20.jar:3.4.0-1.20]
	at org.apache.flink.connector.kafka.source.KafkaSource.lambda$createReader$1(KafkaSource.java:185) ~[flink-connector-kafka-3.4.0-1.20.jar:3.4.0-1.20]
	at org.apache.flink.connector.base.source.reader.fetcher.SplitFetcherManager.createSplitFetcher(SplitFetcherManager.java:259) ~[flink-connector-base-1.20.0.jar:1.20.0]
	at org.apache.flink.connector.base.source.reader.fetcher.SingleThreadFetcherManager.addSplits(SingleThreadFetcherManager.java:148) ~[flink-connector-base-1.20.0.jar:1.20.0]
	at org.apache.flink.connector.base.source.reader.SourceReaderBase.addSplits(SourceReaderBase.java:315) ~[flink-connector-base-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.api.operators.SourceOperator.handleAddSplitsEvent(SourceOperator.java:606) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.api.operators.SourceOperator.handleOperatorEvent(SourceOperator.java:575) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.OperatorEventDispatcherImpl.dispatchEventToHandlers(OperatorEventDispatcherImpl.java:72) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.RegularOperatorChain.dispatchOperatorEvent(RegularOperatorChain.java:80) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.StreamTask.lambda$dispatchOperatorEvent$24(StreamTask.java:1609) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.StreamTaskActionExecutor$1.runThrowing(StreamTaskActionExecutor.java:50) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.mailbox.Mail.run(Mail.java:101) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.mailbox.MailboxProcessor.runMail(MailboxProcessor.java:414) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.mailbox.MailboxProcessor.processMailsWhenDefaultActionUnavailable(MailboxProcessor.java:383) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.mailbox.MailboxProcessor.processMail(MailboxProcessor.java:368) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.mailbox.MailboxProcessor.runMailboxLoop(MailboxProcessor.java:229) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.StreamTask.runMailboxLoop(StreamTask.java:973) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.StreamTask.invoke(StreamTask.java:917) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.runtime.taskmanager.Task.runWithSystemExitMonitoring(Task.java:970) [flink-runtime-1.20.0.jar:1.20.0]
	at org.apache.flink.runtime.taskmanager.Task.restoreAndInvoke(Task.java:949) [flink-runtime-1.20.0.jar:1.20.0]
	at org.apache.flink.runtime.taskmanager.Task.doRun(Task.java:763) [flink-runtime-1.20.0.jar:1.20.0]
	at org.apache.flink.runtime.taskmanager.Task.run(Task.java:575) [flink-runtime-1.20.0.jar:1.20.0]
	at java.lang.Thread.run(Thread.java:840) [?:?]
2025-08-20 19:46:13.274 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] WARN  [o.a.k.c.u.AppInfoParser:68] - Error registering AppInfo mbean
javax.management.InstanceAlreadyExistsException: kafka.consumer:type=app-info,id=orderbook-local-test-group-0
	at com.sun.jmx.mbeanserver.Repository.addMBean(Repository.java:436) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerWithRepository(DefaultMBeanServerInterceptor.java:1865) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerDynamicMBean(DefaultMBeanServerInterceptor.java:960) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerObject(DefaultMBeanServerInterceptor.java:895) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerMBean(DefaultMBeanServerInterceptor.java:320) ~[?:?]
	at com.sun.jmx.mbeanserver.JmxMBeanServer.registerMBean(JmxMBeanServer.java:523) ~[?:?]
	at org.apache.kafka.common.utils.AppInfoParser.registerAppInfo(AppInfoParser.java:64) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.consumer.KafkaConsumer.<init>(KafkaConsumer.java:821) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.consumer.KafkaConsumer.<init>(KafkaConsumer.java:665) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.consumer.KafkaConsumer.<init>(KafkaConsumer.java:646) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.consumer.KafkaConsumer.<init>(KafkaConsumer.java:626) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.flink.connector.kafka.source.reader.KafkaPartitionSplitReader.<init>(KafkaPartitionSplitReader.java:97) ~[flink-connector-kafka-3.4.0-1.20.jar:3.4.0-1.20]
	at org.apache.flink.connector.kafka.source.KafkaSource.lambda$createReader$1(KafkaSource.java:185) ~[flink-connector-kafka-3.4.0-1.20.jar:3.4.0-1.20]
	at org.apache.flink.connector.base.source.reader.fetcher.SplitFetcherManager.createSplitFetcher(SplitFetcherManager.java:259) ~[flink-connector-base-1.20.0.jar:1.20.0]
	at org.apache.flink.connector.base.source.reader.fetcher.SingleThreadFetcherManager.addSplits(SingleThreadFetcherManager.java:148) ~[flink-connector-base-1.20.0.jar:1.20.0]
	at org.apache.flink.connector.base.source.reader.SourceReaderBase.addSplits(SourceReaderBase.java:315) ~[flink-connector-base-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.api.operators.SourceOperator.handleAddSplitsEvent(SourceOperator.java:606) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.api.operators.SourceOperator.handleOperatorEvent(SourceOperator.java:575) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.OperatorEventDispatcherImpl.dispatchEventToHandlers(OperatorEventDispatcherImpl.java:72) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.RegularOperatorChain.dispatchOperatorEvent(RegularOperatorChain.java:80) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.StreamTask.lambda$dispatchOperatorEvent$24(StreamTask.java:1609) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.StreamTaskActionExecutor$1.runThrowing(StreamTaskActionExecutor.java:50) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.mailbox.Mail.run(Mail.java:101) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.mailbox.MailboxProcessor.runMail(MailboxProcessor.java:414) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.mailbox.MailboxProcessor.processMailsWhenDefaultActionUnavailable(MailboxProcessor.java:383) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.mailbox.MailboxProcessor.processMail(MailboxProcessor.java:368) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.mailbox.MailboxProcessor.runMailboxLoop(MailboxProcessor.java:229) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.StreamTask.runMailboxLoop(StreamTask.java:973) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.StreamTask.invoke(StreamTask.java:917) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.runtime.taskmanager.Task.runWithSystemExitMonitoring(Task.java:970) [flink-runtime-1.20.0.jar:1.20.0]
	at org.apache.flink.runtime.taskmanager.Task.restoreAndInvoke(Task.java:949) [flink-runtime-1.20.0.jar:1.20.0]
	at org.apache.flink.runtime.taskmanager.Task.doRun(Task.java:763) [flink-runtime-1.20.0.jar:1.20.0]
	at org.apache.flink.runtime.taskmanager.Task.run(Task.java:575) [flink-runtime-1.20.0.jar:1.20.0]
	at java.lang.Thread.run(Thread.java:840) [?:?]
2025-08-20 19:46:13.390 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] WARN  [c.f.f.PnLCalculationFunction:180] - 发现无持仓的平仓交易 - 发送到异常流: 交易号=101355625, 合约=JD2504-C-3200, 方向=S, 数量=5
2025-08-20 19:46:13.391 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] WARN  [c.f.f.PnLCalculationFunction:180] - 发现无持仓的平仓交易 - 发送到异常流: 交易号=101355626, 合约=JD2504-C-3200, 方向=S, 数量=5
2025-08-20 19:46:13.392 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] WARN  [c.f.f.PnLCalculationFunction:180] - 发现无持仓的平仓交易 - 发送到异常流: 交易号=101355700, 合约=EB2505, 方向=B, 数量=10
2025-08-20 19:46:13.392 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] WARN  [c.f.f.PnLCalculationFunction:180] - 发现无持仓的平仓交易 - 发送到异常流: 交易号=48060, 合约=SH2505, 方向=S, 数量=1
2025-08-20 19:46:13.392 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] WARN  [c.f.f.PnLCalculationFunction:180] - 发现无持仓的平仓交易 - 发送到异常流: 交易号=100052597, 合约=EB2505, 方向=B, 数量=8
2025-08-20 19:46:13.392 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] WARN  [c.f.f.PnLCalculationFunction:180] - 发现无持仓的平仓交易 - 发送到异常流: 交易号=100052597, 合约=EB2505, 方向=S, 数量=8
2025-08-20 19:46:13.392 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] WARN  [c.f.f.PnLCalculationFunction:180] - 发现无持仓的平仓交易 - 发送到异常流: 交易号=100052678, 合约=EB2505, 方向=B, 数量=1
2025-08-20 19:46:13.393 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] WARN  [c.f.f.PnLCalculationFunction:180] - 发现无持仓的平仓交易 - 发送到异常流: 交易号=100052678, 合约=EB2505, 方向=S, 数量=1
2025-08-20 19:46:13.393 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] WARN  [c.f.f.PnLCalculationFunction:180] - 发现无持仓的平仓交易 - 发送到异常流: 交易号=100052768, 合约=EB2505, 方向=B, 数量=1
2025-08-20 19:46:13.396 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] WARN  [c.f.f.PnLCalculationFunction:180] - 发现无持仓的平仓交易 - 发送到异常流: 交易号=100052770, 合约=EB2505, 方向=B, 数量=1
2025-08-20 19:46:13.396 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] WARN  [c.f.f.PnLCalculationFunction:180] - 发现无持仓的平仓交易 - 发送到异常流: 交易号=100052795, 合约=EB2505, 方向=B, 数量=1
2025-08-20 19:46:13.397 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] WARN  [c.f.f.PnLCalculationFunction:180] - 发现无持仓的平仓交易 - 发送到异常流: 交易号=100052821, 合约=EB2505, 方向=B, 数量=1
2025-08-20 19:46:13.398 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] WARN  [c.f.f.PnLCalculationFunction:180] - 发现无持仓的平仓交易 - 发送到异常流: 交易号=100052822, 合约=EB2505, 方向=B, 数量=2
2025-08-20 19:46:13.399 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] WARN  [c.f.f.PnLCalculationFunction:180] - 发现无持仓的平仓交易 - 发送到异常流: 交易号=100052833, 合约=EB2505, 方向=B, 数量=2
2025-08-20 19:46:13.399 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] WARN  [c.f.f.PnLCalculationFunction:180] - 发现无持仓的平仓交易 - 发送到异常流: 交易号=100052846, 合约=EB2504, 方向=B, 数量=1
2025-08-20 19:46:13.400 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] WARN  [c.f.f.PnLCalculationFunction:180] - 发现无持仓的平仓交易 - 发送到异常流: 交易号=100052847, 合约=EB2505, 方向=B, 数量=1
2025-08-20 19:46:13.400 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] WARN  [c.f.f.PnLCalculationFunction:180] - 发现无持仓的平仓交易 - 发送到异常流: 交易号=100052849, 合约=EB2504, 方向=S, 数量=10
2025-08-20 19:46:40.055 [com.futures.job.FuturesOrderBookKafkaJob.main()] WARN  [o.a.f.c.k.s.KafkaSourceBuilder:497] - Property auto.offset.reset is provided but will be overridden from earliest to earliest
2025-08-20 19:46:40.077 [com.futures.job.FuturesOrderBookKafkaJob.main()] WARN  [o.a.f.c.k.s.KafkaSourceBuilder:497] - Property auto.offset.reset is provided but will be overridden from earliest to earliest
2025-08-20 19:46:40.078 [com.futures.job.FuturesOrderBookKafkaJob.main()] WARN  [o.a.f.c.k.s.KafkaSourceBuilder:497] - Property auto.offset.reset is provided but will be overridden from earliest to earliest
2025-08-20 19:46:40.508 [com.futures.job.FuturesOrderBookKafkaJob.main()] WARN  [o.a.f.r.s.t.DefaultDelegationTokenManager:248] - No tokens obtained so skipping notifications
2025-08-20 19:46:40.617 [com.futures.job.FuturesOrderBookKafkaJob.main()] WARN  [o.a.f.r.w.WebMonitorUtils:76] - Log file environment variable 'log.file' is not set.
2025-08-20 19:46:40.617 [com.futures.job.FuturesOrderBookKafkaJob.main()] WARN  [o.a.f.r.w.WebMonitorUtils:82] - JobManager log files are unavailable in the web dashboard. Log file location not found in environment variable 'log.file' or configuration key 'web.log.path'.
2025-08-20 19:46:40.671 [flink-pekko.actor.default-dispatcher-4] WARN  [o.a.f.r.s.t.DefaultDelegationTokenManager:320] - No tokens obtained so skipping notifications
2025-08-20 19:46:40.671 [flink-pekko.actor.default-dispatcher-4] WARN  [o.a.f.r.s.t.DefaultDelegationTokenManager:335] - Tokens update task not started because either no tokens obtained or none of the tokens specified its renewal date
2025-08-20 19:46:50.633 [com.futures.job.FuturesOrderBookKafkaJob.main()] WARN  [o.a.f.c.k.s.KafkaSourceBuilder:497] - Property auto.offset.reset is provided but will be overridden from earliest to earliest
2025-08-20 19:46:50.655 [com.futures.job.FuturesOrderBookKafkaJob.main()] WARN  [o.a.f.c.k.s.KafkaSourceBuilder:497] - Property auto.offset.reset is provided but will be overridden from earliest to earliest
2025-08-20 19:46:50.655 [com.futures.job.FuturesOrderBookKafkaJob.main()] WARN  [o.a.f.c.k.s.KafkaSourceBuilder:497] - Property auto.offset.reset is provided but will be overridden from earliest to earliest
2025-08-20 19:46:51.065 [com.futures.job.FuturesOrderBookKafkaJob.main()] WARN  [o.a.f.r.s.t.DefaultDelegationTokenManager:248] - No tokens obtained so skipping notifications
2025-08-20 19:46:51.166 [com.futures.job.FuturesOrderBookKafkaJob.main()] WARN  [o.a.f.r.w.WebMonitorUtils:76] - Log file environment variable 'log.file' is not set.
2025-08-20 19:46:51.166 [com.futures.job.FuturesOrderBookKafkaJob.main()] WARN  [o.a.f.r.w.WebMonitorUtils:82] - JobManager log files are unavailable in the web dashboard. Log file location not found in environment variable 'log.file' or configuration key 'web.log.path'.
2025-08-20 19:46:51.215 [flink-pekko.actor.default-dispatcher-4] WARN  [o.a.f.r.s.t.DefaultDelegationTokenManager:320] - No tokens obtained so skipping notifications
2025-08-20 19:46:51.216 [flink-pekko.actor.default-dispatcher-4] WARN  [o.a.f.r.s.t.DefaultDelegationTokenManager:335] - Tokens update task not started because either no tokens obtained or none of the tokens specified its renewal date
2025-08-20 19:48:40.655 [main] WARN  [o.a.f.c.k.s.KafkaSourceBuilder:497] - Property auto.offset.reset is provided but will be overridden from earliest to earliest
2025-08-20 19:48:40.672 [main] WARN  [o.a.f.c.k.s.KafkaSourceBuilder:497] - Property auto.offset.reset is provided but will be overridden from earliest to earliest
2025-08-20 19:48:40.672 [main] WARN  [o.a.f.c.k.s.KafkaSourceBuilder:497] - Property auto.offset.reset is provided but will be overridden from earliest to earliest
2025-08-20 19:48:41.112 [main] WARN  [o.a.f.r.s.t.DefaultDelegationTokenManager:248] - No tokens obtained so skipping notifications
2025-08-20 19:48:41.220 [main] WARN  [o.a.f.r.w.WebMonitorUtils:76] - Log file environment variable 'log.file' is not set.
2025-08-20 19:48:41.220 [main] WARN  [o.a.f.r.w.WebMonitorUtils:82] - JobManager log files are unavailable in the web dashboard. Log file location not found in environment variable 'log.file' or configuration key 'web.log.path'.
2025-08-20 19:48:41.276 [flink-pekko.actor.default-dispatcher-4] WARN  [o.a.f.r.s.t.DefaultDelegationTokenManager:320] - No tokens obtained so skipping notifications
2025-08-20 19:48:41.276 [flink-pekko.actor.default-dispatcher-4] WARN  [o.a.f.r.s.t.DefaultDelegationTokenManager:335] - Tokens update task not started because either no tokens obtained or none of the tokens specified its renewal date
2025-08-20 19:48:41.370 [SourceCoordinator-Source: Kafka-交易] WARN  [o.a.k.c.a.AdminClientConfig:385] - These configurations '[key.deserializer, value.deserializer, max.poll.records, client.id.prefix, group.id, partition.discovery.interval.ms, heartbeat.interval.ms, enable.auto.commit, fetch.max.wait.ms, fetch.min.bytes, session.timeout.ms, auto.offset.reset]' were supplied but are not used yet.
2025-08-20 19:48:41.370 [SourceCoordinator-Source: Kafka-组合订单] WARN  [o.a.k.c.a.AdminClientConfig:385] - These configurations '[key.deserializer, value.deserializer, max.poll.records, client.id.prefix, group.id, partition.discovery.interval.ms, heartbeat.interval.ms, enable.auto.commit, fetch.max.wait.ms, fetch.min.bytes, session.timeout.ms, auto.offset.reset]' were supplied but are not used yet.
2025-08-20 19:48:41.370 [SourceCoordinator-Source: Kafka-单腿订单] WARN  [o.a.k.c.a.AdminClientConfig:385] - These configurations '[key.deserializer, value.deserializer, max.poll.records, client.id.prefix, group.id, partition.discovery.interval.ms, heartbeat.interval.ms, enable.auto.commit, fetch.max.wait.ms, fetch.min.bytes, session.timeout.ms, auto.offset.reset]' were supplied but are not used yet.
2025-08-20 19:48:41.372 [SourceCoordinator-Source: Kafka-单腿订单] WARN  [o.a.k.c.u.AppInfoParser:68] - Error registering AppInfo mbean
javax.management.InstanceAlreadyExistsException: kafka.admin.client:type=app-info,id=orderbook-local-test-group-enumerator-admin-client
	at com.sun.jmx.mbeanserver.Repository.addMBean(Repository.java:436) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerWithRepository(DefaultMBeanServerInterceptor.java:1865) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerDynamicMBean(DefaultMBeanServerInterceptor.java:960) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerObject(DefaultMBeanServerInterceptor.java:895) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerMBean(DefaultMBeanServerInterceptor.java:320) ~[?:?]
	at com.sun.jmx.mbeanserver.JmxMBeanServer.registerMBean(JmxMBeanServer.java:523) ~[?:?]
	at org.apache.kafka.common.utils.AppInfoParser.registerAppInfo(AppInfoParser.java:64) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.admin.KafkaAdminClient.<init>(KafkaAdminClient.java:602) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.admin.KafkaAdminClient.createInternal(KafkaAdminClient.java:544) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.admin.KafkaAdminClient.createInternal(KafkaAdminClient.java:488) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.admin.Admin.create(Admin.java:134) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.admin.AdminClient.create(AdminClient.java:39) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.flink.connector.kafka.source.enumerator.KafkaSourceEnumerator.getKafkaAdminClient(KafkaSourceEnumerator.java:441) ~[flink-connector-kafka-3.4.0-1.20.jar:3.4.0-1.20]
	at org.apache.flink.connector.kafka.source.enumerator.KafkaSourceEnumerator.start(KafkaSourceEnumerator.java:164) ~[flink-connector-kafka-3.4.0-1.20.jar:3.4.0-1.20]
	at org.apache.flink.runtime.source.coordinator.SourceCoordinator.lambda$start$1(SourceCoordinator.java:244) ~[flink-runtime-1.20.0.jar:1.20.0]
	at org.apache.flink.runtime.source.coordinator.SourceCoordinator.lambda$runInEventLoop$10(SourceCoordinator.java:533) ~[flink-runtime-1.20.0.jar:1.20.0]
	at org.apache.flink.util.ThrowableCatchingRunnable.run(ThrowableCatchingRunnable.java:40) [flink-core-1.20.0.jar:1.20.0]
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:539) [?:?]
	at java.util.concurrent.FutureTask.run(FutureTask.java:264) [?:?]
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:304) [?:?]
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136) [?:?]
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635) [?:?]
	at java.lang.Thread.run(Thread.java:840) [?:?]
2025-08-20 19:48:41.376 [SourceCoordinator-Source: Kafka-组合订单] WARN  [o.a.k.c.u.AppInfoParser:68] - Error registering AppInfo mbean
javax.management.InstanceAlreadyExistsException: kafka.admin.client:type=app-info,id=orderbook-local-test-group-enumerator-admin-client
	at com.sun.jmx.mbeanserver.Repository.addMBean(Repository.java:436) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerWithRepository(DefaultMBeanServerInterceptor.java:1865) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerDynamicMBean(DefaultMBeanServerInterceptor.java:960) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerObject(DefaultMBeanServerInterceptor.java:895) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerMBean(DefaultMBeanServerInterceptor.java:320) ~[?:?]
	at com.sun.jmx.mbeanserver.JmxMBeanServer.registerMBean(JmxMBeanServer.java:523) ~[?:?]
	at org.apache.kafka.common.utils.AppInfoParser.registerAppInfo(AppInfoParser.java:64) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.admin.KafkaAdminClient.<init>(KafkaAdminClient.java:602) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.admin.KafkaAdminClient.createInternal(KafkaAdminClient.java:544) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.admin.KafkaAdminClient.createInternal(KafkaAdminClient.java:488) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.admin.Admin.create(Admin.java:134) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.admin.AdminClient.create(AdminClient.java:39) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.flink.connector.kafka.source.enumerator.KafkaSourceEnumerator.getKafkaAdminClient(KafkaSourceEnumerator.java:441) ~[flink-connector-kafka-3.4.0-1.20.jar:3.4.0-1.20]
	at org.apache.flink.connector.kafka.source.enumerator.KafkaSourceEnumerator.start(KafkaSourceEnumerator.java:164) ~[flink-connector-kafka-3.4.0-1.20.jar:3.4.0-1.20]
	at org.apache.flink.runtime.source.coordinator.SourceCoordinator.lambda$start$1(SourceCoordinator.java:244) ~[flink-runtime-1.20.0.jar:1.20.0]
	at org.apache.flink.runtime.source.coordinator.SourceCoordinator.lambda$runInEventLoop$10(SourceCoordinator.java:533) ~[flink-runtime-1.20.0.jar:1.20.0]
	at org.apache.flink.util.ThrowableCatchingRunnable.run(ThrowableCatchingRunnable.java:40) [flink-core-1.20.0.jar:1.20.0]
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:539) [?:?]
	at java.util.concurrent.FutureTask.run(FutureTask.java:264) [?:?]
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:304) [?:?]
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136) [?:?]
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635) [?:?]
	at java.lang.Thread.run(Thread.java:840) [?:?]
2025-08-20 19:48:41.521 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] WARN  [o.a.k.c.c.ConsumerConfig:385] - These configurations '[client.id.prefix, partition.discovery.interval.ms]' were supplied but are not used yet.
2025-08-20 19:48:41.521 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 (1/1)#0] WARN  [o.a.k.c.c.ConsumerConfig:385] - These configurations '[client.id.prefix, partition.discovery.interval.ms]' were supplied but are not used yet.
2025-08-20 19:48:41.521 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 (1/1)#0] WARN  [o.a.k.c.c.ConsumerConfig:385] - These configurations '[client.id.prefix, partition.discovery.interval.ms]' were supplied but are not used yet.
2025-08-20 19:48:41.521 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 (1/1)#0] WARN  [o.a.k.c.u.AppInfoParser:68] - Error registering AppInfo mbean
javax.management.InstanceAlreadyExistsException: kafka.consumer:type=app-info,id=orderbook-local-test-group-0
	at com.sun.jmx.mbeanserver.Repository.addMBean(Repository.java:436) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerWithRepository(DefaultMBeanServerInterceptor.java:1865) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerDynamicMBean(DefaultMBeanServerInterceptor.java:960) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerObject(DefaultMBeanServerInterceptor.java:895) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerMBean(DefaultMBeanServerInterceptor.java:320) ~[?:?]
	at com.sun.jmx.mbeanserver.JmxMBeanServer.registerMBean(JmxMBeanServer.java:523) ~[?:?]
	at org.apache.kafka.common.utils.AppInfoParser.registerAppInfo(AppInfoParser.java:64) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.consumer.KafkaConsumer.<init>(KafkaConsumer.java:821) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.consumer.KafkaConsumer.<init>(KafkaConsumer.java:665) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.consumer.KafkaConsumer.<init>(KafkaConsumer.java:646) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.consumer.KafkaConsumer.<init>(KafkaConsumer.java:626) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.flink.connector.kafka.source.reader.KafkaPartitionSplitReader.<init>(KafkaPartitionSplitReader.java:97) ~[flink-connector-kafka-3.4.0-1.20.jar:3.4.0-1.20]
	at org.apache.flink.connector.kafka.source.KafkaSource.lambda$createReader$1(KafkaSource.java:185) ~[flink-connector-kafka-3.4.0-1.20.jar:3.4.0-1.20]
	at org.apache.flink.connector.base.source.reader.fetcher.SplitFetcherManager.createSplitFetcher(SplitFetcherManager.java:259) ~[flink-connector-base-1.20.0.jar:1.20.0]
	at org.apache.flink.connector.base.source.reader.fetcher.SingleThreadFetcherManager.addSplits(SingleThreadFetcherManager.java:148) ~[flink-connector-base-1.20.0.jar:1.20.0]
	at org.apache.flink.connector.base.source.reader.SourceReaderBase.addSplits(SourceReaderBase.java:315) ~[flink-connector-base-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.api.operators.SourceOperator.handleAddSplitsEvent(SourceOperator.java:606) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.api.operators.SourceOperator.handleOperatorEvent(SourceOperator.java:575) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.OperatorEventDispatcherImpl.dispatchEventToHandlers(OperatorEventDispatcherImpl.java:72) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.RegularOperatorChain.dispatchOperatorEvent(RegularOperatorChain.java:80) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.StreamTask.lambda$dispatchOperatorEvent$24(StreamTask.java:1609) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.StreamTaskActionExecutor$1.runThrowing(StreamTaskActionExecutor.java:50) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.mailbox.Mail.run(Mail.java:101) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.mailbox.MailboxProcessor.runMail(MailboxProcessor.java:414) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.mailbox.MailboxProcessor.processMailsNonBlocking(MailboxProcessor.java:399) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.mailbox.MailboxProcessor.processMail(MailboxProcessor.java:361) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.mailbox.MailboxProcessor.runMailboxLoop(MailboxProcessor.java:229) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.StreamTask.runMailboxLoop(StreamTask.java:973) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.StreamTask.invoke(StreamTask.java:917) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.runtime.taskmanager.Task.runWithSystemExitMonitoring(Task.java:970) [flink-runtime-1.20.0.jar:1.20.0]
	at org.apache.flink.runtime.taskmanager.Task.restoreAndInvoke(Task.java:949) [flink-runtime-1.20.0.jar:1.20.0]
	at org.apache.flink.runtime.taskmanager.Task.doRun(Task.java:763) [flink-runtime-1.20.0.jar:1.20.0]
	at org.apache.flink.runtime.taskmanager.Task.run(Task.java:575) [flink-runtime-1.20.0.jar:1.20.0]
	at java.lang.Thread.run(Thread.java:840) [?:?]
2025-08-20 19:48:41.522 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 (1/1)#0] WARN  [o.a.k.c.u.AppInfoParser:68] - Error registering AppInfo mbean
javax.management.InstanceAlreadyExistsException: kafka.consumer:type=app-info,id=orderbook-local-test-group-0
	at com.sun.jmx.mbeanserver.Repository.addMBean(Repository.java:436) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerWithRepository(DefaultMBeanServerInterceptor.java:1865) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerDynamicMBean(DefaultMBeanServerInterceptor.java:960) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerObject(DefaultMBeanServerInterceptor.java:895) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerMBean(DefaultMBeanServerInterceptor.java:320) ~[?:?]
	at com.sun.jmx.mbeanserver.JmxMBeanServer.registerMBean(JmxMBeanServer.java:523) ~[?:?]
	at org.apache.kafka.common.utils.AppInfoParser.registerAppInfo(AppInfoParser.java:64) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.consumer.KafkaConsumer.<init>(KafkaConsumer.java:821) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.consumer.KafkaConsumer.<init>(KafkaConsumer.java:665) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.consumer.KafkaConsumer.<init>(KafkaConsumer.java:646) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.consumer.KafkaConsumer.<init>(KafkaConsumer.java:626) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.flink.connector.kafka.source.reader.KafkaPartitionSplitReader.<init>(KafkaPartitionSplitReader.java:97) ~[flink-connector-kafka-3.4.0-1.20.jar:3.4.0-1.20]
	at org.apache.flink.connector.kafka.source.KafkaSource.lambda$createReader$1(KafkaSource.java:185) ~[flink-connector-kafka-3.4.0-1.20.jar:3.4.0-1.20]
	at org.apache.flink.connector.base.source.reader.fetcher.SplitFetcherManager.createSplitFetcher(SplitFetcherManager.java:259) ~[flink-connector-base-1.20.0.jar:1.20.0]
	at org.apache.flink.connector.base.source.reader.fetcher.SingleThreadFetcherManager.addSplits(SingleThreadFetcherManager.java:148) ~[flink-connector-base-1.20.0.jar:1.20.0]
	at org.apache.flink.connector.base.source.reader.SourceReaderBase.addSplits(SourceReaderBase.java:315) ~[flink-connector-base-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.api.operators.SourceOperator.handleAddSplitsEvent(SourceOperator.java:606) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.api.operators.SourceOperator.handleOperatorEvent(SourceOperator.java:575) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.OperatorEventDispatcherImpl.dispatchEventToHandlers(OperatorEventDispatcherImpl.java:72) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.RegularOperatorChain.dispatchOperatorEvent(RegularOperatorChain.java:80) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.StreamTask.lambda$dispatchOperatorEvent$24(StreamTask.java:1609) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.StreamTaskActionExecutor$1.runThrowing(StreamTaskActionExecutor.java:50) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.mailbox.Mail.run(Mail.java:101) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.mailbox.MailboxProcessor.runMail(MailboxProcessor.java:414) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.mailbox.MailboxProcessor.processMailsNonBlocking(MailboxProcessor.java:399) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.mailbox.MailboxProcessor.processMail(MailboxProcessor.java:361) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.mailbox.MailboxProcessor.runMailboxLoop(MailboxProcessor.java:229) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.StreamTask.runMailboxLoop(StreamTask.java:973) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.StreamTask.invoke(StreamTask.java:917) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.runtime.taskmanager.Task.runWithSystemExitMonitoring(Task.java:970) [flink-runtime-1.20.0.jar:1.20.0]
	at org.apache.flink.runtime.taskmanager.Task.restoreAndInvoke(Task.java:949) [flink-runtime-1.20.0.jar:1.20.0]
	at org.apache.flink.runtime.taskmanager.Task.doRun(Task.java:763) [flink-runtime-1.20.0.jar:1.20.0]
	at org.apache.flink.runtime.taskmanager.Task.run(Task.java:575) [flink-runtime-1.20.0.jar:1.20.0]
	at java.lang.Thread.run(Thread.java:840) [?:?]
2025-08-20 19:48:41.639 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] WARN  [c.f.f.PnLCalculationFunction:180] - 发现无持仓的平仓交易 - 发送到异常流: 交易号=101355625, 合约=JD2504-C-3200, 方向=S, 数量=5
2025-08-20 19:48:41.639 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] WARN  [c.f.f.PnLCalculationFunction:180] - 发现无持仓的平仓交易 - 发送到异常流: 交易号=101355626, 合约=JD2504-C-3200, 方向=S, 数量=5
2025-08-20 19:48:41.640 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] WARN  [c.f.f.PnLCalculationFunction:180] - 发现无持仓的平仓交易 - 发送到异常流: 交易号=101355700, 合约=EB2505, 方向=B, 数量=10
2025-08-20 19:48:41.640 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] WARN  [c.f.f.PnLCalculationFunction:180] - 发现无持仓的平仓交易 - 发送到异常流: 交易号=48060, 合约=SH2505, 方向=S, 数量=1
2025-08-20 19:48:41.640 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] WARN  [c.f.f.PnLCalculationFunction:180] - 发现无持仓的平仓交易 - 发送到异常流: 交易号=100052597, 合约=EB2505, 方向=B, 数量=8
2025-08-20 19:48:41.640 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] WARN  [c.f.f.PnLCalculationFunction:180] - 发现无持仓的平仓交易 - 发送到异常流: 交易号=100052597, 合约=EB2505, 方向=S, 数量=8
2025-08-20 19:48:41.640 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] WARN  [c.f.f.PnLCalculationFunction:180] - 发现无持仓的平仓交易 - 发送到异常流: 交易号=100052678, 合约=EB2505, 方向=B, 数量=1
2025-08-20 19:48:41.640 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] WARN  [c.f.f.PnLCalculationFunction:180] - 发现无持仓的平仓交易 - 发送到异常流: 交易号=100052678, 合约=EB2505, 方向=S, 数量=1
2025-08-20 19:48:41.641 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] WARN  [c.f.f.PnLCalculationFunction:180] - 发现无持仓的平仓交易 - 发送到异常流: 交易号=100052768, 合约=EB2505, 方向=B, 数量=1
2025-08-20 19:48:41.645 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] WARN  [c.f.f.PnLCalculationFunction:180] - 发现无持仓的平仓交易 - 发送到异常流: 交易号=100052770, 合约=EB2505, 方向=B, 数量=1
2025-08-20 19:48:41.645 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] WARN  [c.f.f.PnLCalculationFunction:180] - 发现无持仓的平仓交易 - 发送到异常流: 交易号=100052795, 合约=EB2505, 方向=B, 数量=1
2025-08-20 19:48:41.648 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] WARN  [c.f.f.PnLCalculationFunction:180] - 发现无持仓的平仓交易 - 发送到异常流: 交易号=100052821, 合约=EB2505, 方向=B, 数量=1
2025-08-20 19:48:41.648 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] WARN  [c.f.f.PnLCalculationFunction:180] - 发现无持仓的平仓交易 - 发送到异常流: 交易号=100052822, 合约=EB2505, 方向=B, 数量=2
2025-08-20 19:48:41.649 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] WARN  [c.f.f.PnLCalculationFunction:180] - 发现无持仓的平仓交易 - 发送到异常流: 交易号=100052833, 合约=EB2505, 方向=B, 数量=2
2025-08-20 19:48:41.649 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] WARN  [c.f.f.PnLCalculationFunction:180] - 发现无持仓的平仓交易 - 发送到异常流: 交易号=100052846, 合约=EB2504, 方向=B, 数量=1
2025-08-20 19:48:41.649 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] WARN  [c.f.f.PnLCalculationFunction:180] - 发现无持仓的平仓交易 - 发送到异常流: 交易号=100052847, 合约=EB2505, 方向=B, 数量=1
2025-08-20 19:48:41.650 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] WARN  [c.f.f.PnLCalculationFunction:180] - 发现无持仓的平仓交易 - 发送到异常流: 交易号=100052849, 合约=EB2504, 方向=S, 数量=10
2025-08-20 19:49:42.452 [main] WARN  [o.a.f.c.k.s.KafkaSourceBuilder:497] - Property auto.offset.reset is provided but will be overridden from earliest to earliest
2025-08-20 19:49:48.852 [main] WARN  [o.a.f.c.k.s.KafkaSourceBuilder:497] - Property auto.offset.reset is provided but will be overridden from earliest to earliest
2025-08-20 19:49:50.855 [main] WARN  [o.a.f.c.k.s.KafkaSourceBuilder:497] - Property auto.offset.reset is provided but will be overridden from earliest to earliest
2025-08-20 19:49:51.367 [main] WARN  [o.a.f.r.s.t.DefaultDelegationTokenManager:248] - No tokens obtained so skipping notifications
2025-08-20 19:49:51.529 [main] WARN  [o.a.f.r.w.WebMonitorUtils:76] - Log file environment variable 'log.file' is not set.
2025-08-20 19:49:51.530 [main] WARN  [o.a.f.r.w.WebMonitorUtils:82] - JobManager log files are unavailable in the web dashboard. Log file location not found in environment variable 'log.file' or configuration key 'web.log.path'.
2025-08-20 19:49:51.593 [flink-pekko.actor.default-dispatcher-4] WARN  [o.a.f.r.s.t.DefaultDelegationTokenManager:320] - No tokens obtained so skipping notifications
2025-08-20 19:49:51.593 [flink-pekko.actor.default-dispatcher-4] WARN  [o.a.f.r.s.t.DefaultDelegationTokenManager:335] - Tokens update task not started because either no tokens obtained or none of the tokens specified its renewal date
2025-08-20 19:49:51.706 [SourceCoordinator-Source: Kafka-单腿订单] WARN  [o.a.k.c.a.AdminClientConfig:385] - These configurations '[key.deserializer, value.deserializer, max.poll.records, client.id.prefix, group.id, partition.discovery.interval.ms, heartbeat.interval.ms, enable.auto.commit, fetch.max.wait.ms, fetch.min.bytes, session.timeout.ms, auto.offset.reset]' were supplied but are not used yet.
2025-08-20 19:49:51.706 [SourceCoordinator-Source: Kafka-组合订单] WARN  [o.a.k.c.a.AdminClientConfig:385] - These configurations '[key.deserializer, value.deserializer, max.poll.records, client.id.prefix, group.id, partition.discovery.interval.ms, heartbeat.interval.ms, enable.auto.commit, fetch.max.wait.ms, fetch.min.bytes, session.timeout.ms, auto.offset.reset]' were supplied but are not used yet.
2025-08-20 19:49:51.706 [SourceCoordinator-Source: Kafka-交易] WARN  [o.a.k.c.a.AdminClientConfig:385] - These configurations '[key.deserializer, value.deserializer, max.poll.records, client.id.prefix, group.id, partition.discovery.interval.ms, heartbeat.interval.ms, enable.auto.commit, fetch.max.wait.ms, fetch.min.bytes, session.timeout.ms, auto.offset.reset]' were supplied but are not used yet.
2025-08-20 19:49:51.707 [SourceCoordinator-Source: Kafka-交易] WARN  [o.a.k.c.u.AppInfoParser:68] - Error registering AppInfo mbean
javax.management.InstanceAlreadyExistsException: kafka.admin.client:type=app-info,id=orderbook-local-test-group-enumerator-admin-client
	at com.sun.jmx.mbeanserver.Repository.addMBean(Repository.java:436) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerWithRepository(DefaultMBeanServerInterceptor.java:1865) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerDynamicMBean(DefaultMBeanServerInterceptor.java:960) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerObject(DefaultMBeanServerInterceptor.java:895) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerMBean(DefaultMBeanServerInterceptor.java:320) ~[?:?]
	at com.sun.jmx.mbeanserver.JmxMBeanServer.registerMBean(JmxMBeanServer.java:523) ~[?:?]
	at org.apache.kafka.common.utils.AppInfoParser.registerAppInfo(AppInfoParser.java:64) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.admin.KafkaAdminClient.<init>(KafkaAdminClient.java:602) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.admin.KafkaAdminClient.createInternal(KafkaAdminClient.java:544) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.admin.KafkaAdminClient.createInternal(KafkaAdminClient.java:488) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.admin.Admin.create(Admin.java:134) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.admin.AdminClient.create(AdminClient.java:39) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.flink.connector.kafka.source.enumerator.KafkaSourceEnumerator.getKafkaAdminClient(KafkaSourceEnumerator.java:441) ~[flink-connector-kafka-3.4.0-1.20.jar:3.4.0-1.20]
	at org.apache.flink.connector.kafka.source.enumerator.KafkaSourceEnumerator.start(KafkaSourceEnumerator.java:164) ~[flink-connector-kafka-3.4.0-1.20.jar:3.4.0-1.20]
	at org.apache.flink.runtime.source.coordinator.SourceCoordinator.lambda$start$1(SourceCoordinator.java:244) ~[flink-runtime-1.20.0.jar:1.20.0]
	at org.apache.flink.runtime.source.coordinator.SourceCoordinator.lambda$runInEventLoop$10(SourceCoordinator.java:533) ~[flink-runtime-1.20.0.jar:1.20.0]
	at org.apache.flink.util.ThrowableCatchingRunnable.run(ThrowableCatchingRunnable.java:40) [flink-core-1.20.0.jar:1.20.0]
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:539) [?:?]
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:264) [?:?]
	at java.util.concurrent.FutureTask.run(FutureTask.java) [?:?]
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:304) [?:?]
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136) [?:?]
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635) [?:?]
	at java.lang.Thread.run(Thread.java:840) [?:?]
2025-08-20 19:49:51.711 [SourceCoordinator-Source: Kafka-组合订单] WARN  [o.a.k.c.u.AppInfoParser:68] - Error registering AppInfo mbean
javax.management.InstanceAlreadyExistsException: kafka.admin.client:type=app-info,id=orderbook-local-test-group-enumerator-admin-client
	at com.sun.jmx.mbeanserver.Repository.addMBean(Repository.java:436) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerWithRepository(DefaultMBeanServerInterceptor.java:1865) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerDynamicMBean(DefaultMBeanServerInterceptor.java:960) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerObject(DefaultMBeanServerInterceptor.java:895) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerMBean(DefaultMBeanServerInterceptor.java:320) ~[?:?]
	at com.sun.jmx.mbeanserver.JmxMBeanServer.registerMBean(JmxMBeanServer.java:523) ~[?:?]
	at org.apache.kafka.common.utils.AppInfoParser.registerAppInfo(AppInfoParser.java:64) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.admin.KafkaAdminClient.<init>(KafkaAdminClient.java:602) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.admin.KafkaAdminClient.createInternal(KafkaAdminClient.java:544) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.admin.KafkaAdminClient.createInternal(KafkaAdminClient.java:488) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.admin.Admin.create(Admin.java:134) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.admin.AdminClient.create(AdminClient.java:39) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.flink.connector.kafka.source.enumerator.KafkaSourceEnumerator.getKafkaAdminClient(KafkaSourceEnumerator.java:441) ~[flink-connector-kafka-3.4.0-1.20.jar:3.4.0-1.20]
	at org.apache.flink.connector.kafka.source.enumerator.KafkaSourceEnumerator.start(KafkaSourceEnumerator.java:164) ~[flink-connector-kafka-3.4.0-1.20.jar:3.4.0-1.20]
	at org.apache.flink.runtime.source.coordinator.SourceCoordinator.lambda$start$1(SourceCoordinator.java:244) ~[flink-runtime-1.20.0.jar:1.20.0]
	at org.apache.flink.runtime.source.coordinator.SourceCoordinator.lambda$runInEventLoop$10(SourceCoordinator.java:533) ~[flink-runtime-1.20.0.jar:1.20.0]
	at org.apache.flink.util.ThrowableCatchingRunnable.run(ThrowableCatchingRunnable.java:40) [flink-core-1.20.0.jar:1.20.0]
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:539) [?:?]
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:264) [?:?]
	at java.util.concurrent.FutureTask.run(FutureTask.java) [?:?]
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:304) [?:?]
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136) [?:?]
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635) [?:?]
	at java.lang.Thread.run(Thread.java:840) [?:?]
2025-08-20 19:49:51.890 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 (1/1)#0] WARN  [o.a.k.c.c.ConsumerConfig:385] - These configurations '[client.id.prefix, partition.discovery.interval.ms]' were supplied but are not used yet.
2025-08-20 19:49:51.890 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] WARN  [o.a.k.c.c.ConsumerConfig:385] - These configurations '[client.id.prefix, partition.discovery.interval.ms]' were supplied but are not used yet.
2025-08-20 19:49:51.890 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 (1/1)#0] WARN  [o.a.k.c.c.ConsumerConfig:385] - These configurations '[client.id.prefix, partition.discovery.interval.ms]' were supplied but are not used yet.
2025-08-20 19:49:51.890 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] WARN  [o.a.k.c.u.AppInfoParser:68] - Error registering AppInfo mbean
javax.management.InstanceAlreadyExistsException: kafka.consumer:type=app-info,id=orderbook-local-test-group-0
	at com.sun.jmx.mbeanserver.Repository.addMBean(Repository.java:436) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerWithRepository(DefaultMBeanServerInterceptor.java:1865) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerDynamicMBean(DefaultMBeanServerInterceptor.java:960) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerObject(DefaultMBeanServerInterceptor.java:895) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerMBean(DefaultMBeanServerInterceptor.java:320) ~[?:?]
	at com.sun.jmx.mbeanserver.JmxMBeanServer.registerMBean(JmxMBeanServer.java:523) ~[?:?]
	at org.apache.kafka.common.utils.AppInfoParser.registerAppInfo(AppInfoParser.java:64) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.consumer.KafkaConsumer.<init>(KafkaConsumer.java:821) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.consumer.KafkaConsumer.<init>(KafkaConsumer.java:665) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.consumer.KafkaConsumer.<init>(KafkaConsumer.java:646) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.consumer.KafkaConsumer.<init>(KafkaConsumer.java:626) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.flink.connector.kafka.source.reader.KafkaPartitionSplitReader.<init>(KafkaPartitionSplitReader.java:97) ~[flink-connector-kafka-3.4.0-1.20.jar:3.4.0-1.20]
	at org.apache.flink.connector.kafka.source.KafkaSource.lambda$createReader$1(KafkaSource.java:185) ~[flink-connector-kafka-3.4.0-1.20.jar:3.4.0-1.20]
	at org.apache.flink.connector.base.source.reader.fetcher.SplitFetcherManager.createSplitFetcher(SplitFetcherManager.java:259) ~[flink-connector-base-1.20.0.jar:1.20.0]
	at org.apache.flink.connector.base.source.reader.fetcher.SingleThreadFetcherManager.addSplits(SingleThreadFetcherManager.java:148) ~[flink-connector-base-1.20.0.jar:1.20.0]
	at org.apache.flink.connector.base.source.reader.SourceReaderBase.addSplits(SourceReaderBase.java:315) ~[flink-connector-base-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.api.operators.SourceOperator.handleAddSplitsEvent(SourceOperator.java:606) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.api.operators.SourceOperator.handleOperatorEvent(SourceOperator.java:575) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.OperatorEventDispatcherImpl.dispatchEventToHandlers(OperatorEventDispatcherImpl.java:72) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.RegularOperatorChain.dispatchOperatorEvent(RegularOperatorChain.java:80) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.StreamTask.lambda$dispatchOperatorEvent$24(StreamTask.java:1609) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.StreamTaskActionExecutor$1.runThrowing(StreamTaskActionExecutor.java:50) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.mailbox.Mail.run(Mail.java:101) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.mailbox.MailboxProcessor.runMail(MailboxProcessor.java:414) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.mailbox.MailboxProcessor.processMailsNonBlocking(MailboxProcessor.java:399) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.mailbox.MailboxProcessor.processMail(MailboxProcessor.java:361) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.mailbox.MailboxProcessor.runMailboxLoop(MailboxProcessor.java:229) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.StreamTask.runMailboxLoop(StreamTask.java:973) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.StreamTask.invoke(StreamTask.java:917) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.runtime.taskmanager.Task.runWithSystemExitMonitoring(Task.java:970) [flink-runtime-1.20.0.jar:1.20.0]
	at org.apache.flink.runtime.taskmanager.Task.restoreAndInvoke(Task.java:949) [flink-runtime-1.20.0.jar:1.20.0]
	at org.apache.flink.runtime.taskmanager.Task.doRun(Task.java:763) [flink-runtime-1.20.0.jar:1.20.0]
	at org.apache.flink.runtime.taskmanager.Task.run(Task.java:575) [flink-runtime-1.20.0.jar:1.20.0]
	at java.lang.Thread.run(Thread.java:840) [?:?]
2025-08-20 19:49:51.891 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 (1/1)#0] WARN  [o.a.k.c.u.AppInfoParser:68] - Error registering AppInfo mbean
javax.management.InstanceAlreadyExistsException: kafka.consumer:type=app-info,id=orderbook-local-test-group-0
	at com.sun.jmx.mbeanserver.Repository.addMBean(Repository.java:436) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerWithRepository(DefaultMBeanServerInterceptor.java:1865) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerDynamicMBean(DefaultMBeanServerInterceptor.java:960) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerObject(DefaultMBeanServerInterceptor.java:895) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerMBean(DefaultMBeanServerInterceptor.java:320) ~[?:?]
	at com.sun.jmx.mbeanserver.JmxMBeanServer.registerMBean(JmxMBeanServer.java:523) ~[?:?]
	at org.apache.kafka.common.utils.AppInfoParser.registerAppInfo(AppInfoParser.java:64) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.consumer.KafkaConsumer.<init>(KafkaConsumer.java:821) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.consumer.KafkaConsumer.<init>(KafkaConsumer.java:665) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.consumer.KafkaConsumer.<init>(KafkaConsumer.java:646) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.consumer.KafkaConsumer.<init>(KafkaConsumer.java:626) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.flink.connector.kafka.source.reader.KafkaPartitionSplitReader.<init>(KafkaPartitionSplitReader.java:97) ~[flink-connector-kafka-3.4.0-1.20.jar:3.4.0-1.20]
	at org.apache.flink.connector.kafka.source.KafkaSource.lambda$createReader$1(KafkaSource.java:185) ~[flink-connector-kafka-3.4.0-1.20.jar:3.4.0-1.20]
	at org.apache.flink.connector.base.source.reader.fetcher.SplitFetcherManager.createSplitFetcher(SplitFetcherManager.java:259) ~[flink-connector-base-1.20.0.jar:1.20.0]
	at org.apache.flink.connector.base.source.reader.fetcher.SingleThreadFetcherManager.addSplits(SingleThreadFetcherManager.java:148) ~[flink-connector-base-1.20.0.jar:1.20.0]
	at org.apache.flink.connector.base.source.reader.SourceReaderBase.addSplits(SourceReaderBase.java:315) ~[flink-connector-base-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.api.operators.SourceOperator.handleAddSplitsEvent(SourceOperator.java:606) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.api.operators.SourceOperator.handleOperatorEvent(SourceOperator.java:575) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.OperatorEventDispatcherImpl.dispatchEventToHandlers(OperatorEventDispatcherImpl.java:72) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.RegularOperatorChain.dispatchOperatorEvent(RegularOperatorChain.java:80) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.StreamTask.lambda$dispatchOperatorEvent$24(StreamTask.java:1609) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.StreamTaskActionExecutor$1.runThrowing(StreamTaskActionExecutor.java:50) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.mailbox.Mail.run(Mail.java:101) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.mailbox.MailboxProcessor.runMail(MailboxProcessor.java:414) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.mailbox.MailboxProcessor.processMailsNonBlocking(MailboxProcessor.java:399) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.mailbox.MailboxProcessor.processMail(MailboxProcessor.java:361) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.mailbox.MailboxProcessor.runMailboxLoop(MailboxProcessor.java:229) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.StreamTask.runMailboxLoop(StreamTask.java:973) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.StreamTask.invoke(StreamTask.java:917) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.runtime.taskmanager.Task.runWithSystemExitMonitoring(Task.java:970) [flink-runtime-1.20.0.jar:1.20.0]
	at org.apache.flink.runtime.taskmanager.Task.restoreAndInvoke(Task.java:949) [flink-runtime-1.20.0.jar:1.20.0]
	at org.apache.flink.runtime.taskmanager.Task.doRun(Task.java:763) [flink-runtime-1.20.0.jar:1.20.0]
	at org.apache.flink.runtime.taskmanager.Task.run(Task.java:575) [flink-runtime-1.20.0.jar:1.20.0]
	at java.lang.Thread.run(Thread.java:840) [?:?]
2025-08-20 19:49:52.025 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] WARN  [c.f.f.PnLCalculationFunction:180] - 发现无持仓的平仓交易 - 发送到异常流: 交易号=101355625, 合约=JD2504-C-3200, 方向=S, 数量=5
2025-08-20 19:49:52.026 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] WARN  [c.f.f.PnLCalculationFunction:180] - 发现无持仓的平仓交易 - 发送到异常流: 交易号=101355626, 合约=JD2504-C-3200, 方向=S, 数量=5
2025-08-20 19:49:52.027 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] WARN  [c.f.f.PnLCalculationFunction:180] - 发现无持仓的平仓交易 - 发送到异常流: 交易号=101355700, 合约=EB2505, 方向=B, 数量=10
2025-08-20 19:49:52.027 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] WARN  [c.f.f.PnLCalculationFunction:180] - 发现无持仓的平仓交易 - 发送到异常流: 交易号=48060, 合约=SH2505, 方向=S, 数量=1
2025-08-20 19:49:52.027 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] WARN  [c.f.f.PnLCalculationFunction:180] - 发现无持仓的平仓交易 - 发送到异常流: 交易号=100052597, 合约=EB2505, 方向=B, 数量=8
2025-08-20 19:49:52.028 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] WARN  [c.f.f.PnLCalculationFunction:180] - 发现无持仓的平仓交易 - 发送到异常流: 交易号=100052597, 合约=EB2505, 方向=S, 数量=8
2025-08-20 19:49:52.028 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] WARN  [c.f.f.PnLCalculationFunction:180] - 发现无持仓的平仓交易 - 发送到异常流: 交易号=100052678, 合约=EB2505, 方向=B, 数量=1
2025-08-20 19:49:52.028 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] WARN  [c.f.f.PnLCalculationFunction:180] - 发现无持仓的平仓交易 - 发送到异常流: 交易号=100052678, 合约=EB2505, 方向=S, 数量=1
2025-08-20 19:49:52.028 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] WARN  [c.f.f.PnLCalculationFunction:180] - 发现无持仓的平仓交易 - 发送到异常流: 交易号=100052768, 合约=EB2505, 方向=B, 数量=1
2025-08-20 19:49:52.030 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] WARN  [c.f.f.PnLCalculationFunction:180] - 发现无持仓的平仓交易 - 发送到异常流: 交易号=100052770, 合约=EB2505, 方向=B, 数量=1
2025-08-20 19:49:52.030 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] WARN  [c.f.f.PnLCalculationFunction:180] - 发现无持仓的平仓交易 - 发送到异常流: 交易号=100052795, 合约=EB2505, 方向=B, 数量=1
2025-08-20 19:49:52.035 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] WARN  [c.f.f.PnLCalculationFunction:180] - 发现无持仓的平仓交易 - 发送到异常流: 交易号=100052821, 合约=EB2505, 方向=B, 数量=1
2025-08-20 19:49:52.036 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] WARN  [c.f.f.PnLCalculationFunction:180] - 发现无持仓的平仓交易 - 发送到异常流: 交易号=100052822, 合约=EB2505, 方向=B, 数量=2
2025-08-20 19:49:52.039 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] WARN  [c.f.f.PnLCalculationFunction:180] - 发现无持仓的平仓交易 - 发送到异常流: 交易号=100052833, 合约=EB2505, 方向=B, 数量=2
2025-08-20 19:49:52.040 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] WARN  [c.f.f.PnLCalculationFunction:180] - 发现无持仓的平仓交易 - 发送到异常流: 交易号=100052846, 合约=EB2504, 方向=B, 数量=1
2025-08-20 19:49:52.040 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] WARN  [c.f.f.PnLCalculationFunction:180] - 发现无持仓的平仓交易 - 发送到异常流: 交易号=100052847, 合约=EB2505, 方向=B, 数量=1
2025-08-20 19:49:52.041 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] WARN  [c.f.f.PnLCalculationFunction:180] - 发现无持仓的平仓交易 - 发送到异常流: 交易号=100052849, 合约=EB2504, 方向=S, 数量=10
2025-08-20 19:50:23.366 [main] WARN  [o.a.f.c.k.s.KafkaSourceBuilder:497] - Property auto.offset.reset is provided but will be overridden from earliest to earliest
2025-08-20 19:50:23.382 [main] WARN  [o.a.f.c.k.s.KafkaSourceBuilder:497] - Property auto.offset.reset is provided but will be overridden from earliest to earliest
2025-08-20 19:50:23.383 [main] WARN  [o.a.f.c.k.s.KafkaSourceBuilder:497] - Property auto.offset.reset is provided but will be overridden from earliest to earliest
2025-08-20 19:50:23.862 [main] WARN  [o.a.f.r.s.t.DefaultDelegationTokenManager:248] - No tokens obtained so skipping notifications
2025-08-20 19:50:23.969 [main] WARN  [o.a.f.r.w.WebMonitorUtils:76] - Log file environment variable 'log.file' is not set.
2025-08-20 19:50:23.969 [main] WARN  [o.a.f.r.w.WebMonitorUtils:82] - JobManager log files are unavailable in the web dashboard. Log file location not found in environment variable 'log.file' or configuration key 'web.log.path'.
2025-08-20 19:50:24.029 [flink-pekko.actor.default-dispatcher-4] WARN  [o.a.f.r.s.t.DefaultDelegationTokenManager:320] - No tokens obtained so skipping notifications
2025-08-20 19:50:24.029 [flink-pekko.actor.default-dispatcher-4] WARN  [o.a.f.r.s.t.DefaultDelegationTokenManager:335] - Tokens update task not started because either no tokens obtained or none of the tokens specified its renewal date
2025-08-20 19:50:24.129 [SourceCoordinator-Source: Kafka-单腿订单] WARN  [o.a.k.c.a.AdminClientConfig:385] - These configurations '[key.deserializer, value.deserializer, max.poll.records, client.id.prefix, group.id, partition.discovery.interval.ms, heartbeat.interval.ms, enable.auto.commit, fetch.max.wait.ms, fetch.min.bytes, session.timeout.ms, auto.offset.reset]' were supplied but are not used yet.
2025-08-20 19:50:24.129 [SourceCoordinator-Source: Kafka-组合订单] WARN  [o.a.k.c.a.AdminClientConfig:385] - These configurations '[key.deserializer, value.deserializer, max.poll.records, client.id.prefix, group.id, partition.discovery.interval.ms, heartbeat.interval.ms, enable.auto.commit, fetch.max.wait.ms, fetch.min.bytes, session.timeout.ms, auto.offset.reset]' were supplied but are not used yet.
2025-08-20 19:50:24.129 [SourceCoordinator-Source: Kafka-交易] WARN  [o.a.k.c.a.AdminClientConfig:385] - These configurations '[key.deserializer, value.deserializer, max.poll.records, client.id.prefix, group.id, partition.discovery.interval.ms, heartbeat.interval.ms, enable.auto.commit, fetch.max.wait.ms, fetch.min.bytes, session.timeout.ms, auto.offset.reset]' were supplied but are not used yet.
2025-08-20 19:50:24.130 [SourceCoordinator-Source: Kafka-组合订单] WARN  [o.a.k.c.u.AppInfoParser:68] - Error registering AppInfo mbean
javax.management.InstanceAlreadyExistsException: kafka.admin.client:type=app-info,id=orderbook-local-test-group-enumerator-admin-client
	at com.sun.jmx.mbeanserver.Repository.addMBean(Repository.java:436) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerWithRepository(DefaultMBeanServerInterceptor.java:1865) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerDynamicMBean(DefaultMBeanServerInterceptor.java:960) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerObject(DefaultMBeanServerInterceptor.java:895) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerMBean(DefaultMBeanServerInterceptor.java:320) ~[?:?]
	at com.sun.jmx.mbeanserver.JmxMBeanServer.registerMBean(JmxMBeanServer.java:523) ~[?:?]
	at org.apache.kafka.common.utils.AppInfoParser.registerAppInfo(AppInfoParser.java:64) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.admin.KafkaAdminClient.<init>(KafkaAdminClient.java:602) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.admin.KafkaAdminClient.createInternal(KafkaAdminClient.java:544) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.admin.KafkaAdminClient.createInternal(KafkaAdminClient.java:488) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.admin.Admin.create(Admin.java:134) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.admin.AdminClient.create(AdminClient.java:39) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.flink.connector.kafka.source.enumerator.KafkaSourceEnumerator.getKafkaAdminClient(KafkaSourceEnumerator.java:441) ~[flink-connector-kafka-3.4.0-1.20.jar:3.4.0-1.20]
	at org.apache.flink.connector.kafka.source.enumerator.KafkaSourceEnumerator.start(KafkaSourceEnumerator.java:164) ~[flink-connector-kafka-3.4.0-1.20.jar:3.4.0-1.20]
	at org.apache.flink.runtime.source.coordinator.SourceCoordinator.lambda$start$1(SourceCoordinator.java:244) ~[flink-runtime-1.20.0.jar:1.20.0]
	at org.apache.flink.runtime.source.coordinator.SourceCoordinator.lambda$runInEventLoop$10(SourceCoordinator.java:533) ~[flink-runtime-1.20.0.jar:1.20.0]
	at org.apache.flink.util.ThrowableCatchingRunnable.run(ThrowableCatchingRunnable.java:40) [flink-core-1.20.0.jar:1.20.0]
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:539) [?:?]
	at java.util.concurrent.FutureTask.run(FutureTask.java:264) [?:?]
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:304) [?:?]
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136) [?:?]
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635) [?:?]
	at java.lang.Thread.run(Thread.java:840) [?:?]
2025-08-20 19:50:24.133 [SourceCoordinator-Source: Kafka-交易] WARN  [o.a.k.c.u.AppInfoParser:68] - Error registering AppInfo mbean
javax.management.InstanceAlreadyExistsException: kafka.admin.client:type=app-info,id=orderbook-local-test-group-enumerator-admin-client
	at com.sun.jmx.mbeanserver.Repository.addMBean(Repository.java:436) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerWithRepository(DefaultMBeanServerInterceptor.java:1865) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerDynamicMBean(DefaultMBeanServerInterceptor.java:960) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerObject(DefaultMBeanServerInterceptor.java:895) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerMBean(DefaultMBeanServerInterceptor.java:320) ~[?:?]
	at com.sun.jmx.mbeanserver.JmxMBeanServer.registerMBean(JmxMBeanServer.java:523) ~[?:?]
	at org.apache.kafka.common.utils.AppInfoParser.registerAppInfo(AppInfoParser.java:64) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.admin.KafkaAdminClient.<init>(KafkaAdminClient.java:602) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.admin.KafkaAdminClient.createInternal(KafkaAdminClient.java:544) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.admin.KafkaAdminClient.createInternal(KafkaAdminClient.java:488) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.admin.Admin.create(Admin.java:134) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.admin.AdminClient.create(AdminClient.java:39) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.flink.connector.kafka.source.enumerator.KafkaSourceEnumerator.getKafkaAdminClient(KafkaSourceEnumerator.java:441) ~[flink-connector-kafka-3.4.0-1.20.jar:3.4.0-1.20]
	at org.apache.flink.connector.kafka.source.enumerator.KafkaSourceEnumerator.start(KafkaSourceEnumerator.java:164) ~[flink-connector-kafka-3.4.0-1.20.jar:3.4.0-1.20]
	at org.apache.flink.runtime.source.coordinator.SourceCoordinator.lambda$start$1(SourceCoordinator.java:244) ~[flink-runtime-1.20.0.jar:1.20.0]
	at org.apache.flink.runtime.source.coordinator.SourceCoordinator.lambda$runInEventLoop$10(SourceCoordinator.java:533) ~[flink-runtime-1.20.0.jar:1.20.0]
	at org.apache.flink.util.ThrowableCatchingRunnable.run(ThrowableCatchingRunnable.java:40) [flink-core-1.20.0.jar:1.20.0]
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:539) [?:?]
	at java.util.concurrent.FutureTask.run(FutureTask.java:264) [?:?]
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:304) [?:?]
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136) [?:?]
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635) [?:?]
	at java.lang.Thread.run(Thread.java:840) [?:?]
2025-08-20 19:50:24.281 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] WARN  [o.a.k.c.c.ConsumerConfig:385] - These configurations '[client.id.prefix, partition.discovery.interval.ms]' were supplied but are not used yet.
2025-08-20 19:50:24.281 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 (1/1)#0] WARN  [o.a.k.c.c.ConsumerConfig:385] - These configurations '[client.id.prefix, partition.discovery.interval.ms]' were supplied but are not used yet.
2025-08-20 19:50:24.281 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 (1/1)#0] WARN  [o.a.k.c.c.ConsumerConfig:385] - These configurations '[client.id.prefix, partition.discovery.interval.ms]' were supplied but are not used yet.
2025-08-20 19:50:24.282 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 (1/1)#0] WARN  [o.a.k.c.u.AppInfoParser:68] - Error registering AppInfo mbean
javax.management.InstanceAlreadyExistsException: kafka.consumer:type=app-info,id=orderbook-local-test-group-0
	at com.sun.jmx.mbeanserver.Repository.addMBean(Repository.java:436) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerWithRepository(DefaultMBeanServerInterceptor.java:1865) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerDynamicMBean(DefaultMBeanServerInterceptor.java:960) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerObject(DefaultMBeanServerInterceptor.java:895) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerMBean(DefaultMBeanServerInterceptor.java:320) ~[?:?]
	at com.sun.jmx.mbeanserver.JmxMBeanServer.registerMBean(JmxMBeanServer.java:523) ~[?:?]
	at org.apache.kafka.common.utils.AppInfoParser.registerAppInfo(AppInfoParser.java:64) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.consumer.KafkaConsumer.<init>(KafkaConsumer.java:821) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.consumer.KafkaConsumer.<init>(KafkaConsumer.java:665) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.consumer.KafkaConsumer.<init>(KafkaConsumer.java:646) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.consumer.KafkaConsumer.<init>(KafkaConsumer.java:626) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.flink.connector.kafka.source.reader.KafkaPartitionSplitReader.<init>(KafkaPartitionSplitReader.java:97) ~[flink-connector-kafka-3.4.0-1.20.jar:3.4.0-1.20]
	at org.apache.flink.connector.kafka.source.KafkaSource.lambda$createReader$1(KafkaSource.java:185) ~[flink-connector-kafka-3.4.0-1.20.jar:3.4.0-1.20]
	at org.apache.flink.connector.base.source.reader.fetcher.SplitFetcherManager.createSplitFetcher(SplitFetcherManager.java:259) ~[flink-connector-base-1.20.0.jar:1.20.0]
	at org.apache.flink.connector.base.source.reader.fetcher.SingleThreadFetcherManager.addSplits(SingleThreadFetcherManager.java:148) ~[flink-connector-base-1.20.0.jar:1.20.0]
	at org.apache.flink.connector.base.source.reader.SourceReaderBase.addSplits(SourceReaderBase.java:315) ~[flink-connector-base-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.api.operators.SourceOperator.handleAddSplitsEvent(SourceOperator.java:606) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.api.operators.SourceOperator.handleOperatorEvent(SourceOperator.java:575) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.OperatorEventDispatcherImpl.dispatchEventToHandlers(OperatorEventDispatcherImpl.java:72) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.RegularOperatorChain.dispatchOperatorEvent(RegularOperatorChain.java:80) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.StreamTask.lambda$dispatchOperatorEvent$24(StreamTask.java:1609) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.StreamTaskActionExecutor$1.runThrowing(StreamTaskActionExecutor.java:50) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.mailbox.Mail.run(Mail.java:101) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.mailbox.MailboxProcessor.runMail(MailboxProcessor.java:414) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.mailbox.MailboxProcessor.processMailsWhenDefaultActionUnavailable(MailboxProcessor.java:383) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.mailbox.MailboxProcessor.processMail(MailboxProcessor.java:368) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.mailbox.MailboxProcessor.runMailboxLoop(MailboxProcessor.java:229) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.StreamTask.runMailboxLoop(StreamTask.java:973) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.StreamTask.invoke(StreamTask.java:917) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.runtime.taskmanager.Task.runWithSystemExitMonitoring(Task.java:970) [flink-runtime-1.20.0.jar:1.20.0]
	at org.apache.flink.runtime.taskmanager.Task.restoreAndInvoke(Task.java:949) [flink-runtime-1.20.0.jar:1.20.0]
	at org.apache.flink.runtime.taskmanager.Task.doRun(Task.java:763) [flink-runtime-1.20.0.jar:1.20.0]
	at org.apache.flink.runtime.taskmanager.Task.run(Task.java:575) [flink-runtime-1.20.0.jar:1.20.0]
	at java.lang.Thread.run(Thread.java:840) [?:?]
2025-08-20 19:50:24.282 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 (1/1)#0] WARN  [o.a.k.c.u.AppInfoParser:68] - Error registering AppInfo mbean
javax.management.InstanceAlreadyExistsException: kafka.consumer:type=app-info,id=orderbook-local-test-group-0
	at com.sun.jmx.mbeanserver.Repository.addMBean(Repository.java:436) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerWithRepository(DefaultMBeanServerInterceptor.java:1865) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerDynamicMBean(DefaultMBeanServerInterceptor.java:960) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerObject(DefaultMBeanServerInterceptor.java:895) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerMBean(DefaultMBeanServerInterceptor.java:320) ~[?:?]
	at com.sun.jmx.mbeanserver.JmxMBeanServer.registerMBean(JmxMBeanServer.java:523) ~[?:?]
	at org.apache.kafka.common.utils.AppInfoParser.registerAppInfo(AppInfoParser.java:64) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.consumer.KafkaConsumer.<init>(KafkaConsumer.java:821) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.consumer.KafkaConsumer.<init>(KafkaConsumer.java:665) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.consumer.KafkaConsumer.<init>(KafkaConsumer.java:646) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.consumer.KafkaConsumer.<init>(KafkaConsumer.java:626) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.flink.connector.kafka.source.reader.KafkaPartitionSplitReader.<init>(KafkaPartitionSplitReader.java:97) ~[flink-connector-kafka-3.4.0-1.20.jar:3.4.0-1.20]
	at org.apache.flink.connector.kafka.source.KafkaSource.lambda$createReader$1(KafkaSource.java:185) ~[flink-connector-kafka-3.4.0-1.20.jar:3.4.0-1.20]
	at org.apache.flink.connector.base.source.reader.fetcher.SplitFetcherManager.createSplitFetcher(SplitFetcherManager.java:259) ~[flink-connector-base-1.20.0.jar:1.20.0]
	at org.apache.flink.connector.base.source.reader.fetcher.SingleThreadFetcherManager.addSplits(SingleThreadFetcherManager.java:148) ~[flink-connector-base-1.20.0.jar:1.20.0]
	at org.apache.flink.connector.base.source.reader.SourceReaderBase.addSplits(SourceReaderBase.java:315) ~[flink-connector-base-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.api.operators.SourceOperator.handleAddSplitsEvent(SourceOperator.java:606) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.api.operators.SourceOperator.handleOperatorEvent(SourceOperator.java:575) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.OperatorEventDispatcherImpl.dispatchEventToHandlers(OperatorEventDispatcherImpl.java:72) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.RegularOperatorChain.dispatchOperatorEvent(RegularOperatorChain.java:80) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.StreamTask.lambda$dispatchOperatorEvent$24(StreamTask.java:1609) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.StreamTaskActionExecutor$1.runThrowing(StreamTaskActionExecutor.java:50) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.mailbox.Mail.run(Mail.java:101) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.mailbox.MailboxProcessor.runMail(MailboxProcessor.java:414) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.mailbox.MailboxProcessor.processMailsWhenDefaultActionUnavailable(MailboxProcessor.java:383) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.mailbox.MailboxProcessor.processMail(MailboxProcessor.java:368) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.mailbox.MailboxProcessor.runMailboxLoop(MailboxProcessor.java:229) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.StreamTask.runMailboxLoop(StreamTask.java:973) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.StreamTask.invoke(StreamTask.java:917) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.runtime.taskmanager.Task.runWithSystemExitMonitoring(Task.java:970) [flink-runtime-1.20.0.jar:1.20.0]
	at org.apache.flink.runtime.taskmanager.Task.restoreAndInvoke(Task.java:949) [flink-runtime-1.20.0.jar:1.20.0]
	at org.apache.flink.runtime.taskmanager.Task.doRun(Task.java:763) [flink-runtime-1.20.0.jar:1.20.0]
	at org.apache.flink.runtime.taskmanager.Task.run(Task.java:575) [flink-runtime-1.20.0.jar:1.20.0]
	at java.lang.Thread.run(Thread.java:840) [?:?]
2025-08-20 19:50:24.412 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] WARN  [c.f.f.PnLCalculationFunction:180] - 发现无持仓的平仓交易 - 发送到异常流: 交易号=101355625, 合约=JD2504-C-3200, 方向=S, 数量=5
2025-08-20 19:50:24.412 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] WARN  [c.f.f.PnLCalculationFunction:180] - 发现无持仓的平仓交易 - 发送到异常流: 交易号=101355626, 合约=JD2504-C-3200, 方向=S, 数量=5
2025-08-20 19:50:24.413 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] WARN  [c.f.f.PnLCalculationFunction:180] - 发现无持仓的平仓交易 - 发送到异常流: 交易号=101355700, 合约=EB2505, 方向=B, 数量=10
2025-08-20 19:50:24.413 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] WARN  [c.f.f.PnLCalculationFunction:180] - 发现无持仓的平仓交易 - 发送到异常流: 交易号=48060, 合约=SH2505, 方向=S, 数量=1
2025-08-20 19:50:24.413 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] WARN  [c.f.f.PnLCalculationFunction:180] - 发现无持仓的平仓交易 - 发送到异常流: 交易号=100052597, 合约=EB2505, 方向=B, 数量=8
2025-08-20 19:50:24.413 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] WARN  [c.f.f.PnLCalculationFunction:180] - 发现无持仓的平仓交易 - 发送到异常流: 交易号=100052597, 合约=EB2505, 方向=S, 数量=8
2025-08-20 19:50:24.413 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] WARN  [c.f.f.PnLCalculationFunction:180] - 发现无持仓的平仓交易 - 发送到异常流: 交易号=100052678, 合约=EB2505, 方向=B, 数量=1
2025-08-20 19:50:24.414 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] WARN  [c.f.f.PnLCalculationFunction:180] - 发现无持仓的平仓交易 - 发送到异常流: 交易号=100052678, 合约=EB2505, 方向=S, 数量=1
2025-08-20 19:50:24.414 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] WARN  [c.f.f.PnLCalculationFunction:180] - 发现无持仓的平仓交易 - 发送到异常流: 交易号=100052768, 合约=EB2505, 方向=B, 数量=1
2025-08-20 19:50:24.418 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] WARN  [c.f.f.PnLCalculationFunction:180] - 发现无持仓的平仓交易 - 发送到异常流: 交易号=100052770, 合约=EB2505, 方向=B, 数量=1
2025-08-20 19:50:24.418 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] WARN  [c.f.f.PnLCalculationFunction:180] - 发现无持仓的平仓交易 - 发送到异常流: 交易号=100052795, 合约=EB2505, 方向=B, 数量=1
2025-08-20 19:50:24.419 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] WARN  [c.f.f.PnLCalculationFunction:180] - 发现无持仓的平仓交易 - 发送到异常流: 交易号=100052821, 合约=EB2505, 方向=B, 数量=1
2025-08-20 19:50:24.420 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] WARN  [c.f.f.PnLCalculationFunction:180] - 发现无持仓的平仓交易 - 发送到异常流: 交易号=100052822, 合约=EB2505, 方向=B, 数量=2
2025-08-20 19:50:24.421 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] WARN  [c.f.f.PnLCalculationFunction:180] - 发现无持仓的平仓交易 - 发送到异常流: 交易号=100052833, 合约=EB2505, 方向=B, 数量=2
2025-08-20 19:50:24.421 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] WARN  [c.f.f.PnLCalculationFunction:180] - 发现无持仓的平仓交易 - 发送到异常流: 交易号=100052846, 合约=EB2504, 方向=B, 数量=1
2025-08-20 19:50:24.422 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] WARN  [c.f.f.PnLCalculationFunction:180] - 发现无持仓的平仓交易 - 发送到异常流: 交易号=100052847, 合约=EB2505, 方向=B, 数量=1
2025-08-20 19:50:24.423 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] WARN  [c.f.f.PnLCalculationFunction:180] - 发现无持仓的平仓交易 - 发送到异常流: 交易号=100052849, 合约=EB2504, 方向=S, 数量=10
2025-08-20 19:50:28.446 [main] WARN  [o.a.f.c.k.s.KafkaSourceBuilder:497] - Property auto.offset.reset is provided but will be overridden from earliest to earliest
2025-08-20 19:50:47.198 [main] WARN  [o.a.f.c.k.s.KafkaSourceBuilder:497] - Property auto.offset.reset is provided but will be overridden from earliest to earliest
2025-08-20 19:50:47.200 [main] WARN  [o.a.f.c.k.s.KafkaSourceBuilder:497] - Property auto.offset.reset is provided but will be overridden from earliest to earliest
2025-08-20 19:52:23.318 [main] WARN  [o.a.f.r.s.t.DefaultDelegationTokenManager:248] - No tokens obtained so skipping notifications
2025-08-20 19:52:24.002 [main] WARN  [o.a.f.r.w.WebMonitorUtils:76] - Log file environment variable 'log.file' is not set.
2025-08-20 19:52:24.003 [main] WARN  [o.a.f.r.w.WebMonitorUtils:82] - JobManager log files are unavailable in the web dashboard. Log file location not found in environment variable 'log.file' or configuration key 'web.log.path'.
2025-08-20 19:52:24.342 [flink-pekko.actor.default-dispatcher-4] WARN  [o.a.f.r.s.t.DefaultDelegationTokenManager:320] - No tokens obtained so skipping notifications
2025-08-20 19:52:24.342 [flink-pekko.actor.default-dispatcher-4] WARN  [o.a.f.r.s.t.DefaultDelegationTokenManager:335] - Tokens update task not started because either no tokens obtained or none of the tokens specified its renewal date
2025-08-20 19:52:24.524 [SourceCoordinator-Source: Kafka-交易] WARN  [o.a.k.c.a.AdminClientConfig:385] - These configurations '[key.deserializer, value.deserializer, max.poll.records, client.id.prefix, group.id, partition.discovery.interval.ms, heartbeat.interval.ms, enable.auto.commit, fetch.max.wait.ms, fetch.min.bytes, session.timeout.ms, auto.offset.reset]' were supplied but are not used yet.
2025-08-20 19:52:24.524 [SourceCoordinator-Source: Kafka-组合订单] WARN  [o.a.k.c.a.AdminClientConfig:385] - These configurations '[key.deserializer, value.deserializer, max.poll.records, client.id.prefix, group.id, partition.discovery.interval.ms, heartbeat.interval.ms, enable.auto.commit, fetch.max.wait.ms, fetch.min.bytes, session.timeout.ms, auto.offset.reset]' were supplied but are not used yet.
2025-08-20 19:52:24.524 [SourceCoordinator-Source: Kafka-单腿订单] WARN  [o.a.k.c.a.AdminClientConfig:385] - These configurations '[key.deserializer, value.deserializer, max.poll.records, client.id.prefix, group.id, partition.discovery.interval.ms, heartbeat.interval.ms, enable.auto.commit, fetch.max.wait.ms, fetch.min.bytes, session.timeout.ms, auto.offset.reset]' were supplied but are not used yet.
2025-08-20 19:52:24.525 [SourceCoordinator-Source: Kafka-单腿订单] WARN  [o.a.k.c.u.AppInfoParser:68] - Error registering AppInfo mbean
javax.management.InstanceAlreadyExistsException: kafka.admin.client:type=app-info,id=orderbook-local-test-group-enumerator-admin-client
	at com.sun.jmx.mbeanserver.Repository.addMBean(Repository.java:436) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerWithRepository(DefaultMBeanServerInterceptor.java:1865) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerDynamicMBean(DefaultMBeanServerInterceptor.java:960) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerObject(DefaultMBeanServerInterceptor.java:895) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerMBean(DefaultMBeanServerInterceptor.java:320) ~[?:?]
	at com.sun.jmx.mbeanserver.JmxMBeanServer.registerMBean(JmxMBeanServer.java:523) ~[?:?]
	at org.apache.kafka.common.utils.AppInfoParser.registerAppInfo(AppInfoParser.java:64) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.admin.KafkaAdminClient.<init>(KafkaAdminClient.java:602) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.admin.KafkaAdminClient.createInternal(KafkaAdminClient.java:544) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.admin.KafkaAdminClient.createInternal(KafkaAdminClient.java:488) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.admin.Admin.create(Admin.java:134) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.admin.AdminClient.create(AdminClient.java:39) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.flink.connector.kafka.source.enumerator.KafkaSourceEnumerator.getKafkaAdminClient(KafkaSourceEnumerator.java:441) ~[flink-connector-kafka-3.4.0-1.20.jar:3.4.0-1.20]
	at org.apache.flink.connector.kafka.source.enumerator.KafkaSourceEnumerator.start(KafkaSourceEnumerator.java:164) ~[flink-connector-kafka-3.4.0-1.20.jar:3.4.0-1.20]
	at org.apache.flink.runtime.source.coordinator.SourceCoordinator.lambda$start$1(SourceCoordinator.java:244) ~[flink-runtime-1.20.0.jar:1.20.0]
	at org.apache.flink.runtime.source.coordinator.SourceCoordinator.lambda$runInEventLoop$10(SourceCoordinator.java:533) ~[flink-runtime-1.20.0.jar:1.20.0]
	at org.apache.flink.util.ThrowableCatchingRunnable.run(ThrowableCatchingRunnable.java:40) [flink-core-1.20.0.jar:1.20.0]
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:539) [?:?]
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:264) [?:?]
	at java.util.concurrent.FutureTask.run(FutureTask.java) [?:?]
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:304) [?:?]
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136) [?:?]
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635) [?:?]
	at java.lang.Thread.run(Thread.java:840) [?:?]
2025-08-20 19:52:24.528 [SourceCoordinator-Source: Kafka-组合订单] WARN  [o.a.k.c.u.AppInfoParser:68] - Error registering AppInfo mbean
javax.management.InstanceAlreadyExistsException: kafka.admin.client:type=app-info,id=orderbook-local-test-group-enumerator-admin-client
	at com.sun.jmx.mbeanserver.Repository.addMBean(Repository.java:436) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerWithRepository(DefaultMBeanServerInterceptor.java:1865) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerDynamicMBean(DefaultMBeanServerInterceptor.java:960) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerObject(DefaultMBeanServerInterceptor.java:895) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerMBean(DefaultMBeanServerInterceptor.java:320) ~[?:?]
	at com.sun.jmx.mbeanserver.JmxMBeanServer.registerMBean(JmxMBeanServer.java:523) ~[?:?]
	at org.apache.kafka.common.utils.AppInfoParser.registerAppInfo(AppInfoParser.java:64) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.admin.KafkaAdminClient.<init>(KafkaAdminClient.java:602) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.admin.KafkaAdminClient.createInternal(KafkaAdminClient.java:544) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.admin.KafkaAdminClient.createInternal(KafkaAdminClient.java:488) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.admin.Admin.create(Admin.java:134) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.admin.AdminClient.create(AdminClient.java:39) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.flink.connector.kafka.source.enumerator.KafkaSourceEnumerator.getKafkaAdminClient(KafkaSourceEnumerator.java:441) ~[flink-connector-kafka-3.4.0-1.20.jar:3.4.0-1.20]
	at org.apache.flink.connector.kafka.source.enumerator.KafkaSourceEnumerator.start(KafkaSourceEnumerator.java:164) ~[flink-connector-kafka-3.4.0-1.20.jar:3.4.0-1.20]
	at org.apache.flink.runtime.source.coordinator.SourceCoordinator.lambda$start$1(SourceCoordinator.java:244) ~[flink-runtime-1.20.0.jar:1.20.0]
	at org.apache.flink.runtime.source.coordinator.SourceCoordinator.lambda$runInEventLoop$10(SourceCoordinator.java:533) ~[flink-runtime-1.20.0.jar:1.20.0]
	at org.apache.flink.util.ThrowableCatchingRunnable.run(ThrowableCatchingRunnable.java:40) [flink-core-1.20.0.jar:1.20.0]
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:539) [?:?]
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:264) [?:?]
	at java.util.concurrent.FutureTask.run(FutureTask.java) [?:?]
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:304) [?:?]
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136) [?:?]
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635) [?:?]
	at java.lang.Thread.run(Thread.java:840) [?:?]
2025-08-20 19:52:24.704 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 (1/1)#0] WARN  [o.a.k.c.c.ConsumerConfig:385] - These configurations '[client.id.prefix, partition.discovery.interval.ms]' were supplied but are not used yet.
2025-08-20 19:52:24.704 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 (1/1)#0] WARN  [o.a.k.c.c.ConsumerConfig:385] - These configurations '[client.id.prefix, partition.discovery.interval.ms]' were supplied but are not used yet.
2025-08-20 19:52:24.704 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] WARN  [o.a.k.c.c.ConsumerConfig:385] - These configurations '[client.id.prefix, partition.discovery.interval.ms]' were supplied but are not used yet.
2025-08-20 19:52:24.704 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] WARN  [o.a.k.c.u.AppInfoParser:68] - Error registering AppInfo mbean
javax.management.InstanceAlreadyExistsException: kafka.consumer:type=app-info,id=orderbook-local-test-group-0
	at com.sun.jmx.mbeanserver.Repository.addMBean(Repository.java:436) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerWithRepository(DefaultMBeanServerInterceptor.java:1865) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerDynamicMBean(DefaultMBeanServerInterceptor.java:960) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerObject(DefaultMBeanServerInterceptor.java:895) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerMBean(DefaultMBeanServerInterceptor.java:320) ~[?:?]
	at com.sun.jmx.mbeanserver.JmxMBeanServer.registerMBean(JmxMBeanServer.java:523) ~[?:?]
	at org.apache.kafka.common.utils.AppInfoParser.registerAppInfo(AppInfoParser.java:64) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.consumer.KafkaConsumer.<init>(KafkaConsumer.java:821) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.consumer.KafkaConsumer.<init>(KafkaConsumer.java:665) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.consumer.KafkaConsumer.<init>(KafkaConsumer.java:646) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.consumer.KafkaConsumer.<init>(KafkaConsumer.java:626) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.flink.connector.kafka.source.reader.KafkaPartitionSplitReader.<init>(KafkaPartitionSplitReader.java:97) ~[flink-connector-kafka-3.4.0-1.20.jar:3.4.0-1.20]
	at org.apache.flink.connector.kafka.source.KafkaSource.lambda$createReader$1(KafkaSource.java:185) ~[flink-connector-kafka-3.4.0-1.20.jar:3.4.0-1.20]
	at org.apache.flink.connector.base.source.reader.fetcher.SplitFetcherManager.createSplitFetcher(SplitFetcherManager.java:259) ~[flink-connector-base-1.20.0.jar:1.20.0]
	at org.apache.flink.connector.base.source.reader.fetcher.SingleThreadFetcherManager.addSplits(SingleThreadFetcherManager.java:148) ~[flink-connector-base-1.20.0.jar:1.20.0]
	at org.apache.flink.connector.base.source.reader.SourceReaderBase.addSplits(SourceReaderBase.java:315) ~[flink-connector-base-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.api.operators.SourceOperator.handleAddSplitsEvent(SourceOperator.java:606) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.api.operators.SourceOperator.handleOperatorEvent(SourceOperator.java:575) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.OperatorEventDispatcherImpl.dispatchEventToHandlers(OperatorEventDispatcherImpl.java:72) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.RegularOperatorChain.dispatchOperatorEvent(RegularOperatorChain.java:80) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.StreamTask.lambda$dispatchOperatorEvent$24(StreamTask.java:1609) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.StreamTaskActionExecutor$1.runThrowing(StreamTaskActionExecutor.java:50) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.mailbox.Mail.run(Mail.java:101) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.mailbox.MailboxProcessor.runMail(MailboxProcessor.java:414) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.mailbox.MailboxProcessor.processMailsWhenDefaultActionUnavailable(MailboxProcessor.java:383) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.mailbox.MailboxProcessor.processMail(MailboxProcessor.java:368) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.mailbox.MailboxProcessor.runMailboxLoop(MailboxProcessor.java:229) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.StreamTask.runMailboxLoop(StreamTask.java:973) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.StreamTask.invoke(StreamTask.java:917) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.runtime.taskmanager.Task.runWithSystemExitMonitoring(Task.java:970) [flink-runtime-1.20.0.jar:1.20.0]
	at org.apache.flink.runtime.taskmanager.Task.restoreAndInvoke(Task.java:949) [flink-runtime-1.20.0.jar:1.20.0]
	at org.apache.flink.runtime.taskmanager.Task.doRun(Task.java:763) [flink-runtime-1.20.0.jar:1.20.0]
	at org.apache.flink.runtime.taskmanager.Task.run(Task.java:575) [flink-runtime-1.20.0.jar:1.20.0]
	at java.lang.Thread.run(Thread.java:840) [?:?]
2025-08-20 19:52:24.704 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 (1/1)#0] WARN  [o.a.k.c.u.AppInfoParser:68] - Error registering AppInfo mbean
javax.management.InstanceAlreadyExistsException: kafka.consumer:type=app-info,id=orderbook-local-test-group-0
	at com.sun.jmx.mbeanserver.Repository.addMBean(Repository.java:436) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerWithRepository(DefaultMBeanServerInterceptor.java:1865) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerDynamicMBean(DefaultMBeanServerInterceptor.java:960) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerObject(DefaultMBeanServerInterceptor.java:895) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerMBean(DefaultMBeanServerInterceptor.java:320) ~[?:?]
	at com.sun.jmx.mbeanserver.JmxMBeanServer.registerMBean(JmxMBeanServer.java:523) ~[?:?]
	at org.apache.kafka.common.utils.AppInfoParser.registerAppInfo(AppInfoParser.java:64) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.consumer.KafkaConsumer.<init>(KafkaConsumer.java:821) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.consumer.KafkaConsumer.<init>(KafkaConsumer.java:665) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.consumer.KafkaConsumer.<init>(KafkaConsumer.java:646) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.consumer.KafkaConsumer.<init>(KafkaConsumer.java:626) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.flink.connector.kafka.source.reader.KafkaPartitionSplitReader.<init>(KafkaPartitionSplitReader.java:97) ~[flink-connector-kafka-3.4.0-1.20.jar:3.4.0-1.20]
	at org.apache.flink.connector.kafka.source.KafkaSource.lambda$createReader$1(KafkaSource.java:185) ~[flink-connector-kafka-3.4.0-1.20.jar:3.4.0-1.20]
	at org.apache.flink.connector.base.source.reader.fetcher.SplitFetcherManager.createSplitFetcher(SplitFetcherManager.java:259) ~[flink-connector-base-1.20.0.jar:1.20.0]
	at org.apache.flink.connector.base.source.reader.fetcher.SingleThreadFetcherManager.addSplits(SingleThreadFetcherManager.java:148) ~[flink-connector-base-1.20.0.jar:1.20.0]
	at org.apache.flink.connector.base.source.reader.SourceReaderBase.addSplits(SourceReaderBase.java:315) ~[flink-connector-base-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.api.operators.SourceOperator.handleAddSplitsEvent(SourceOperator.java:606) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.api.operators.SourceOperator.handleOperatorEvent(SourceOperator.java:575) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.OperatorEventDispatcherImpl.dispatchEventToHandlers(OperatorEventDispatcherImpl.java:72) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.RegularOperatorChain.dispatchOperatorEvent(RegularOperatorChain.java:80) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.StreamTask.lambda$dispatchOperatorEvent$24(StreamTask.java:1609) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.StreamTaskActionExecutor$1.runThrowing(StreamTaskActionExecutor.java:50) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.mailbox.Mail.run(Mail.java:101) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.mailbox.MailboxProcessor.runMail(MailboxProcessor.java:414) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.mailbox.MailboxProcessor.processMailsWhenDefaultActionUnavailable(MailboxProcessor.java:383) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.mailbox.MailboxProcessor.processMail(MailboxProcessor.java:368) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.mailbox.MailboxProcessor.runMailboxLoop(MailboxProcessor.java:229) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.StreamTask.runMailboxLoop(StreamTask.java:973) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.StreamTask.invoke(StreamTask.java:917) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.runtime.taskmanager.Task.runWithSystemExitMonitoring(Task.java:970) [flink-runtime-1.20.0.jar:1.20.0]
	at org.apache.flink.runtime.taskmanager.Task.restoreAndInvoke(Task.java:949) [flink-runtime-1.20.0.jar:1.20.0]
	at org.apache.flink.runtime.taskmanager.Task.doRun(Task.java:763) [flink-runtime-1.20.0.jar:1.20.0]
	at org.apache.flink.runtime.taskmanager.Task.run(Task.java:575) [flink-runtime-1.20.0.jar:1.20.0]
	at java.lang.Thread.run(Thread.java:840) [?:?]
2025-08-20 19:53:38.836 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] WARN  [c.f.f.PnLCalculationFunction:180] - 发现无持仓的平仓交易 - 发送到异常流: 交易号=101355625, 合约=JD2504-C-3200, 方向=S, 数量=5
2025-08-20 19:53:41.302 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] WARN  [c.f.f.PnLCalculationFunction:180] - 发现无持仓的平仓交易 - 发送到异常流: 交易号=101355626, 合约=JD2504-C-3200, 方向=S, 数量=5
2025-08-20 19:53:51.367 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] WARN  [c.f.f.PnLCalculationFunction:180] - 发现无持仓的平仓交易 - 发送到异常流: 交易号=101355700, 合约=EB2505, 方向=B, 数量=10
2025-08-20 19:53:51.369 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] WARN  [c.f.f.PnLCalculationFunction:180] - 发现无持仓的平仓交易 - 发送到异常流: 交易号=48060, 合约=SH2505, 方向=S, 数量=1
2025-08-20 19:53:51.371 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] WARN  [c.f.f.PnLCalculationFunction:180] - 发现无持仓的平仓交易 - 发送到异常流: 交易号=100052597, 合约=EB2505, 方向=B, 数量=8
2025-08-20 19:53:51.372 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] WARN  [c.f.f.PnLCalculationFunction:180] - 发现无持仓的平仓交易 - 发送到异常流: 交易号=100052597, 合约=EB2505, 方向=S, 数量=8
2025-08-20 19:53:57.642 [main] WARN  [o.a.f.c.k.s.KafkaSourceBuilder:497] - Property auto.offset.reset is provided but will be overridden from earliest to earliest
2025-08-20 19:53:57.658 [main] WARN  [o.a.f.c.k.s.KafkaSourceBuilder:497] - Property auto.offset.reset is provided but will be overridden from earliest to earliest
2025-08-20 19:53:57.658 [main] WARN  [o.a.f.c.k.s.KafkaSourceBuilder:497] - Property auto.offset.reset is provided but will be overridden from earliest to earliest
2025-08-20 19:53:58.101 [main] WARN  [o.a.f.r.s.t.DefaultDelegationTokenManager:248] - No tokens obtained so skipping notifications
2025-08-20 19:53:58.208 [main] WARN  [o.a.f.r.w.WebMonitorUtils:76] - Log file environment variable 'log.file' is not set.
2025-08-20 19:53:58.208 [main] WARN  [o.a.f.r.w.WebMonitorUtils:82] - JobManager log files are unavailable in the web dashboard. Log file location not found in environment variable 'log.file' or configuration key 'web.log.path'.
2025-08-20 19:53:58.264 [flink-pekko.actor.default-dispatcher-4] WARN  [o.a.f.r.s.t.DefaultDelegationTokenManager:320] - No tokens obtained so skipping notifications
2025-08-20 19:53:58.264 [flink-pekko.actor.default-dispatcher-4] WARN  [o.a.f.r.s.t.DefaultDelegationTokenManager:335] - Tokens update task not started because either no tokens obtained or none of the tokens specified its renewal date
2025-08-20 19:53:58.364 [SourceCoordinator-Source: Kafka-组合订单] WARN  [o.a.k.c.a.AdminClientConfig:385] - These configurations '[key.deserializer, value.deserializer, max.poll.records, client.id.prefix, group.id, partition.discovery.interval.ms, heartbeat.interval.ms, enable.auto.commit, fetch.max.wait.ms, fetch.min.bytes, session.timeout.ms, auto.offset.reset]' were supplied but are not used yet.
2025-08-20 19:53:58.364 [SourceCoordinator-Source: Kafka-交易] WARN  [o.a.k.c.a.AdminClientConfig:385] - These configurations '[key.deserializer, value.deserializer, max.poll.records, client.id.prefix, group.id, partition.discovery.interval.ms, heartbeat.interval.ms, enable.auto.commit, fetch.max.wait.ms, fetch.min.bytes, session.timeout.ms, auto.offset.reset]' were supplied but are not used yet.
2025-08-20 19:53:58.364 [SourceCoordinator-Source: Kafka-单腿订单] WARN  [o.a.k.c.a.AdminClientConfig:385] - These configurations '[key.deserializer, value.deserializer, max.poll.records, client.id.prefix, group.id, partition.discovery.interval.ms, heartbeat.interval.ms, enable.auto.commit, fetch.max.wait.ms, fetch.min.bytes, session.timeout.ms, auto.offset.reset]' were supplied but are not used yet.
2025-08-20 19:53:58.365 [SourceCoordinator-Source: Kafka-交易] WARN  [o.a.k.c.u.AppInfoParser:68] - Error registering AppInfo mbean
javax.management.InstanceAlreadyExistsException: kafka.admin.client:type=app-info,id=orderbook-local-test-group-enumerator-admin-client
	at com.sun.jmx.mbeanserver.Repository.addMBean(Repository.java:436) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerWithRepository(DefaultMBeanServerInterceptor.java:1865) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerDynamicMBean(DefaultMBeanServerInterceptor.java:960) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerObject(DefaultMBeanServerInterceptor.java:895) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerMBean(DefaultMBeanServerInterceptor.java:320) ~[?:?]
	at com.sun.jmx.mbeanserver.JmxMBeanServer.registerMBean(JmxMBeanServer.java:523) ~[?:?]
	at org.apache.kafka.common.utils.AppInfoParser.registerAppInfo(AppInfoParser.java:64) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.admin.KafkaAdminClient.<init>(KafkaAdminClient.java:602) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.admin.KafkaAdminClient.createInternal(KafkaAdminClient.java:544) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.admin.KafkaAdminClient.createInternal(KafkaAdminClient.java:488) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.admin.Admin.create(Admin.java:134) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.admin.AdminClient.create(AdminClient.java:39) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.flink.connector.kafka.source.enumerator.KafkaSourceEnumerator.getKafkaAdminClient(KafkaSourceEnumerator.java:441) ~[flink-connector-kafka-3.4.0-1.20.jar:3.4.0-1.20]
	at org.apache.flink.connector.kafka.source.enumerator.KafkaSourceEnumerator.start(KafkaSourceEnumerator.java:164) ~[flink-connector-kafka-3.4.0-1.20.jar:3.4.0-1.20]
	at org.apache.flink.runtime.source.coordinator.SourceCoordinator.lambda$start$1(SourceCoordinator.java:244) ~[flink-runtime-1.20.0.jar:1.20.0]
	at org.apache.flink.runtime.source.coordinator.SourceCoordinator.lambda$runInEventLoop$10(SourceCoordinator.java:533) ~[flink-runtime-1.20.0.jar:1.20.0]
	at org.apache.flink.util.ThrowableCatchingRunnable.run(ThrowableCatchingRunnable.java:40) [flink-core-1.20.0.jar:1.20.0]
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:539) [?:?]
	at java.util.concurrent.FutureTask.run(FutureTask.java:264) [?:?]
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:304) [?:?]
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136) [?:?]
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635) [?:?]
	at java.lang.Thread.run(Thread.java:840) [?:?]
2025-08-20 19:53:58.368 [SourceCoordinator-Source: Kafka-组合订单] WARN  [o.a.k.c.u.AppInfoParser:68] - Error registering AppInfo mbean
javax.management.InstanceAlreadyExistsException: kafka.admin.client:type=app-info,id=orderbook-local-test-group-enumerator-admin-client
	at com.sun.jmx.mbeanserver.Repository.addMBean(Repository.java:436) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerWithRepository(DefaultMBeanServerInterceptor.java:1865) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerDynamicMBean(DefaultMBeanServerInterceptor.java:960) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerObject(DefaultMBeanServerInterceptor.java:895) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerMBean(DefaultMBeanServerInterceptor.java:320) ~[?:?]
	at com.sun.jmx.mbeanserver.JmxMBeanServer.registerMBean(JmxMBeanServer.java:523) ~[?:?]
	at org.apache.kafka.common.utils.AppInfoParser.registerAppInfo(AppInfoParser.java:64) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.admin.KafkaAdminClient.<init>(KafkaAdminClient.java:602) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.admin.KafkaAdminClient.createInternal(KafkaAdminClient.java:544) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.admin.KafkaAdminClient.createInternal(KafkaAdminClient.java:488) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.admin.Admin.create(Admin.java:134) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.admin.AdminClient.create(AdminClient.java:39) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.flink.connector.kafka.source.enumerator.KafkaSourceEnumerator.getKafkaAdminClient(KafkaSourceEnumerator.java:441) ~[flink-connector-kafka-3.4.0-1.20.jar:3.4.0-1.20]
	at org.apache.flink.connector.kafka.source.enumerator.KafkaSourceEnumerator.start(KafkaSourceEnumerator.java:164) ~[flink-connector-kafka-3.4.0-1.20.jar:3.4.0-1.20]
	at org.apache.flink.runtime.source.coordinator.SourceCoordinator.lambda$start$1(SourceCoordinator.java:244) ~[flink-runtime-1.20.0.jar:1.20.0]
	at org.apache.flink.runtime.source.coordinator.SourceCoordinator.lambda$runInEventLoop$10(SourceCoordinator.java:533) ~[flink-runtime-1.20.0.jar:1.20.0]
	at org.apache.flink.util.ThrowableCatchingRunnable.run(ThrowableCatchingRunnable.java:40) [flink-core-1.20.0.jar:1.20.0]
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:539) [?:?]
	at java.util.concurrent.FutureTask.run(FutureTask.java:264) [?:?]
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:304) [?:?]
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136) [?:?]
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635) [?:?]
	at java.lang.Thread.run(Thread.java:840) [?:?]
2025-08-20 19:53:58.511 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 (1/1)#0] WARN  [o.a.k.c.c.ConsumerConfig:385] - These configurations '[client.id.prefix, partition.discovery.interval.ms]' were supplied but are not used yet.
2025-08-20 19:53:58.511 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] WARN  [o.a.k.c.c.ConsumerConfig:385] - These configurations '[client.id.prefix, partition.discovery.interval.ms]' were supplied but are not used yet.
2025-08-20 19:53:58.511 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 (1/1)#0] WARN  [o.a.k.c.c.ConsumerConfig:385] - These configurations '[client.id.prefix, partition.discovery.interval.ms]' were supplied but are not used yet.
2025-08-20 19:53:58.511 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] WARN  [o.a.k.c.u.AppInfoParser:68] - Error registering AppInfo mbean
javax.management.InstanceAlreadyExistsException: kafka.consumer:type=app-info,id=orderbook-local-test-group-0
	at com.sun.jmx.mbeanserver.Repository.addMBean(Repository.java:436) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerWithRepository(DefaultMBeanServerInterceptor.java:1865) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerDynamicMBean(DefaultMBeanServerInterceptor.java:960) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerObject(DefaultMBeanServerInterceptor.java:895) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerMBean(DefaultMBeanServerInterceptor.java:320) ~[?:?]
	at com.sun.jmx.mbeanserver.JmxMBeanServer.registerMBean(JmxMBeanServer.java:523) ~[?:?]
	at org.apache.kafka.common.utils.AppInfoParser.registerAppInfo(AppInfoParser.java:64) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.consumer.KafkaConsumer.<init>(KafkaConsumer.java:821) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.consumer.KafkaConsumer.<init>(KafkaConsumer.java:665) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.consumer.KafkaConsumer.<init>(KafkaConsumer.java:646) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.consumer.KafkaConsumer.<init>(KafkaConsumer.java:626) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.flink.connector.kafka.source.reader.KafkaPartitionSplitReader.<init>(KafkaPartitionSplitReader.java:97) ~[flink-connector-kafka-3.4.0-1.20.jar:3.4.0-1.20]
	at org.apache.flink.connector.kafka.source.KafkaSource.lambda$createReader$1(KafkaSource.java:185) ~[flink-connector-kafka-3.4.0-1.20.jar:3.4.0-1.20]
	at org.apache.flink.connector.base.source.reader.fetcher.SplitFetcherManager.createSplitFetcher(SplitFetcherManager.java:259) ~[flink-connector-base-1.20.0.jar:1.20.0]
	at org.apache.flink.connector.base.source.reader.fetcher.SingleThreadFetcherManager.addSplits(SingleThreadFetcherManager.java:148) ~[flink-connector-base-1.20.0.jar:1.20.0]
	at org.apache.flink.connector.base.source.reader.SourceReaderBase.addSplits(SourceReaderBase.java:315) ~[flink-connector-base-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.api.operators.SourceOperator.handleAddSplitsEvent(SourceOperator.java:606) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.api.operators.SourceOperator.handleOperatorEvent(SourceOperator.java:575) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.OperatorEventDispatcherImpl.dispatchEventToHandlers(OperatorEventDispatcherImpl.java:72) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.RegularOperatorChain.dispatchOperatorEvent(RegularOperatorChain.java:80) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.StreamTask.lambda$dispatchOperatorEvent$24(StreamTask.java:1609) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.StreamTaskActionExecutor$1.runThrowing(StreamTaskActionExecutor.java:50) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.mailbox.Mail.run(Mail.java:101) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.mailbox.MailboxProcessor.runMail(MailboxProcessor.java:414) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.mailbox.MailboxProcessor.processMailsNonBlocking(MailboxProcessor.java:399) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.mailbox.MailboxProcessor.processMail(MailboxProcessor.java:361) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.mailbox.MailboxProcessor.runMailboxLoop(MailboxProcessor.java:229) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.StreamTask.runMailboxLoop(StreamTask.java:973) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.StreamTask.invoke(StreamTask.java:917) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.runtime.taskmanager.Task.runWithSystemExitMonitoring(Task.java:970) [flink-runtime-1.20.0.jar:1.20.0]
	at org.apache.flink.runtime.taskmanager.Task.restoreAndInvoke(Task.java:949) [flink-runtime-1.20.0.jar:1.20.0]
	at org.apache.flink.runtime.taskmanager.Task.doRun(Task.java:763) [flink-runtime-1.20.0.jar:1.20.0]
	at org.apache.flink.runtime.taskmanager.Task.run(Task.java:575) [flink-runtime-1.20.0.jar:1.20.0]
	at java.lang.Thread.run(Thread.java:840) [?:?]
2025-08-20 19:53:58.512 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 (1/1)#0] WARN  [o.a.k.c.u.AppInfoParser:68] - Error registering AppInfo mbean
javax.management.InstanceAlreadyExistsException: kafka.consumer:type=app-info,id=orderbook-local-test-group-0
	at com.sun.jmx.mbeanserver.Repository.addMBean(Repository.java:436) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerWithRepository(DefaultMBeanServerInterceptor.java:1865) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerDynamicMBean(DefaultMBeanServerInterceptor.java:960) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerObject(DefaultMBeanServerInterceptor.java:895) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerMBean(DefaultMBeanServerInterceptor.java:320) ~[?:?]
	at com.sun.jmx.mbeanserver.JmxMBeanServer.registerMBean(JmxMBeanServer.java:523) ~[?:?]
	at org.apache.kafka.common.utils.AppInfoParser.registerAppInfo(AppInfoParser.java:64) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.consumer.KafkaConsumer.<init>(KafkaConsumer.java:821) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.consumer.KafkaConsumer.<init>(KafkaConsumer.java:665) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.consumer.KafkaConsumer.<init>(KafkaConsumer.java:646) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.consumer.KafkaConsumer.<init>(KafkaConsumer.java:626) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.flink.connector.kafka.source.reader.KafkaPartitionSplitReader.<init>(KafkaPartitionSplitReader.java:97) ~[flink-connector-kafka-3.4.0-1.20.jar:3.4.0-1.20]
	at org.apache.flink.connector.kafka.source.KafkaSource.lambda$createReader$1(KafkaSource.java:185) ~[flink-connector-kafka-3.4.0-1.20.jar:3.4.0-1.20]
	at org.apache.flink.connector.base.source.reader.fetcher.SplitFetcherManager.createSplitFetcher(SplitFetcherManager.java:259) ~[flink-connector-base-1.20.0.jar:1.20.0]
	at org.apache.flink.connector.base.source.reader.fetcher.SingleThreadFetcherManager.addSplits(SingleThreadFetcherManager.java:148) ~[flink-connector-base-1.20.0.jar:1.20.0]
	at org.apache.flink.connector.base.source.reader.SourceReaderBase.addSplits(SourceReaderBase.java:315) ~[flink-connector-base-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.api.operators.SourceOperator.handleAddSplitsEvent(SourceOperator.java:606) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.api.operators.SourceOperator.handleOperatorEvent(SourceOperator.java:575) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.OperatorEventDispatcherImpl.dispatchEventToHandlers(OperatorEventDispatcherImpl.java:72) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.RegularOperatorChain.dispatchOperatorEvent(RegularOperatorChain.java:80) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.StreamTask.lambda$dispatchOperatorEvent$24(StreamTask.java:1609) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.StreamTaskActionExecutor$1.runThrowing(StreamTaskActionExecutor.java:50) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.mailbox.Mail.run(Mail.java:101) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.mailbox.MailboxProcessor.runMail(MailboxProcessor.java:414) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.mailbox.MailboxProcessor.processMailsNonBlocking(MailboxProcessor.java:399) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.mailbox.MailboxProcessor.processMail(MailboxProcessor.java:361) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.mailbox.MailboxProcessor.runMailboxLoop(MailboxProcessor.java:229) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.StreamTask.runMailboxLoop(StreamTask.java:973) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.StreamTask.invoke(StreamTask.java:917) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.runtime.taskmanager.Task.runWithSystemExitMonitoring(Task.java:970) [flink-runtime-1.20.0.jar:1.20.0]
	at org.apache.flink.runtime.taskmanager.Task.restoreAndInvoke(Task.java:949) [flink-runtime-1.20.0.jar:1.20.0]
	at org.apache.flink.runtime.taskmanager.Task.doRun(Task.java:763) [flink-runtime-1.20.0.jar:1.20.0]
	at org.apache.flink.runtime.taskmanager.Task.run(Task.java:575) [flink-runtime-1.20.0.jar:1.20.0]
	at java.lang.Thread.run(Thread.java:840) [?:?]
2025-08-20 19:53:58.648 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] WARN  [c.f.f.PnLCalculationFunction:180] - 发现无持仓的平仓交易 - 发送到异常流: 交易号=101355625, 合约=JD2504-C-3200, 方向=S, 数量=5
2025-08-20 19:53:58.648 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] WARN  [c.f.f.PnLCalculationFunction:180] - 发现无持仓的平仓交易 - 发送到异常流: 交易号=101355626, 合约=JD2504-C-3200, 方向=S, 数量=5
2025-08-20 19:53:58.649 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] WARN  [c.f.f.PnLCalculationFunction:180] - 发现无持仓的平仓交易 - 发送到异常流: 交易号=101355700, 合约=EB2505, 方向=B, 数量=10
2025-08-20 19:53:58.649 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] WARN  [c.f.f.PnLCalculationFunction:180] - 发现无持仓的平仓交易 - 发送到异常流: 交易号=48060, 合约=SH2505, 方向=S, 数量=1
2025-08-20 19:53:58.649 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] WARN  [c.f.f.PnLCalculationFunction:180] - 发现无持仓的平仓交易 - 发送到异常流: 交易号=100052597, 合约=EB2505, 方向=B, 数量=8
2025-08-20 19:53:58.649 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] WARN  [c.f.f.PnLCalculationFunction:180] - 发现无持仓的平仓交易 - 发送到异常流: 交易号=100052597, 合约=EB2505, 方向=S, 数量=8
2025-08-20 19:53:58.649 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] WARN  [c.f.f.PnLCalculationFunction:180] - 发现无持仓的平仓交易 - 发送到异常流: 交易号=100052678, 合约=EB2505, 方向=B, 数量=1
2025-08-20 19:53:58.650 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] WARN  [c.f.f.PnLCalculationFunction:180] - 发现无持仓的平仓交易 - 发送到异常流: 交易号=100052678, 合约=EB2505, 方向=S, 数量=1
2025-08-20 19:53:58.650 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] WARN  [c.f.f.PnLCalculationFunction:180] - 发现无持仓的平仓交易 - 发送到异常流: 交易号=100052768, 合约=EB2505, 方向=B, 数量=1
2025-08-20 19:53:58.653 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] WARN  [c.f.f.PnLCalculationFunction:180] - 发现无持仓的平仓交易 - 发送到异常流: 交易号=100052770, 合约=EB2505, 方向=B, 数量=1
2025-08-20 19:53:58.654 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] WARN  [c.f.f.PnLCalculationFunction:180] - 发现无持仓的平仓交易 - 发送到异常流: 交易号=100052795, 合约=EB2505, 方向=B, 数量=1
2025-08-20 19:53:58.655 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] WARN  [c.f.f.PnLCalculationFunction:180] - 发现无持仓的平仓交易 - 发送到异常流: 交易号=100052821, 合约=EB2505, 方向=B, 数量=1
2025-08-20 19:53:58.656 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] WARN  [c.f.f.PnLCalculationFunction:180] - 发现无持仓的平仓交易 - 发送到异常流: 交易号=100052822, 合约=EB2505, 方向=B, 数量=2
2025-08-20 19:53:58.656 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] WARN  [c.f.f.PnLCalculationFunction:180] - 发现无持仓的平仓交易 - 发送到异常流: 交易号=100052833, 合约=EB2505, 方向=B, 数量=2
2025-08-20 19:53:58.657 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] WARN  [c.f.f.PnLCalculationFunction:180] - 发现无持仓的平仓交易 - 发送到异常流: 交易号=100052846, 合约=EB2504, 方向=B, 数量=1
2025-08-20 19:53:58.657 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] WARN  [c.f.f.PnLCalculationFunction:180] - 发现无持仓的平仓交易 - 发送到异常流: 交易号=100052847, 合约=EB2505, 方向=B, 数量=1
2025-08-20 19:53:58.657 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] WARN  [c.f.f.PnLCalculationFunction:180] - 发现无持仓的平仓交易 - 发送到异常流: 交易号=100052849, 合约=EB2504, 方向=S, 数量=10
2025-08-20 19:56:12.504 [main] WARN  [o.a.f.c.k.s.KafkaSourceBuilder:497] - Property auto.offset.reset is provided but will be overridden from earliest to earliest
2025-08-20 19:56:12.522 [main] WARN  [o.a.f.c.k.s.KafkaSourceBuilder:497] - Property auto.offset.reset is provided but will be overridden from earliest to earliest
2025-08-20 19:56:12.523 [main] WARN  [o.a.f.c.k.s.KafkaSourceBuilder:497] - Property auto.offset.reset is provided but will be overridden from earliest to earliest
2025-08-20 19:56:12.987 [main] WARN  [o.a.f.r.s.t.DefaultDelegationTokenManager:248] - No tokens obtained so skipping notifications
2025-08-20 19:56:13.090 [main] WARN  [o.a.f.r.w.WebMonitorUtils:76] - Log file environment variable 'log.file' is not set.
2025-08-20 19:56:13.090 [main] WARN  [o.a.f.r.w.WebMonitorUtils:82] - JobManager log files are unavailable in the web dashboard. Log file location not found in environment variable 'log.file' or configuration key 'web.log.path'.
2025-08-20 19:56:13.151 [flink-pekko.actor.default-dispatcher-4] WARN  [o.a.f.r.s.t.DefaultDelegationTokenManager:320] - No tokens obtained so skipping notifications
2025-08-20 19:56:13.152 [flink-pekko.actor.default-dispatcher-4] WARN  [o.a.f.r.s.t.DefaultDelegationTokenManager:335] - Tokens update task not started because either no tokens obtained or none of the tokens specified its renewal date
2025-08-20 19:56:13.256 [SourceCoordinator-Source: Kafka-交易] WARN  [o.a.k.c.a.AdminClientConfig:385] - These configurations '[key.deserializer, value.deserializer, max.poll.records, client.id.prefix, group.id, partition.discovery.interval.ms, heartbeat.interval.ms, enable.auto.commit, fetch.max.wait.ms, fetch.min.bytes, session.timeout.ms, auto.offset.reset]' were supplied but are not used yet.
2025-08-20 19:56:13.256 [SourceCoordinator-Source: Kafka-单腿订单] WARN  [o.a.k.c.a.AdminClientConfig:385] - These configurations '[key.deserializer, value.deserializer, max.poll.records, client.id.prefix, group.id, partition.discovery.interval.ms, heartbeat.interval.ms, enable.auto.commit, fetch.max.wait.ms, fetch.min.bytes, session.timeout.ms, auto.offset.reset]' were supplied but are not used yet.
2025-08-20 19:56:13.256 [SourceCoordinator-Source: Kafka-组合订单] WARN  [o.a.k.c.a.AdminClientConfig:385] - These configurations '[key.deserializer, value.deserializer, max.poll.records, client.id.prefix, group.id, partition.discovery.interval.ms, heartbeat.interval.ms, enable.auto.commit, fetch.max.wait.ms, fetch.min.bytes, session.timeout.ms, auto.offset.reset]' were supplied but are not used yet.
2025-08-20 19:56:13.257 [SourceCoordinator-Source: Kafka-组合订单] WARN  [o.a.k.c.u.AppInfoParser:68] - Error registering AppInfo mbean
javax.management.InstanceAlreadyExistsException: kafka.admin.client:type=app-info,id=orderbook-local-test-group-enumerator-admin-client
	at com.sun.jmx.mbeanserver.Repository.addMBean(Repository.java:436) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerWithRepository(DefaultMBeanServerInterceptor.java:1865) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerDynamicMBean(DefaultMBeanServerInterceptor.java:960) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerObject(DefaultMBeanServerInterceptor.java:895) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerMBean(DefaultMBeanServerInterceptor.java:320) ~[?:?]
	at com.sun.jmx.mbeanserver.JmxMBeanServer.registerMBean(JmxMBeanServer.java:523) ~[?:?]
	at org.apache.kafka.common.utils.AppInfoParser.registerAppInfo(AppInfoParser.java:64) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.admin.KafkaAdminClient.<init>(KafkaAdminClient.java:602) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.admin.KafkaAdminClient.createInternal(KafkaAdminClient.java:544) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.admin.KafkaAdminClient.createInternal(KafkaAdminClient.java:488) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.admin.Admin.create(Admin.java:134) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.admin.AdminClient.create(AdminClient.java:39) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.flink.connector.kafka.source.enumerator.KafkaSourceEnumerator.getKafkaAdminClient(KafkaSourceEnumerator.java:441) ~[flink-connector-kafka-3.4.0-1.20.jar:3.4.0-1.20]
	at org.apache.flink.connector.kafka.source.enumerator.KafkaSourceEnumerator.start(KafkaSourceEnumerator.java:164) ~[flink-connector-kafka-3.4.0-1.20.jar:3.4.0-1.20]
	at org.apache.flink.runtime.source.coordinator.SourceCoordinator.lambda$start$1(SourceCoordinator.java:244) ~[flink-runtime-1.20.0.jar:1.20.0]
	at org.apache.flink.runtime.source.coordinator.SourceCoordinator.lambda$runInEventLoop$10(SourceCoordinator.java:533) ~[flink-runtime-1.20.0.jar:1.20.0]
	at org.apache.flink.util.ThrowableCatchingRunnable.run(ThrowableCatchingRunnable.java:40) [flink-core-1.20.0.jar:1.20.0]
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:539) [?:?]
	at java.util.concurrent.FutureTask.run(FutureTask.java:264) [?:?]
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:304) [?:?]
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136) [?:?]
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635) [?:?]
	at java.lang.Thread.run(Thread.java:840) [?:?]
2025-08-20 19:56:13.260 [SourceCoordinator-Source: Kafka-单腿订单] WARN  [o.a.k.c.u.AppInfoParser:68] - Error registering AppInfo mbean
javax.management.InstanceAlreadyExistsException: kafka.admin.client:type=app-info,id=orderbook-local-test-group-enumerator-admin-client
	at com.sun.jmx.mbeanserver.Repository.addMBean(Repository.java:436) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerWithRepository(DefaultMBeanServerInterceptor.java:1865) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerDynamicMBean(DefaultMBeanServerInterceptor.java:960) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerObject(DefaultMBeanServerInterceptor.java:895) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerMBean(DefaultMBeanServerInterceptor.java:320) ~[?:?]
	at com.sun.jmx.mbeanserver.JmxMBeanServer.registerMBean(JmxMBeanServer.java:523) ~[?:?]
	at org.apache.kafka.common.utils.AppInfoParser.registerAppInfo(AppInfoParser.java:64) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.admin.KafkaAdminClient.<init>(KafkaAdminClient.java:602) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.admin.KafkaAdminClient.createInternal(KafkaAdminClient.java:544) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.admin.KafkaAdminClient.createInternal(KafkaAdminClient.java:488) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.admin.Admin.create(Admin.java:134) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.admin.AdminClient.create(AdminClient.java:39) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.flink.connector.kafka.source.enumerator.KafkaSourceEnumerator.getKafkaAdminClient(KafkaSourceEnumerator.java:441) ~[flink-connector-kafka-3.4.0-1.20.jar:3.4.0-1.20]
	at org.apache.flink.connector.kafka.source.enumerator.KafkaSourceEnumerator.start(KafkaSourceEnumerator.java:164) ~[flink-connector-kafka-3.4.0-1.20.jar:3.4.0-1.20]
	at org.apache.flink.runtime.source.coordinator.SourceCoordinator.lambda$start$1(SourceCoordinator.java:244) ~[flink-runtime-1.20.0.jar:1.20.0]
	at org.apache.flink.runtime.source.coordinator.SourceCoordinator.lambda$runInEventLoop$10(SourceCoordinator.java:533) ~[flink-runtime-1.20.0.jar:1.20.0]
	at org.apache.flink.util.ThrowableCatchingRunnable.run(ThrowableCatchingRunnable.java:40) [flink-core-1.20.0.jar:1.20.0]
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:539) [?:?]
	at java.util.concurrent.FutureTask.run(FutureTask.java:264) [?:?]
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:304) [?:?]
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136) [?:?]
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635) [?:?]
	at java.lang.Thread.run(Thread.java:840) [?:?]
2025-08-20 19:56:13.408 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] WARN  [o.a.k.c.c.ConsumerConfig:385] - These configurations '[client.id.prefix, partition.discovery.interval.ms]' were supplied but are not used yet.
2025-08-20 19:56:13.408 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 (1/1)#0] WARN  [o.a.k.c.c.ConsumerConfig:385] - These configurations '[client.id.prefix, partition.discovery.interval.ms]' were supplied but are not used yet.
2025-08-20 19:56:13.408 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 (1/1)#0] WARN  [o.a.k.c.c.ConsumerConfig:385] - These configurations '[client.id.prefix, partition.discovery.interval.ms]' were supplied but are not used yet.
2025-08-20 19:56:13.408 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 (1/1)#0] WARN  [o.a.k.c.u.AppInfoParser:68] - Error registering AppInfo mbean
javax.management.InstanceAlreadyExistsException: kafka.consumer:type=app-info,id=orderbook-local-test-group-0
	at com.sun.jmx.mbeanserver.Repository.addMBean(Repository.java:436) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerWithRepository(DefaultMBeanServerInterceptor.java:1865) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerDynamicMBean(DefaultMBeanServerInterceptor.java:960) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerObject(DefaultMBeanServerInterceptor.java:895) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerMBean(DefaultMBeanServerInterceptor.java:320) ~[?:?]
	at com.sun.jmx.mbeanserver.JmxMBeanServer.registerMBean(JmxMBeanServer.java:523) ~[?:?]
	at org.apache.kafka.common.utils.AppInfoParser.registerAppInfo(AppInfoParser.java:64) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.consumer.KafkaConsumer.<init>(KafkaConsumer.java:821) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.consumer.KafkaConsumer.<init>(KafkaConsumer.java:665) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.consumer.KafkaConsumer.<init>(KafkaConsumer.java:646) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.consumer.KafkaConsumer.<init>(KafkaConsumer.java:626) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.flink.connector.kafka.source.reader.KafkaPartitionSplitReader.<init>(KafkaPartitionSplitReader.java:97) ~[flink-connector-kafka-3.4.0-1.20.jar:3.4.0-1.20]
	at org.apache.flink.connector.kafka.source.KafkaSource.lambda$createReader$1(KafkaSource.java:185) ~[flink-connector-kafka-3.4.0-1.20.jar:3.4.0-1.20]
	at org.apache.flink.connector.base.source.reader.fetcher.SplitFetcherManager.createSplitFetcher(SplitFetcherManager.java:259) ~[flink-connector-base-1.20.0.jar:1.20.0]
	at org.apache.flink.connector.base.source.reader.fetcher.SingleThreadFetcherManager.addSplits(SingleThreadFetcherManager.java:148) ~[flink-connector-base-1.20.0.jar:1.20.0]
	at org.apache.flink.connector.base.source.reader.SourceReaderBase.addSplits(SourceReaderBase.java:315) ~[flink-connector-base-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.api.operators.SourceOperator.handleAddSplitsEvent(SourceOperator.java:606) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.api.operators.SourceOperator.handleOperatorEvent(SourceOperator.java:575) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.OperatorEventDispatcherImpl.dispatchEventToHandlers(OperatorEventDispatcherImpl.java:72) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.RegularOperatorChain.dispatchOperatorEvent(RegularOperatorChain.java:80) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.StreamTask.lambda$dispatchOperatorEvent$24(StreamTask.java:1609) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.StreamTaskActionExecutor$1.runThrowing(StreamTaskActionExecutor.java:50) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.mailbox.Mail.run(Mail.java:101) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.mailbox.MailboxProcessor.runMail(MailboxProcessor.java:414) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.mailbox.MailboxProcessor.processMailsWhenDefaultActionUnavailable(MailboxProcessor.java:383) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.mailbox.MailboxProcessor.processMail(MailboxProcessor.java:368) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.mailbox.MailboxProcessor.runMailboxLoop(MailboxProcessor.java:229) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.StreamTask.runMailboxLoop(StreamTask.java:973) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.StreamTask.invoke(StreamTask.java:917) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.runtime.taskmanager.Task.runWithSystemExitMonitoring(Task.java:970) [flink-runtime-1.20.0.jar:1.20.0]
	at org.apache.flink.runtime.taskmanager.Task.restoreAndInvoke(Task.java:949) [flink-runtime-1.20.0.jar:1.20.0]
	at org.apache.flink.runtime.taskmanager.Task.doRun(Task.java:763) [flink-runtime-1.20.0.jar:1.20.0]
	at org.apache.flink.runtime.taskmanager.Task.run(Task.java:575) [flink-runtime-1.20.0.jar:1.20.0]
	at java.lang.Thread.run(Thread.java:840) [?:?]
2025-08-20 19:56:13.409 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 (1/1)#0] WARN  [o.a.k.c.u.AppInfoParser:68] - Error registering AppInfo mbean
javax.management.InstanceAlreadyExistsException: kafka.consumer:type=app-info,id=orderbook-local-test-group-0
	at com.sun.jmx.mbeanserver.Repository.addMBean(Repository.java:436) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerWithRepository(DefaultMBeanServerInterceptor.java:1865) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerDynamicMBean(DefaultMBeanServerInterceptor.java:960) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerObject(DefaultMBeanServerInterceptor.java:895) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerMBean(DefaultMBeanServerInterceptor.java:320) ~[?:?]
	at com.sun.jmx.mbeanserver.JmxMBeanServer.registerMBean(JmxMBeanServer.java:523) ~[?:?]
	at org.apache.kafka.common.utils.AppInfoParser.registerAppInfo(AppInfoParser.java:64) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.consumer.KafkaConsumer.<init>(KafkaConsumer.java:821) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.consumer.KafkaConsumer.<init>(KafkaConsumer.java:665) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.consumer.KafkaConsumer.<init>(KafkaConsumer.java:646) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.consumer.KafkaConsumer.<init>(KafkaConsumer.java:626) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.flink.connector.kafka.source.reader.KafkaPartitionSplitReader.<init>(KafkaPartitionSplitReader.java:97) ~[flink-connector-kafka-3.4.0-1.20.jar:3.4.0-1.20]
	at org.apache.flink.connector.kafka.source.KafkaSource.lambda$createReader$1(KafkaSource.java:185) ~[flink-connector-kafka-3.4.0-1.20.jar:3.4.0-1.20]
	at org.apache.flink.connector.base.source.reader.fetcher.SplitFetcherManager.createSplitFetcher(SplitFetcherManager.java:259) ~[flink-connector-base-1.20.0.jar:1.20.0]
	at org.apache.flink.connector.base.source.reader.fetcher.SingleThreadFetcherManager.addSplits(SingleThreadFetcherManager.java:148) ~[flink-connector-base-1.20.0.jar:1.20.0]
	at org.apache.flink.connector.base.source.reader.SourceReaderBase.addSplits(SourceReaderBase.java:315) ~[flink-connector-base-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.api.operators.SourceOperator.handleAddSplitsEvent(SourceOperator.java:606) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.api.operators.SourceOperator.handleOperatorEvent(SourceOperator.java:575) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.OperatorEventDispatcherImpl.dispatchEventToHandlers(OperatorEventDispatcherImpl.java:72) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.RegularOperatorChain.dispatchOperatorEvent(RegularOperatorChain.java:80) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.StreamTask.lambda$dispatchOperatorEvent$24(StreamTask.java:1609) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.StreamTaskActionExecutor$1.runThrowing(StreamTaskActionExecutor.java:50) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.mailbox.Mail.run(Mail.java:101) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.mailbox.MailboxProcessor.runMail(MailboxProcessor.java:414) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.mailbox.MailboxProcessor.processMailsWhenDefaultActionUnavailable(MailboxProcessor.java:383) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.mailbox.MailboxProcessor.processMail(MailboxProcessor.java:368) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.mailbox.MailboxProcessor.runMailboxLoop(MailboxProcessor.java:229) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.StreamTask.runMailboxLoop(StreamTask.java:973) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.StreamTask.invoke(StreamTask.java:917) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.runtime.taskmanager.Task.runWithSystemExitMonitoring(Task.java:970) [flink-runtime-1.20.0.jar:1.20.0]
	at org.apache.flink.runtime.taskmanager.Task.restoreAndInvoke(Task.java:949) [flink-runtime-1.20.0.jar:1.20.0]
	at org.apache.flink.runtime.taskmanager.Task.doRun(Task.java:763) [flink-runtime-1.20.0.jar:1.20.0]
	at org.apache.flink.runtime.taskmanager.Task.run(Task.java:575) [flink-runtime-1.20.0.jar:1.20.0]
	at java.lang.Thread.run(Thread.java:840) [?:?]
2025-08-20 19:56:13.537 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] WARN  [c.f.f.PnLCalculationFunction:180] - 发现无持仓的平仓交易 - 发送到异常流: 交易号=101355625, 合约=JD2504-C-3200, 方向=S, 数量=5
2025-08-20 19:56:13.538 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] WARN  [c.f.f.PnLCalculationFunction:180] - 发现无持仓的平仓交易 - 发送到异常流: 交易号=101355626, 合约=JD2504-C-3200, 方向=S, 数量=5
2025-08-20 19:56:13.538 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] WARN  [c.f.f.PnLCalculationFunction:180] - 发现无持仓的平仓交易 - 发送到异常流: 交易号=101355700, 合约=EB2505, 方向=B, 数量=10
2025-08-20 19:56:13.538 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] WARN  [c.f.f.PnLCalculationFunction:180] - 发现无持仓的平仓交易 - 发送到异常流: 交易号=48060, 合约=SH2505, 方向=S, 数量=1
2025-08-20 19:56:13.538 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] WARN  [c.f.f.PnLCalculationFunction:180] - 发现无持仓的平仓交易 - 发送到异常流: 交易号=100052597, 合约=EB2505, 方向=B, 数量=8
2025-08-20 19:56:13.539 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] WARN  [c.f.f.PnLCalculationFunction:180] - 发现无持仓的平仓交易 - 发送到异常流: 交易号=100052597, 合约=EB2505, 方向=S, 数量=8
2025-08-20 19:56:13.539 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] WARN  [c.f.f.PnLCalculationFunction:180] - 发现无持仓的平仓交易 - 发送到异常流: 交易号=100052678, 合约=EB2505, 方向=B, 数量=1
2025-08-20 19:56:13.539 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] WARN  [c.f.f.PnLCalculationFunction:180] - 发现无持仓的平仓交易 - 发送到异常流: 交易号=100052678, 合约=EB2505, 方向=S, 数量=1
2025-08-20 19:56:13.539 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] WARN  [c.f.f.PnLCalculationFunction:180] - 发现无持仓的平仓交易 - 发送到异常流: 交易号=100052768, 合约=EB2505, 方向=B, 数量=1
2025-08-20 19:56:13.544 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] WARN  [c.f.f.PnLCalculationFunction:180] - 发现无持仓的平仓交易 - 发送到异常流: 交易号=100052770, 合约=EB2505, 方向=B, 数量=1
2025-08-20 19:56:13.544 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] WARN  [c.f.f.PnLCalculationFunction:180] - 发现无持仓的平仓交易 - 发送到异常流: 交易号=100052795, 合约=EB2505, 方向=B, 数量=1
2025-08-20 19:56:13.546 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] WARN  [c.f.f.PnLCalculationFunction:180] - 发现无持仓的平仓交易 - 发送到异常流: 交易号=100052821, 合约=EB2505, 方向=B, 数量=1
2025-08-20 19:56:13.546 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] WARN  [c.f.f.PnLCalculationFunction:180] - 发现无持仓的平仓交易 - 发送到异常流: 交易号=100052822, 合约=EB2505, 方向=B, 数量=2
2025-08-20 19:56:13.547 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] WARN  [c.f.f.PnLCalculationFunction:180] - 发现无持仓的平仓交易 - 发送到异常流: 交易号=100052833, 合约=EB2505, 方向=B, 数量=2
2025-08-20 19:56:13.548 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] WARN  [c.f.f.PnLCalculationFunction:180] - 发现无持仓的平仓交易 - 发送到异常流: 交易号=100052846, 合约=EB2504, 方向=B, 数量=1
2025-08-20 19:56:13.548 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] WARN  [c.f.f.PnLCalculationFunction:180] - 发现无持仓的平仓交易 - 发送到异常流: 交易号=100052847, 合约=EB2505, 方向=B, 数量=1
2025-08-20 19:56:13.549 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] WARN  [c.f.f.PnLCalculationFunction:180] - 发现无持仓的平仓交易 - 发送到异常流: 交易号=100052849, 合约=EB2504, 方向=S, 数量=10
2025-08-20 19:56:37.934 [main] WARN  [o.a.f.c.k.s.KafkaSourceBuilder:497] - Property auto.offset.reset is provided but will be overridden from earliest to earliest
2025-08-20 19:56:37.951 [main] WARN  [o.a.f.c.k.s.KafkaSourceBuilder:497] - Property auto.offset.reset is provided but will be overridden from earliest to earliest
2025-08-20 19:56:37.952 [main] WARN  [o.a.f.c.k.s.KafkaSourceBuilder:497] - Property auto.offset.reset is provided but will be overridden from earliest to earliest
2025-08-20 19:56:38.449 [main] WARN  [o.a.f.r.s.t.DefaultDelegationTokenManager:248] - No tokens obtained so skipping notifications
2025-08-20 19:56:38.556 [main] WARN  [o.a.f.r.w.WebMonitorUtils:76] - Log file environment variable 'log.file' is not set.
2025-08-20 19:56:38.556 [main] WARN  [o.a.f.r.w.WebMonitorUtils:82] - JobManager log files are unavailable in the web dashboard. Log file location not found in environment variable 'log.file' or configuration key 'web.log.path'.
2025-08-20 19:56:38.620 [flink-pekko.actor.default-dispatcher-6] WARN  [o.a.f.r.s.t.DefaultDelegationTokenManager:320] - No tokens obtained so skipping notifications
2025-08-20 19:56:38.621 [flink-pekko.actor.default-dispatcher-6] WARN  [o.a.f.r.s.t.DefaultDelegationTokenManager:335] - Tokens update task not started because either no tokens obtained or none of the tokens specified its renewal date
2025-08-20 19:56:38.741 [SourceCoordinator-Source: Kafka-单腿订单] WARN  [o.a.k.c.a.AdminClientConfig:385] - These configurations '[key.deserializer, value.deserializer, max.poll.records, client.id.prefix, group.id, partition.discovery.interval.ms, heartbeat.interval.ms, enable.auto.commit, fetch.max.wait.ms, fetch.min.bytes, session.timeout.ms, auto.offset.reset]' were supplied but are not used yet.
2025-08-20 19:56:38.741 [SourceCoordinator-Source: Kafka-组合订单] WARN  [o.a.k.c.a.AdminClientConfig:385] - These configurations '[key.deserializer, value.deserializer, max.poll.records, client.id.prefix, group.id, partition.discovery.interval.ms, heartbeat.interval.ms, enable.auto.commit, fetch.max.wait.ms, fetch.min.bytes, session.timeout.ms, auto.offset.reset]' were supplied but are not used yet.
2025-08-20 19:56:38.741 [SourceCoordinator-Source: Kafka-交易] WARN  [o.a.k.c.a.AdminClientConfig:385] - These configurations '[key.deserializer, value.deserializer, max.poll.records, client.id.prefix, group.id, partition.discovery.interval.ms, heartbeat.interval.ms, enable.auto.commit, fetch.max.wait.ms, fetch.min.bytes, session.timeout.ms, auto.offset.reset]' were supplied but are not used yet.
2025-08-20 19:56:38.743 [SourceCoordinator-Source: Kafka-交易] WARN  [o.a.k.c.u.AppInfoParser:68] - Error registering AppInfo mbean
javax.management.InstanceAlreadyExistsException: kafka.admin.client:type=app-info,id=orderbook-local-test-group-enumerator-admin-client
	at com.sun.jmx.mbeanserver.Repository.addMBean(Repository.java:436) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerWithRepository(DefaultMBeanServerInterceptor.java:1865) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerDynamicMBean(DefaultMBeanServerInterceptor.java:960) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerObject(DefaultMBeanServerInterceptor.java:895) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerMBean(DefaultMBeanServerInterceptor.java:320) ~[?:?]
	at com.sun.jmx.mbeanserver.JmxMBeanServer.registerMBean(JmxMBeanServer.java:523) ~[?:?]
	at org.apache.kafka.common.utils.AppInfoParser.registerAppInfo(AppInfoParser.java:64) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.admin.KafkaAdminClient.<init>(KafkaAdminClient.java:602) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.admin.KafkaAdminClient.createInternal(KafkaAdminClient.java:544) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.admin.KafkaAdminClient.createInternal(KafkaAdminClient.java:488) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.admin.Admin.create(Admin.java:134) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.admin.AdminClient.create(AdminClient.java:39) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.flink.connector.kafka.source.enumerator.KafkaSourceEnumerator.getKafkaAdminClient(KafkaSourceEnumerator.java:441) ~[flink-connector-kafka-3.4.0-1.20.jar:3.4.0-1.20]
	at org.apache.flink.connector.kafka.source.enumerator.KafkaSourceEnumerator.start(KafkaSourceEnumerator.java:164) ~[flink-connector-kafka-3.4.0-1.20.jar:3.4.0-1.20]
	at org.apache.flink.runtime.source.coordinator.SourceCoordinator.lambda$start$1(SourceCoordinator.java:244) ~[flink-runtime-1.20.0.jar:1.20.0]
	at org.apache.flink.runtime.source.coordinator.SourceCoordinator.lambda$runInEventLoop$10(SourceCoordinator.java:533) ~[flink-runtime-1.20.0.jar:1.20.0]
	at org.apache.flink.util.ThrowableCatchingRunnable.run(ThrowableCatchingRunnable.java:40) [flink-core-1.20.0.jar:1.20.0]
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:539) [?:?]
	at java.util.concurrent.FutureTask.run(FutureTask.java:264) [?:?]
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:304) [?:?]
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136) [?:?]
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635) [?:?]
	at java.lang.Thread.run(Thread.java:840) [?:?]
2025-08-20 19:56:38.746 [SourceCoordinator-Source: Kafka-组合订单] WARN  [o.a.k.c.u.AppInfoParser:68] - Error registering AppInfo mbean
javax.management.InstanceAlreadyExistsException: kafka.admin.client:type=app-info,id=orderbook-local-test-group-enumerator-admin-client
	at com.sun.jmx.mbeanserver.Repository.addMBean(Repository.java:436) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerWithRepository(DefaultMBeanServerInterceptor.java:1865) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerDynamicMBean(DefaultMBeanServerInterceptor.java:960) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerObject(DefaultMBeanServerInterceptor.java:895) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerMBean(DefaultMBeanServerInterceptor.java:320) ~[?:?]
	at com.sun.jmx.mbeanserver.JmxMBeanServer.registerMBean(JmxMBeanServer.java:523) ~[?:?]
	at org.apache.kafka.common.utils.AppInfoParser.registerAppInfo(AppInfoParser.java:64) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.admin.KafkaAdminClient.<init>(KafkaAdminClient.java:602) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.admin.KafkaAdminClient.createInternal(KafkaAdminClient.java:544) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.admin.KafkaAdminClient.createInternal(KafkaAdminClient.java:488) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.admin.Admin.create(Admin.java:134) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.admin.AdminClient.create(AdminClient.java:39) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.flink.connector.kafka.source.enumerator.KafkaSourceEnumerator.getKafkaAdminClient(KafkaSourceEnumerator.java:441) ~[flink-connector-kafka-3.4.0-1.20.jar:3.4.0-1.20]
	at org.apache.flink.connector.kafka.source.enumerator.KafkaSourceEnumerator.start(KafkaSourceEnumerator.java:164) ~[flink-connector-kafka-3.4.0-1.20.jar:3.4.0-1.20]
	at org.apache.flink.runtime.source.coordinator.SourceCoordinator.lambda$start$1(SourceCoordinator.java:244) ~[flink-runtime-1.20.0.jar:1.20.0]
	at org.apache.flink.runtime.source.coordinator.SourceCoordinator.lambda$runInEventLoop$10(SourceCoordinator.java:533) ~[flink-runtime-1.20.0.jar:1.20.0]
	at org.apache.flink.util.ThrowableCatchingRunnable.run(ThrowableCatchingRunnable.java:40) [flink-core-1.20.0.jar:1.20.0]
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:539) [?:?]
	at java.util.concurrent.FutureTask.run(FutureTask.java:264) [?:?]
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:304) [?:?]
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136) [?:?]
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635) [?:?]
	at java.lang.Thread.run(Thread.java:840) [?:?]
2025-08-20 19:56:38.892 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 (1/1)#0] WARN  [o.a.k.c.c.ConsumerConfig:385] - These configurations '[client.id.prefix, partition.discovery.interval.ms]' were supplied but are not used yet.
2025-08-20 19:56:38.892 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 (1/1)#0] WARN  [o.a.k.c.c.ConsumerConfig:385] - These configurations '[client.id.prefix, partition.discovery.interval.ms]' were supplied but are not used yet.
2025-08-20 19:56:38.892 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] WARN  [o.a.k.c.c.ConsumerConfig:385] - These configurations '[client.id.prefix, partition.discovery.interval.ms]' were supplied but are not used yet.
2025-08-20 19:56:38.892 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 (1/1)#0] WARN  [o.a.k.c.u.AppInfoParser:68] - Error registering AppInfo mbean
javax.management.InstanceAlreadyExistsException: kafka.consumer:type=app-info,id=orderbook-local-test-group-0
	at com.sun.jmx.mbeanserver.Repository.addMBean(Repository.java:436) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerWithRepository(DefaultMBeanServerInterceptor.java:1865) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerDynamicMBean(DefaultMBeanServerInterceptor.java:960) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerObject(DefaultMBeanServerInterceptor.java:895) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerMBean(DefaultMBeanServerInterceptor.java:320) ~[?:?]
	at com.sun.jmx.mbeanserver.JmxMBeanServer.registerMBean(JmxMBeanServer.java:523) ~[?:?]
	at org.apache.kafka.common.utils.AppInfoParser.registerAppInfo(AppInfoParser.java:64) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.consumer.KafkaConsumer.<init>(KafkaConsumer.java:821) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.consumer.KafkaConsumer.<init>(KafkaConsumer.java:665) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.consumer.KafkaConsumer.<init>(KafkaConsumer.java:646) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.consumer.KafkaConsumer.<init>(KafkaConsumer.java:626) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.flink.connector.kafka.source.reader.KafkaPartitionSplitReader.<init>(KafkaPartitionSplitReader.java:97) ~[flink-connector-kafka-3.4.0-1.20.jar:3.4.0-1.20]
	at org.apache.flink.connector.kafka.source.KafkaSource.lambda$createReader$1(KafkaSource.java:185) ~[flink-connector-kafka-3.4.0-1.20.jar:3.4.0-1.20]
	at org.apache.flink.connector.base.source.reader.fetcher.SplitFetcherManager.createSplitFetcher(SplitFetcherManager.java:259) ~[flink-connector-base-1.20.0.jar:1.20.0]
	at org.apache.flink.connector.base.source.reader.fetcher.SingleThreadFetcherManager.addSplits(SingleThreadFetcherManager.java:148) ~[flink-connector-base-1.20.0.jar:1.20.0]
	at org.apache.flink.connector.base.source.reader.SourceReaderBase.addSplits(SourceReaderBase.java:315) ~[flink-connector-base-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.api.operators.SourceOperator.handleAddSplitsEvent(SourceOperator.java:606) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.api.operators.SourceOperator.handleOperatorEvent(SourceOperator.java:575) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.OperatorEventDispatcherImpl.dispatchEventToHandlers(OperatorEventDispatcherImpl.java:72) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.RegularOperatorChain.dispatchOperatorEvent(RegularOperatorChain.java:80) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.StreamTask.lambda$dispatchOperatorEvent$24(StreamTask.java:1609) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.StreamTaskActionExecutor$1.runThrowing(StreamTaskActionExecutor.java:50) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.mailbox.Mail.run(Mail.java:101) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.mailbox.MailboxProcessor.runMail(MailboxProcessor.java:414) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.mailbox.MailboxProcessor.processMailsWhenDefaultActionUnavailable(MailboxProcessor.java:383) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.mailbox.MailboxProcessor.processMail(MailboxProcessor.java:368) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.mailbox.MailboxProcessor.runMailboxLoop(MailboxProcessor.java:229) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.StreamTask.runMailboxLoop(StreamTask.java:973) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.StreamTask.invoke(StreamTask.java:917) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.runtime.taskmanager.Task.runWithSystemExitMonitoring(Task.java:970) [flink-runtime-1.20.0.jar:1.20.0]
	at org.apache.flink.runtime.taskmanager.Task.restoreAndInvoke(Task.java:949) [flink-runtime-1.20.0.jar:1.20.0]
	at org.apache.flink.runtime.taskmanager.Task.doRun(Task.java:763) [flink-runtime-1.20.0.jar:1.20.0]
	at org.apache.flink.runtime.taskmanager.Task.run(Task.java:575) [flink-runtime-1.20.0.jar:1.20.0]
	at java.lang.Thread.run(Thread.java:840) [?:?]
2025-08-20 19:56:38.893 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] WARN  [o.a.k.c.u.AppInfoParser:68] - Error registering AppInfo mbean
javax.management.InstanceAlreadyExistsException: kafka.consumer:type=app-info,id=orderbook-local-test-group-0
	at com.sun.jmx.mbeanserver.Repository.addMBean(Repository.java:436) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerWithRepository(DefaultMBeanServerInterceptor.java:1865) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerDynamicMBean(DefaultMBeanServerInterceptor.java:960) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerObject(DefaultMBeanServerInterceptor.java:895) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerMBean(DefaultMBeanServerInterceptor.java:320) ~[?:?]
	at com.sun.jmx.mbeanserver.JmxMBeanServer.registerMBean(JmxMBeanServer.java:523) ~[?:?]
	at org.apache.kafka.common.utils.AppInfoParser.registerAppInfo(AppInfoParser.java:64) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.consumer.KafkaConsumer.<init>(KafkaConsumer.java:821) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.consumer.KafkaConsumer.<init>(KafkaConsumer.java:665) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.consumer.KafkaConsumer.<init>(KafkaConsumer.java:646) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.consumer.KafkaConsumer.<init>(KafkaConsumer.java:626) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.flink.connector.kafka.source.reader.KafkaPartitionSplitReader.<init>(KafkaPartitionSplitReader.java:97) ~[flink-connector-kafka-3.4.0-1.20.jar:3.4.0-1.20]
	at org.apache.flink.connector.kafka.source.KafkaSource.lambda$createReader$1(KafkaSource.java:185) ~[flink-connector-kafka-3.4.0-1.20.jar:3.4.0-1.20]
	at org.apache.flink.connector.base.source.reader.fetcher.SplitFetcherManager.createSplitFetcher(SplitFetcherManager.java:259) ~[flink-connector-base-1.20.0.jar:1.20.0]
	at org.apache.flink.connector.base.source.reader.fetcher.SingleThreadFetcherManager.addSplits(SingleThreadFetcherManager.java:148) ~[flink-connector-base-1.20.0.jar:1.20.0]
	at org.apache.flink.connector.base.source.reader.SourceReaderBase.addSplits(SourceReaderBase.java:315) ~[flink-connector-base-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.api.operators.SourceOperator.handleAddSplitsEvent(SourceOperator.java:606) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.api.operators.SourceOperator.handleOperatorEvent(SourceOperator.java:575) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.OperatorEventDispatcherImpl.dispatchEventToHandlers(OperatorEventDispatcherImpl.java:72) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.RegularOperatorChain.dispatchOperatorEvent(RegularOperatorChain.java:80) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.StreamTask.lambda$dispatchOperatorEvent$24(StreamTask.java:1609) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.StreamTaskActionExecutor$1.runThrowing(StreamTaskActionExecutor.java:50) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.mailbox.Mail.run(Mail.java:101) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.mailbox.MailboxProcessor.runMail(MailboxProcessor.java:414) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.mailbox.MailboxProcessor.processMailsWhenDefaultActionUnavailable(MailboxProcessor.java:383) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.mailbox.MailboxProcessor.processMail(MailboxProcessor.java:368) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.mailbox.MailboxProcessor.runMailboxLoop(MailboxProcessor.java:229) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.StreamTask.runMailboxLoop(StreamTask.java:973) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.StreamTask.invoke(StreamTask.java:917) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.runtime.taskmanager.Task.runWithSystemExitMonitoring(Task.java:970) [flink-runtime-1.20.0.jar:1.20.0]
	at org.apache.flink.runtime.taskmanager.Task.restoreAndInvoke(Task.java:949) [flink-runtime-1.20.0.jar:1.20.0]
	at org.apache.flink.runtime.taskmanager.Task.doRun(Task.java:763) [flink-runtime-1.20.0.jar:1.20.0]
	at org.apache.flink.runtime.taskmanager.Task.run(Task.java:575) [flink-runtime-1.20.0.jar:1.20.0]
	at java.lang.Thread.run(Thread.java:840) [?:?]
2025-08-20 19:56:39.003 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] WARN  [c.f.f.PnLCalculationFunction:180] - 发现无持仓的平仓交易 - 发送到异常流: 交易号=101355625, 合约=JD2504-C-3200, 方向=S, 数量=5
2025-08-20 19:56:39.004 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] WARN  [c.f.f.PnLCalculationFunction:180] - 发现无持仓的平仓交易 - 发送到异常流: 交易号=101355626, 合约=JD2504-C-3200, 方向=S, 数量=5
2025-08-20 19:56:39.005 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] WARN  [c.f.f.PnLCalculationFunction:180] - 发现无持仓的平仓交易 - 发送到异常流: 交易号=101355700, 合约=EB2505, 方向=B, 数量=10
2025-08-20 19:56:39.006 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] WARN  [c.f.f.PnLCalculationFunction:180] - 发现无持仓的平仓交易 - 发送到异常流: 交易号=48060, 合约=SH2505, 方向=S, 数量=1
2025-08-20 19:56:39.006 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] WARN  [c.f.f.PnLCalculationFunction:180] - 发现无持仓的平仓交易 - 发送到异常流: 交易号=100052597, 合约=EB2505, 方向=B, 数量=8
2025-08-20 19:56:39.006 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] WARN  [c.f.f.PnLCalculationFunction:180] - 发现无持仓的平仓交易 - 发送到异常流: 交易号=100052597, 合约=EB2505, 方向=S, 数量=8
2025-08-20 19:56:39.007 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] WARN  [c.f.f.PnLCalculationFunction:180] - 发现无持仓的平仓交易 - 发送到异常流: 交易号=100052678, 合约=EB2505, 方向=B, 数量=1
2025-08-20 19:56:39.007 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] WARN  [c.f.f.PnLCalculationFunction:180] - 发现无持仓的平仓交易 - 发送到异常流: 交易号=100052678, 合约=EB2505, 方向=S, 数量=1
2025-08-20 19:56:39.007 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] WARN  [c.f.f.PnLCalculationFunction:180] - 发现无持仓的平仓交易 - 发送到异常流: 交易号=100052768, 合约=EB2505, 方向=B, 数量=1
2025-08-20 19:56:39.009 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] WARN  [c.f.f.PnLCalculationFunction:180] - 发现无持仓的平仓交易 - 发送到异常流: 交易号=100052770, 合约=EB2505, 方向=B, 数量=1
2025-08-20 19:56:39.009 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] WARN  [c.f.f.PnLCalculationFunction:180] - 发现无持仓的平仓交易 - 发送到异常流: 交易号=100052795, 合约=EB2505, 方向=B, 数量=1
2025-08-20 19:56:39.010 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] WARN  [c.f.f.PnLCalculationFunction:180] - 发现无持仓的平仓交易 - 发送到异常流: 交易号=100052821, 合约=EB2505, 方向=B, 数量=1
2025-08-20 19:56:39.011 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] WARN  [c.f.f.PnLCalculationFunction:180] - 发现无持仓的平仓交易 - 发送到异常流: 交易号=100052822, 合约=EB2505, 方向=B, 数量=2
2025-08-20 19:56:39.012 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] WARN  [c.f.f.PnLCalculationFunction:180] - 发现无持仓的平仓交易 - 发送到异常流: 交易号=100052833, 合约=EB2505, 方向=B, 数量=2
2025-08-20 19:56:39.013 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] WARN  [c.f.f.PnLCalculationFunction:180] - 发现无持仓的平仓交易 - 发送到异常流: 交易号=100052846, 合约=EB2504, 方向=B, 数量=1
2025-08-20 19:56:39.013 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] WARN  [c.f.f.PnLCalculationFunction:180] - 发现无持仓的平仓交易 - 发送到异常流: 交易号=100052847, 合约=EB2505, 方向=B, 数量=1
2025-08-20 19:56:39.014 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] WARN  [c.f.f.PnLCalculationFunction:180] - 发现无持仓的平仓交易 - 发送到异常流: 交易号=100052849, 合约=EB2504, 方向=S, 数量=10
2025-08-20 19:57:06.997 [main] WARN  [o.a.f.c.k.s.KafkaSourceBuilder:497] - Property auto.offset.reset is provided but will be overridden from earliest to earliest
2025-08-20 19:57:07.014 [main] WARN  [o.a.f.c.k.s.KafkaSourceBuilder:497] - Property auto.offset.reset is provided but will be overridden from earliest to earliest
2025-08-20 19:57:07.014 [main] WARN  [o.a.f.c.k.s.KafkaSourceBuilder:497] - Property auto.offset.reset is provided but will be overridden from earliest to earliest
2025-08-20 19:57:07.692 [main] WARN  [o.a.f.r.s.t.DefaultDelegationTokenManager:248] - No tokens obtained so skipping notifications
2025-08-20 19:57:07.805 [main] WARN  [o.a.f.r.w.WebMonitorUtils:76] - Log file environment variable 'log.file' is not set.
2025-08-20 19:57:07.805 [main] WARN  [o.a.f.r.w.WebMonitorUtils:82] - JobManager log files are unavailable in the web dashboard. Log file location not found in environment variable 'log.file' or configuration key 'web.log.path'.
2025-08-20 19:57:07.864 [flink-pekko.actor.default-dispatcher-5] WARN  [o.a.f.r.s.t.DefaultDelegationTokenManager:320] - No tokens obtained so skipping notifications
2025-08-20 19:57:07.864 [flink-pekko.actor.default-dispatcher-5] WARN  [o.a.f.r.s.t.DefaultDelegationTokenManager:335] - Tokens update task not started because either no tokens obtained or none of the tokens specified its renewal date
2025-08-20 19:57:07.964 [SourceCoordinator-Source: Kafka-交易] WARN  [o.a.k.c.a.AdminClientConfig:385] - These configurations '[key.deserializer, value.deserializer, max.poll.records, client.id.prefix, group.id, partition.discovery.interval.ms, heartbeat.interval.ms, enable.auto.commit, fetch.max.wait.ms, fetch.min.bytes, session.timeout.ms, auto.offset.reset]' were supplied but are not used yet.
2025-08-20 19:57:07.964 [SourceCoordinator-Source: Kafka-组合订单] WARN  [o.a.k.c.a.AdminClientConfig:385] - These configurations '[key.deserializer, value.deserializer, max.poll.records, client.id.prefix, group.id, partition.discovery.interval.ms, heartbeat.interval.ms, enable.auto.commit, fetch.max.wait.ms, fetch.min.bytes, session.timeout.ms, auto.offset.reset]' were supplied but are not used yet.
2025-08-20 19:57:07.964 [SourceCoordinator-Source: Kafka-单腿订单] WARN  [o.a.k.c.a.AdminClientConfig:385] - These configurations '[key.deserializer, value.deserializer, max.poll.records, client.id.prefix, group.id, partition.discovery.interval.ms, heartbeat.interval.ms, enable.auto.commit, fetch.max.wait.ms, fetch.min.bytes, session.timeout.ms, auto.offset.reset]' were supplied but are not used yet.
2025-08-20 19:57:07.966 [SourceCoordinator-Source: Kafka-单腿订单] WARN  [o.a.k.c.u.AppInfoParser:68] - Error registering AppInfo mbean
javax.management.InstanceAlreadyExistsException: kafka.admin.client:type=app-info,id=orderbook-local-test-group-enumerator-admin-client
	at com.sun.jmx.mbeanserver.Repository.addMBean(Repository.java:436) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerWithRepository(DefaultMBeanServerInterceptor.java:1865) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerDynamicMBean(DefaultMBeanServerInterceptor.java:960) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerObject(DefaultMBeanServerInterceptor.java:895) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerMBean(DefaultMBeanServerInterceptor.java:320) ~[?:?]
	at com.sun.jmx.mbeanserver.JmxMBeanServer.registerMBean(JmxMBeanServer.java:523) ~[?:?]
	at org.apache.kafka.common.utils.AppInfoParser.registerAppInfo(AppInfoParser.java:64) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.admin.KafkaAdminClient.<init>(KafkaAdminClient.java:602) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.admin.KafkaAdminClient.createInternal(KafkaAdminClient.java:544) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.admin.KafkaAdminClient.createInternal(KafkaAdminClient.java:488) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.admin.Admin.create(Admin.java:134) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.admin.AdminClient.create(AdminClient.java:39) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.flink.connector.kafka.source.enumerator.KafkaSourceEnumerator.getKafkaAdminClient(KafkaSourceEnumerator.java:441) ~[flink-connector-kafka-3.4.0-1.20.jar:3.4.0-1.20]
	at org.apache.flink.connector.kafka.source.enumerator.KafkaSourceEnumerator.start(KafkaSourceEnumerator.java:164) ~[flink-connector-kafka-3.4.0-1.20.jar:3.4.0-1.20]
	at org.apache.flink.runtime.source.coordinator.SourceCoordinator.lambda$start$1(SourceCoordinator.java:244) ~[flink-runtime-1.20.0.jar:1.20.0]
	at org.apache.flink.runtime.source.coordinator.SourceCoordinator.lambda$runInEventLoop$10(SourceCoordinator.java:533) ~[flink-runtime-1.20.0.jar:1.20.0]
	at org.apache.flink.util.ThrowableCatchingRunnable.run(ThrowableCatchingRunnable.java:40) [flink-core-1.20.0.jar:1.20.0]
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:539) [?:?]
	at java.util.concurrent.FutureTask.run(FutureTask.java:264) [?:?]
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:304) [?:?]
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136) [?:?]
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635) [?:?]
	at java.lang.Thread.run(Thread.java:840) [?:?]
2025-08-20 19:57:07.968 [SourceCoordinator-Source: Kafka-组合订单] WARN  [o.a.k.c.u.AppInfoParser:68] - Error registering AppInfo mbean
javax.management.InstanceAlreadyExistsException: kafka.admin.client:type=app-info,id=orderbook-local-test-group-enumerator-admin-client
	at com.sun.jmx.mbeanserver.Repository.addMBean(Repository.java:436) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerWithRepository(DefaultMBeanServerInterceptor.java:1865) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerDynamicMBean(DefaultMBeanServerInterceptor.java:960) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerObject(DefaultMBeanServerInterceptor.java:895) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerMBean(DefaultMBeanServerInterceptor.java:320) ~[?:?]
	at com.sun.jmx.mbeanserver.JmxMBeanServer.registerMBean(JmxMBeanServer.java:523) ~[?:?]
	at org.apache.kafka.common.utils.AppInfoParser.registerAppInfo(AppInfoParser.java:64) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.admin.KafkaAdminClient.<init>(KafkaAdminClient.java:602) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.admin.KafkaAdminClient.createInternal(KafkaAdminClient.java:544) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.admin.KafkaAdminClient.createInternal(KafkaAdminClient.java:488) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.admin.Admin.create(Admin.java:134) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.admin.AdminClient.create(AdminClient.java:39) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.flink.connector.kafka.source.enumerator.KafkaSourceEnumerator.getKafkaAdminClient(KafkaSourceEnumerator.java:441) ~[flink-connector-kafka-3.4.0-1.20.jar:3.4.0-1.20]
	at org.apache.flink.connector.kafka.source.enumerator.KafkaSourceEnumerator.start(KafkaSourceEnumerator.java:164) ~[flink-connector-kafka-3.4.0-1.20.jar:3.4.0-1.20]
	at org.apache.flink.runtime.source.coordinator.SourceCoordinator.lambda$start$1(SourceCoordinator.java:244) ~[flink-runtime-1.20.0.jar:1.20.0]
	at org.apache.flink.runtime.source.coordinator.SourceCoordinator.lambda$runInEventLoop$10(SourceCoordinator.java:533) ~[flink-runtime-1.20.0.jar:1.20.0]
	at org.apache.flink.util.ThrowableCatchingRunnable.run(ThrowableCatchingRunnable.java:40) [flink-core-1.20.0.jar:1.20.0]
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:539) [?:?]
	at java.util.concurrent.FutureTask.run(FutureTask.java:264) [?:?]
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:304) [?:?]
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136) [?:?]
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635) [?:?]
	at java.lang.Thread.run(Thread.java:840) [?:?]
2025-08-20 19:57:08.124 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 (1/1)#0] WARN  [o.a.k.c.c.ConsumerConfig:385] - These configurations '[client.id.prefix, partition.discovery.interval.ms]' were supplied but are not used yet.
2025-08-20 19:57:08.124 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 (1/1)#0] WARN  [o.a.k.c.c.ConsumerConfig:385] - These configurations '[client.id.prefix, partition.discovery.interval.ms]' were supplied but are not used yet.
2025-08-20 19:57:08.124 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] WARN  [o.a.k.c.c.ConsumerConfig:385] - These configurations '[client.id.prefix, partition.discovery.interval.ms]' were supplied but are not used yet.
2025-08-20 19:57:08.124 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] WARN  [o.a.k.c.u.AppInfoParser:68] - Error registering AppInfo mbean
javax.management.InstanceAlreadyExistsException: kafka.consumer:type=app-info,id=orderbook-local-test-group-0
	at com.sun.jmx.mbeanserver.Repository.addMBean(Repository.java:436) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerWithRepository(DefaultMBeanServerInterceptor.java:1865) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerDynamicMBean(DefaultMBeanServerInterceptor.java:960) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerObject(DefaultMBeanServerInterceptor.java:895) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerMBean(DefaultMBeanServerInterceptor.java:320) ~[?:?]
	at com.sun.jmx.mbeanserver.JmxMBeanServer.registerMBean(JmxMBeanServer.java:523) ~[?:?]
	at org.apache.kafka.common.utils.AppInfoParser.registerAppInfo(AppInfoParser.java:64) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.consumer.KafkaConsumer.<init>(KafkaConsumer.java:821) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.consumer.KafkaConsumer.<init>(KafkaConsumer.java:665) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.consumer.KafkaConsumer.<init>(KafkaConsumer.java:646) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.consumer.KafkaConsumer.<init>(KafkaConsumer.java:626) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.flink.connector.kafka.source.reader.KafkaPartitionSplitReader.<init>(KafkaPartitionSplitReader.java:97) ~[flink-connector-kafka-3.4.0-1.20.jar:3.4.0-1.20]
	at org.apache.flink.connector.kafka.source.KafkaSource.lambda$createReader$1(KafkaSource.java:185) ~[flink-connector-kafka-3.4.0-1.20.jar:3.4.0-1.20]
	at org.apache.flink.connector.base.source.reader.fetcher.SplitFetcherManager.createSplitFetcher(SplitFetcherManager.java:259) ~[flink-connector-base-1.20.0.jar:1.20.0]
	at org.apache.flink.connector.base.source.reader.fetcher.SingleThreadFetcherManager.addSplits(SingleThreadFetcherManager.java:148) ~[flink-connector-base-1.20.0.jar:1.20.0]
	at org.apache.flink.connector.base.source.reader.SourceReaderBase.addSplits(SourceReaderBase.java:315) ~[flink-connector-base-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.api.operators.SourceOperator.handleAddSplitsEvent(SourceOperator.java:606) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.api.operators.SourceOperator.handleOperatorEvent(SourceOperator.java:575) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.OperatorEventDispatcherImpl.dispatchEventToHandlers(OperatorEventDispatcherImpl.java:72) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.RegularOperatorChain.dispatchOperatorEvent(RegularOperatorChain.java:80) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.StreamTask.lambda$dispatchOperatorEvent$24(StreamTask.java:1609) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.StreamTaskActionExecutor$1.runThrowing(StreamTaskActionExecutor.java:50) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.mailbox.Mail.run(Mail.java:101) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.mailbox.MailboxProcessor.runMail(MailboxProcessor.java:414) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.mailbox.MailboxProcessor.processMailsWhenDefaultActionUnavailable(MailboxProcessor.java:383) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.mailbox.MailboxProcessor.processMail(MailboxProcessor.java:368) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.mailbox.MailboxProcessor.runMailboxLoop(MailboxProcessor.java:229) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.StreamTask.runMailboxLoop(StreamTask.java:973) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.StreamTask.invoke(StreamTask.java:917) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.runtime.taskmanager.Task.runWithSystemExitMonitoring(Task.java:970) [flink-runtime-1.20.0.jar:1.20.0]
	at org.apache.flink.runtime.taskmanager.Task.restoreAndInvoke(Task.java:949) [flink-runtime-1.20.0.jar:1.20.0]
	at org.apache.flink.runtime.taskmanager.Task.doRun(Task.java:763) [flink-runtime-1.20.0.jar:1.20.0]
	at org.apache.flink.runtime.taskmanager.Task.run(Task.java:575) [flink-runtime-1.20.0.jar:1.20.0]
	at java.lang.Thread.run(Thread.java:840) [?:?]
2025-08-20 19:57:08.125 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 (1/1)#0] WARN  [o.a.k.c.u.AppInfoParser:68] - Error registering AppInfo mbean
javax.management.InstanceAlreadyExistsException: kafka.consumer:type=app-info,id=orderbook-local-test-group-0
	at com.sun.jmx.mbeanserver.Repository.addMBean(Repository.java:436) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerWithRepository(DefaultMBeanServerInterceptor.java:1865) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerDynamicMBean(DefaultMBeanServerInterceptor.java:960) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerObject(DefaultMBeanServerInterceptor.java:895) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerMBean(DefaultMBeanServerInterceptor.java:320) ~[?:?]
	at com.sun.jmx.mbeanserver.JmxMBeanServer.registerMBean(JmxMBeanServer.java:523) ~[?:?]
	at org.apache.kafka.common.utils.AppInfoParser.registerAppInfo(AppInfoParser.java:64) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.consumer.KafkaConsumer.<init>(KafkaConsumer.java:821) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.consumer.KafkaConsumer.<init>(KafkaConsumer.java:665) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.consumer.KafkaConsumer.<init>(KafkaConsumer.java:646) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.consumer.KafkaConsumer.<init>(KafkaConsumer.java:626) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.flink.connector.kafka.source.reader.KafkaPartitionSplitReader.<init>(KafkaPartitionSplitReader.java:97) ~[flink-connector-kafka-3.4.0-1.20.jar:3.4.0-1.20]
	at org.apache.flink.connector.kafka.source.KafkaSource.lambda$createReader$1(KafkaSource.java:185) ~[flink-connector-kafka-3.4.0-1.20.jar:3.4.0-1.20]
	at org.apache.flink.connector.base.source.reader.fetcher.SplitFetcherManager.createSplitFetcher(SplitFetcherManager.java:259) ~[flink-connector-base-1.20.0.jar:1.20.0]
	at org.apache.flink.connector.base.source.reader.fetcher.SingleThreadFetcherManager.addSplits(SingleThreadFetcherManager.java:148) ~[flink-connector-base-1.20.0.jar:1.20.0]
	at org.apache.flink.connector.base.source.reader.SourceReaderBase.addSplits(SourceReaderBase.java:315) ~[flink-connector-base-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.api.operators.SourceOperator.handleAddSplitsEvent(SourceOperator.java:606) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.api.operators.SourceOperator.handleOperatorEvent(SourceOperator.java:575) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.OperatorEventDispatcherImpl.dispatchEventToHandlers(OperatorEventDispatcherImpl.java:72) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.RegularOperatorChain.dispatchOperatorEvent(RegularOperatorChain.java:80) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.StreamTask.lambda$dispatchOperatorEvent$24(StreamTask.java:1609) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.StreamTaskActionExecutor$1.runThrowing(StreamTaskActionExecutor.java:50) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.mailbox.Mail.run(Mail.java:101) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.mailbox.MailboxProcessor.runMail(MailboxProcessor.java:414) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.mailbox.MailboxProcessor.processMailsWhenDefaultActionUnavailable(MailboxProcessor.java:383) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.mailbox.MailboxProcessor.processMail(MailboxProcessor.java:368) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.mailbox.MailboxProcessor.runMailboxLoop(MailboxProcessor.java:229) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.StreamTask.runMailboxLoop(StreamTask.java:973) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.StreamTask.invoke(StreamTask.java:917) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.runtime.taskmanager.Task.runWithSystemExitMonitoring(Task.java:970) [flink-runtime-1.20.0.jar:1.20.0]
	at org.apache.flink.runtime.taskmanager.Task.restoreAndInvoke(Task.java:949) [flink-runtime-1.20.0.jar:1.20.0]
	at org.apache.flink.runtime.taskmanager.Task.doRun(Task.java:763) [flink-runtime-1.20.0.jar:1.20.0]
	at org.apache.flink.runtime.taskmanager.Task.run(Task.java:575) [flink-runtime-1.20.0.jar:1.20.0]
	at java.lang.Thread.run(Thread.java:840) [?:?]
2025-08-20 19:57:08.257 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] WARN  [c.f.f.PnLCalculationFunction:180] - 发现无持仓的平仓交易 - 发送到异常流: 交易号=101355625, 合约=JD2504-C-3200, 方向=S, 数量=5
2025-08-20 19:57:08.257 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] WARN  [c.f.f.PnLCalculationFunction:180] - 发现无持仓的平仓交易 - 发送到异常流: 交易号=101355626, 合约=JD2504-C-3200, 方向=S, 数量=5
2025-08-20 19:57:08.258 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] WARN  [c.f.f.PnLCalculationFunction:180] - 发现无持仓的平仓交易 - 发送到异常流: 交易号=101355700, 合约=EB2505, 方向=B, 数量=10
2025-08-20 19:57:08.258 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] WARN  [c.f.f.PnLCalculationFunction:180] - 发现无持仓的平仓交易 - 发送到异常流: 交易号=48060, 合约=SH2505, 方向=S, 数量=1
2025-08-20 19:57:08.258 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] WARN  [c.f.f.PnLCalculationFunction:180] - 发现无持仓的平仓交易 - 发送到异常流: 交易号=100052597, 合约=EB2505, 方向=B, 数量=8
2025-08-20 19:57:08.259 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] WARN  [c.f.f.PnLCalculationFunction:180] - 发现无持仓的平仓交易 - 发送到异常流: 交易号=100052597, 合约=EB2505, 方向=S, 数量=8
2025-08-20 19:57:08.259 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] WARN  [c.f.f.PnLCalculationFunction:180] - 发现无持仓的平仓交易 - 发送到异常流: 交易号=100052678, 合约=EB2505, 方向=B, 数量=1
2025-08-20 19:57:08.259 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] WARN  [c.f.f.PnLCalculationFunction:180] - 发现无持仓的平仓交易 - 发送到异常流: 交易号=100052678, 合约=EB2505, 方向=S, 数量=1
2025-08-20 19:57:08.259 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] WARN  [c.f.f.PnLCalculationFunction:180] - 发现无持仓的平仓交易 - 发送到异常流: 交易号=100052768, 合约=EB2505, 方向=B, 数量=1
2025-08-20 19:57:08.262 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] WARN  [c.f.f.PnLCalculationFunction:180] - 发现无持仓的平仓交易 - 发送到异常流: 交易号=100052770, 合约=EB2505, 方向=B, 数量=1
2025-08-20 19:57:08.263 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] WARN  [c.f.f.PnLCalculationFunction:180] - 发现无持仓的平仓交易 - 发送到异常流: 交易号=100052795, 合约=EB2505, 方向=B, 数量=1
2025-08-20 19:57:08.265 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] WARN  [c.f.f.PnLCalculationFunction:180] - 发现无持仓的平仓交易 - 发送到异常流: 交易号=100052821, 合约=EB2505, 方向=B, 数量=1
2025-08-20 19:57:08.266 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] WARN  [c.f.f.PnLCalculationFunction:180] - 发现无持仓的平仓交易 - 发送到异常流: 交易号=100052822, 合约=EB2505, 方向=B, 数量=2
2025-08-20 19:57:08.267 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] WARN  [c.f.f.PnLCalculationFunction:180] - 发现无持仓的平仓交易 - 发送到异常流: 交易号=100052833, 合约=EB2505, 方向=B, 数量=2
2025-08-20 19:57:08.268 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] WARN  [c.f.f.PnLCalculationFunction:180] - 发现无持仓的平仓交易 - 发送到异常流: 交易号=100052846, 合约=EB2504, 方向=B, 数量=1
2025-08-20 19:57:08.268 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] WARN  [c.f.f.PnLCalculationFunction:180] - 发现无持仓的平仓交易 - 发送到异常流: 交易号=100052847, 合约=EB2505, 方向=B, 数量=1
2025-08-20 19:57:08.269 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] WARN  [c.f.f.PnLCalculationFunction:180] - 发现无持仓的平仓交易 - 发送到异常流: 交易号=100052849, 合约=EB2504, 方向=S, 数量=10
2025-08-20 19:58:04.894 [main] WARN  [o.a.f.c.k.s.KafkaSourceBuilder:497] - Property auto.offset.reset is provided but will be overridden from earliest to earliest
2025-08-20 19:58:04.911 [main] WARN  [o.a.f.c.k.s.KafkaSourceBuilder:497] - Property auto.offset.reset is provided but will be overridden from earliest to earliest
2025-08-20 19:58:04.912 [main] WARN  [o.a.f.c.k.s.KafkaSourceBuilder:497] - Property auto.offset.reset is provided but will be overridden from earliest to earliest
2025-08-20 19:58:05.370 [main] WARN  [o.a.f.r.s.t.DefaultDelegationTokenManager:248] - No tokens obtained so skipping notifications
2025-08-20 19:58:05.475 [main] WARN  [o.a.f.r.w.WebMonitorUtils:76] - Log file environment variable 'log.file' is not set.
2025-08-20 19:58:05.475 [main] WARN  [o.a.f.r.w.WebMonitorUtils:82] - JobManager log files are unavailable in the web dashboard. Log file location not found in environment variable 'log.file' or configuration key 'web.log.path'.
2025-08-20 19:58:05.532 [flink-pekko.actor.default-dispatcher-4] WARN  [o.a.f.r.s.t.DefaultDelegationTokenManager:320] - No tokens obtained so skipping notifications
2025-08-20 19:58:05.533 [flink-pekko.actor.default-dispatcher-4] WARN  [o.a.f.r.s.t.DefaultDelegationTokenManager:335] - Tokens update task not started because either no tokens obtained or none of the tokens specified its renewal date
2025-08-20 19:58:05.638 [SourceCoordinator-Source: Kafka-交易] WARN  [o.a.k.c.a.AdminClientConfig:385] - These configurations '[key.deserializer, value.deserializer, max.poll.records, client.id.prefix, group.id, partition.discovery.interval.ms, heartbeat.interval.ms, enable.auto.commit, fetch.max.wait.ms, fetch.min.bytes, session.timeout.ms, auto.offset.reset]' were supplied but are not used yet.
2025-08-20 19:58:05.638 [SourceCoordinator-Source: Kafka-组合订单] WARN  [o.a.k.c.a.AdminClientConfig:385] - These configurations '[key.deserializer, value.deserializer, max.poll.records, client.id.prefix, group.id, partition.discovery.interval.ms, heartbeat.interval.ms, enable.auto.commit, fetch.max.wait.ms, fetch.min.bytes, session.timeout.ms, auto.offset.reset]' were supplied but are not used yet.
2025-08-20 19:58:05.638 [SourceCoordinator-Source: Kafka-单腿订单] WARN  [o.a.k.c.a.AdminClientConfig:385] - These configurations '[key.deserializer, value.deserializer, max.poll.records, client.id.prefix, group.id, partition.discovery.interval.ms, heartbeat.interval.ms, enable.auto.commit, fetch.max.wait.ms, fetch.min.bytes, session.timeout.ms, auto.offset.reset]' were supplied but are not used yet.
2025-08-20 19:58:05.639 [SourceCoordinator-Source: Kafka-单腿订单] WARN  [o.a.k.c.u.AppInfoParser:68] - Error registering AppInfo mbean
javax.management.InstanceAlreadyExistsException: kafka.admin.client:type=app-info,id=orderbook-local-test-group-enumerator-admin-client
	at com.sun.jmx.mbeanserver.Repository.addMBean(Repository.java:436) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerWithRepository(DefaultMBeanServerInterceptor.java:1865) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerDynamicMBean(DefaultMBeanServerInterceptor.java:960) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerObject(DefaultMBeanServerInterceptor.java:895) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerMBean(DefaultMBeanServerInterceptor.java:320) ~[?:?]
	at com.sun.jmx.mbeanserver.JmxMBeanServer.registerMBean(JmxMBeanServer.java:523) ~[?:?]
	at org.apache.kafka.common.utils.AppInfoParser.registerAppInfo(AppInfoParser.java:64) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.admin.KafkaAdminClient.<init>(KafkaAdminClient.java:602) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.admin.KafkaAdminClient.createInternal(KafkaAdminClient.java:544) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.admin.KafkaAdminClient.createInternal(KafkaAdminClient.java:488) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.admin.Admin.create(Admin.java:134) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.admin.AdminClient.create(AdminClient.java:39) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.flink.connector.kafka.source.enumerator.KafkaSourceEnumerator.getKafkaAdminClient(KafkaSourceEnumerator.java:441) ~[flink-connector-kafka-3.4.0-1.20.jar:3.4.0-1.20]
	at org.apache.flink.connector.kafka.source.enumerator.KafkaSourceEnumerator.start(KafkaSourceEnumerator.java:164) ~[flink-connector-kafka-3.4.0-1.20.jar:3.4.0-1.20]
	at org.apache.flink.runtime.source.coordinator.SourceCoordinator.lambda$start$1(SourceCoordinator.java:244) ~[flink-runtime-1.20.0.jar:1.20.0]
	at org.apache.flink.runtime.source.coordinator.SourceCoordinator.lambda$runInEventLoop$10(SourceCoordinator.java:533) ~[flink-runtime-1.20.0.jar:1.20.0]
	at org.apache.flink.util.ThrowableCatchingRunnable.run(ThrowableCatchingRunnable.java:40) [flink-core-1.20.0.jar:1.20.0]
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:539) [?:?]
	at java.util.concurrent.FutureTask.run(FutureTask.java:264) [?:?]
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:304) [?:?]
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136) [?:?]
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635) [?:?]
	at java.lang.Thread.run(Thread.java:840) [?:?]
2025-08-20 19:58:05.643 [SourceCoordinator-Source: Kafka-组合订单] WARN  [o.a.k.c.u.AppInfoParser:68] - Error registering AppInfo mbean
javax.management.InstanceAlreadyExistsException: kafka.admin.client:type=app-info,id=orderbook-local-test-group-enumerator-admin-client
	at com.sun.jmx.mbeanserver.Repository.addMBean(Repository.java:436) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerWithRepository(DefaultMBeanServerInterceptor.java:1865) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerDynamicMBean(DefaultMBeanServerInterceptor.java:960) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerObject(DefaultMBeanServerInterceptor.java:895) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerMBean(DefaultMBeanServerInterceptor.java:320) ~[?:?]
	at com.sun.jmx.mbeanserver.JmxMBeanServer.registerMBean(JmxMBeanServer.java:523) ~[?:?]
	at org.apache.kafka.common.utils.AppInfoParser.registerAppInfo(AppInfoParser.java:64) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.admin.KafkaAdminClient.<init>(KafkaAdminClient.java:602) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.admin.KafkaAdminClient.createInternal(KafkaAdminClient.java:544) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.admin.KafkaAdminClient.createInternal(KafkaAdminClient.java:488) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.admin.Admin.create(Admin.java:134) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.admin.AdminClient.create(AdminClient.java:39) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.flink.connector.kafka.source.enumerator.KafkaSourceEnumerator.getKafkaAdminClient(KafkaSourceEnumerator.java:441) ~[flink-connector-kafka-3.4.0-1.20.jar:3.4.0-1.20]
	at org.apache.flink.connector.kafka.source.enumerator.KafkaSourceEnumerator.start(KafkaSourceEnumerator.java:164) ~[flink-connector-kafka-3.4.0-1.20.jar:3.4.0-1.20]
	at org.apache.flink.runtime.source.coordinator.SourceCoordinator.lambda$start$1(SourceCoordinator.java:244) ~[flink-runtime-1.20.0.jar:1.20.0]
	at org.apache.flink.runtime.source.coordinator.SourceCoordinator.lambda$runInEventLoop$10(SourceCoordinator.java:533) ~[flink-runtime-1.20.0.jar:1.20.0]
	at org.apache.flink.util.ThrowableCatchingRunnable.run(ThrowableCatchingRunnable.java:40) [flink-core-1.20.0.jar:1.20.0]
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:539) [?:?]
	at java.util.concurrent.FutureTask.run(FutureTask.java:264) [?:?]
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:304) [?:?]
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136) [?:?]
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635) [?:?]
	at java.lang.Thread.run(Thread.java:840) [?:?]
2025-08-20 19:58:05.786 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 (1/1)#0] WARN  [o.a.k.c.c.ConsumerConfig:385] - These configurations '[client.id.prefix, partition.discovery.interval.ms]' were supplied but are not used yet.
2025-08-20 19:58:05.786 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] WARN  [o.a.k.c.c.ConsumerConfig:385] - These configurations '[client.id.prefix, partition.discovery.interval.ms]' were supplied but are not used yet.
2025-08-20 19:58:05.786 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 (1/1)#0] WARN  [o.a.k.c.c.ConsumerConfig:385] - These configurations '[client.id.prefix, partition.discovery.interval.ms]' were supplied but are not used yet.
2025-08-20 19:58:05.787 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 (1/1)#0] WARN  [o.a.k.c.u.AppInfoParser:68] - Error registering AppInfo mbean
javax.management.InstanceAlreadyExistsException: kafka.consumer:type=app-info,id=orderbook-local-test-group-0
	at com.sun.jmx.mbeanserver.Repository.addMBean(Repository.java:436) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerWithRepository(DefaultMBeanServerInterceptor.java:1865) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerDynamicMBean(DefaultMBeanServerInterceptor.java:960) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerObject(DefaultMBeanServerInterceptor.java:895) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerMBean(DefaultMBeanServerInterceptor.java:320) ~[?:?]
	at com.sun.jmx.mbeanserver.JmxMBeanServer.registerMBean(JmxMBeanServer.java:523) ~[?:?]
	at org.apache.kafka.common.utils.AppInfoParser.registerAppInfo(AppInfoParser.java:64) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.consumer.KafkaConsumer.<init>(KafkaConsumer.java:821) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.consumer.KafkaConsumer.<init>(KafkaConsumer.java:665) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.consumer.KafkaConsumer.<init>(KafkaConsumer.java:646) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.consumer.KafkaConsumer.<init>(KafkaConsumer.java:626) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.flink.connector.kafka.source.reader.KafkaPartitionSplitReader.<init>(KafkaPartitionSplitReader.java:97) ~[flink-connector-kafka-3.4.0-1.20.jar:3.4.0-1.20]
	at org.apache.flink.connector.kafka.source.KafkaSource.lambda$createReader$1(KafkaSource.java:185) ~[flink-connector-kafka-3.4.0-1.20.jar:3.4.0-1.20]
	at org.apache.flink.connector.base.source.reader.fetcher.SplitFetcherManager.createSplitFetcher(SplitFetcherManager.java:259) ~[flink-connector-base-1.20.0.jar:1.20.0]
	at org.apache.flink.connector.base.source.reader.fetcher.SingleThreadFetcherManager.addSplits(SingleThreadFetcherManager.java:148) ~[flink-connector-base-1.20.0.jar:1.20.0]
	at org.apache.flink.connector.base.source.reader.SourceReaderBase.addSplits(SourceReaderBase.java:315) ~[flink-connector-base-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.api.operators.SourceOperator.handleAddSplitsEvent(SourceOperator.java:606) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.api.operators.SourceOperator.handleOperatorEvent(SourceOperator.java:575) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.OperatorEventDispatcherImpl.dispatchEventToHandlers(OperatorEventDispatcherImpl.java:72) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.RegularOperatorChain.dispatchOperatorEvent(RegularOperatorChain.java:80) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.StreamTask.lambda$dispatchOperatorEvent$24(StreamTask.java:1609) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.StreamTaskActionExecutor$1.runThrowing(StreamTaskActionExecutor.java:50) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.mailbox.Mail.run(Mail.java:101) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.mailbox.MailboxProcessor.runMail(MailboxProcessor.java:414) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.mailbox.MailboxProcessor.processMailsWhenDefaultActionUnavailable(MailboxProcessor.java:383) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.mailbox.MailboxProcessor.processMail(MailboxProcessor.java:368) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.mailbox.MailboxProcessor.runMailboxLoop(MailboxProcessor.java:229) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.StreamTask.runMailboxLoop(StreamTask.java:973) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.StreamTask.invoke(StreamTask.java:917) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.runtime.taskmanager.Task.runWithSystemExitMonitoring(Task.java:970) [flink-runtime-1.20.0.jar:1.20.0]
	at org.apache.flink.runtime.taskmanager.Task.restoreAndInvoke(Task.java:949) [flink-runtime-1.20.0.jar:1.20.0]
	at org.apache.flink.runtime.taskmanager.Task.doRun(Task.java:763) [flink-runtime-1.20.0.jar:1.20.0]
	at org.apache.flink.runtime.taskmanager.Task.run(Task.java:575) [flink-runtime-1.20.0.jar:1.20.0]
	at java.lang.Thread.run(Thread.java:840) [?:?]
2025-08-20 19:58:05.787 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] WARN  [o.a.k.c.u.AppInfoParser:68] - Error registering AppInfo mbean
javax.management.InstanceAlreadyExistsException: kafka.consumer:type=app-info,id=orderbook-local-test-group-0
	at com.sun.jmx.mbeanserver.Repository.addMBean(Repository.java:436) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerWithRepository(DefaultMBeanServerInterceptor.java:1865) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerDynamicMBean(DefaultMBeanServerInterceptor.java:960) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerObject(DefaultMBeanServerInterceptor.java:895) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerMBean(DefaultMBeanServerInterceptor.java:320) ~[?:?]
	at com.sun.jmx.mbeanserver.JmxMBeanServer.registerMBean(JmxMBeanServer.java:523) ~[?:?]
	at org.apache.kafka.common.utils.AppInfoParser.registerAppInfo(AppInfoParser.java:64) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.consumer.KafkaConsumer.<init>(KafkaConsumer.java:821) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.consumer.KafkaConsumer.<init>(KafkaConsumer.java:665) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.consumer.KafkaConsumer.<init>(KafkaConsumer.java:646) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.consumer.KafkaConsumer.<init>(KafkaConsumer.java:626) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.flink.connector.kafka.source.reader.KafkaPartitionSplitReader.<init>(KafkaPartitionSplitReader.java:97) ~[flink-connector-kafka-3.4.0-1.20.jar:3.4.0-1.20]
	at org.apache.flink.connector.kafka.source.KafkaSource.lambda$createReader$1(KafkaSource.java:185) ~[flink-connector-kafka-3.4.0-1.20.jar:3.4.0-1.20]
	at org.apache.flink.connector.base.source.reader.fetcher.SplitFetcherManager.createSplitFetcher(SplitFetcherManager.java:259) ~[flink-connector-base-1.20.0.jar:1.20.0]
	at org.apache.flink.connector.base.source.reader.fetcher.SingleThreadFetcherManager.addSplits(SingleThreadFetcherManager.java:148) ~[flink-connector-base-1.20.0.jar:1.20.0]
	at org.apache.flink.connector.base.source.reader.SourceReaderBase.addSplits(SourceReaderBase.java:315) ~[flink-connector-base-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.api.operators.SourceOperator.handleAddSplitsEvent(SourceOperator.java:606) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.api.operators.SourceOperator.handleOperatorEvent(SourceOperator.java:575) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.OperatorEventDispatcherImpl.dispatchEventToHandlers(OperatorEventDispatcherImpl.java:72) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.RegularOperatorChain.dispatchOperatorEvent(RegularOperatorChain.java:80) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.StreamTask.lambda$dispatchOperatorEvent$24(StreamTask.java:1609) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.StreamTaskActionExecutor$1.runThrowing(StreamTaskActionExecutor.java:50) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.mailbox.Mail.run(Mail.java:101) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.mailbox.MailboxProcessor.runMail(MailboxProcessor.java:414) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.mailbox.MailboxProcessor.processMailsWhenDefaultActionUnavailable(MailboxProcessor.java:383) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.mailbox.MailboxProcessor.processMail(MailboxProcessor.java:368) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.mailbox.MailboxProcessor.runMailboxLoop(MailboxProcessor.java:229) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.StreamTask.runMailboxLoop(StreamTask.java:973) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.StreamTask.invoke(StreamTask.java:917) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.runtime.taskmanager.Task.runWithSystemExitMonitoring(Task.java:970) [flink-runtime-1.20.0.jar:1.20.0]
	at org.apache.flink.runtime.taskmanager.Task.restoreAndInvoke(Task.java:949) [flink-runtime-1.20.0.jar:1.20.0]
	at org.apache.flink.runtime.taskmanager.Task.doRun(Task.java:763) [flink-runtime-1.20.0.jar:1.20.0]
	at org.apache.flink.runtime.taskmanager.Task.run(Task.java:575) [flink-runtime-1.20.0.jar:1.20.0]
	at java.lang.Thread.run(Thread.java:840) [?:?]
2025-08-20 19:58:05.906 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] WARN  [c.f.f.PnLCalculationFunction:180] - 发现无持仓的平仓交易 - 发送到异常流: 交易号=101355625, 合约=JD2504-C-3200, 方向=S, 数量=5
2025-08-20 19:58:05.906 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] WARN  [c.f.f.PnLCalculationFunction:180] - 发现无持仓的平仓交易 - 发送到异常流: 交易号=101355626, 合约=JD2504-C-3200, 方向=S, 数量=5
2025-08-20 19:58:05.907 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] WARN  [c.f.f.PnLCalculationFunction:180] - 发现无持仓的平仓交易 - 发送到异常流: 交易号=101355700, 合约=EB2505, 方向=B, 数量=10
2025-08-20 19:58:05.907 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] WARN  [c.f.f.PnLCalculationFunction:180] - 发现无持仓的平仓交易 - 发送到异常流: 交易号=48060, 合约=SH2505, 方向=S, 数量=1
2025-08-20 19:58:05.907 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] WARN  [c.f.f.PnLCalculationFunction:180] - 发现无持仓的平仓交易 - 发送到异常流: 交易号=100052597, 合约=EB2505, 方向=B, 数量=8
2025-08-20 19:58:05.907 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] WARN  [c.f.f.PnLCalculationFunction:180] - 发现无持仓的平仓交易 - 发送到异常流: 交易号=100052597, 合约=EB2505, 方向=S, 数量=8
2025-08-20 19:58:05.907 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] WARN  [c.f.f.PnLCalculationFunction:180] - 发现无持仓的平仓交易 - 发送到异常流: 交易号=100052678, 合约=EB2505, 方向=B, 数量=1
2025-08-20 19:58:05.908 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] WARN  [c.f.f.PnLCalculationFunction:180] - 发现无持仓的平仓交易 - 发送到异常流: 交易号=100052678, 合约=EB2505, 方向=S, 数量=1
2025-08-20 19:58:05.908 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] WARN  [c.f.f.PnLCalculationFunction:180] - 发现无持仓的平仓交易 - 发送到异常流: 交易号=100052768, 合约=EB2505, 方向=B, 数量=1
2025-08-20 19:58:05.914 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] WARN  [c.f.f.PnLCalculationFunction:180] - 发现无持仓的平仓交易 - 发送到异常流: 交易号=100052770, 合约=EB2505, 方向=B, 数量=1
2025-08-20 19:58:05.914 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] WARN  [c.f.f.PnLCalculationFunction:180] - 发现无持仓的平仓交易 - 发送到异常流: 交易号=100052795, 合约=EB2505, 方向=B, 数量=1
2025-08-20 19:58:05.916 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] WARN  [c.f.f.PnLCalculationFunction:180] - 发现无持仓的平仓交易 - 发送到异常流: 交易号=100052821, 合约=EB2505, 方向=B, 数量=1
2025-08-20 19:58:05.917 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] WARN  [c.f.f.PnLCalculationFunction:180] - 发现无持仓的平仓交易 - 发送到异常流: 交易号=100052822, 合约=EB2505, 方向=B, 数量=2
2025-08-20 19:58:05.917 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] WARN  [c.f.f.PnLCalculationFunction:180] - 发现无持仓的平仓交易 - 发送到异常流: 交易号=100052833, 合约=EB2505, 方向=B, 数量=2
2025-08-20 19:58:05.918 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] WARN  [c.f.f.PnLCalculationFunction:180] - 发现无持仓的平仓交易 - 发送到异常流: 交易号=100052846, 合约=EB2504, 方向=B, 数量=1
2025-08-20 19:58:05.918 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] WARN  [c.f.f.PnLCalculationFunction:180] - 发现无持仓的平仓交易 - 发送到异常流: 交易号=100052847, 合约=EB2505, 方向=B, 数量=1
2025-08-20 19:58:05.919 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] WARN  [c.f.f.PnLCalculationFunction:180] - 发现无持仓的平仓交易 - 发送到异常流: 交易号=100052849, 合约=EB2504, 方向=S, 数量=10
2025-08-20 19:58:16.489 [com.futures.job.FuturesOrderBookKafkaJob.main()] WARN  [o.a.f.c.k.s.KafkaSourceBuilder:497] - Property auto.offset.reset is provided but will be overridden from earliest to earliest
2025-08-20 19:58:16.511 [com.futures.job.FuturesOrderBookKafkaJob.main()] WARN  [o.a.f.c.k.s.KafkaSourceBuilder:497] - Property auto.offset.reset is provided but will be overridden from earliest to earliest
2025-08-20 19:58:16.512 [com.futures.job.FuturesOrderBookKafkaJob.main()] WARN  [o.a.f.c.k.s.KafkaSourceBuilder:497] - Property auto.offset.reset is provided but will be overridden from earliest to earliest
2025-08-20 19:58:16.950 [com.futures.job.FuturesOrderBookKafkaJob.main()] WARN  [o.a.f.r.s.t.DefaultDelegationTokenManager:248] - No tokens obtained so skipping notifications
2025-08-20 19:58:17.067 [com.futures.job.FuturesOrderBookKafkaJob.main()] WARN  [o.a.f.r.w.WebMonitorUtils:76] - Log file environment variable 'log.file' is not set.
2025-08-20 19:58:17.067 [com.futures.job.FuturesOrderBookKafkaJob.main()] WARN  [o.a.f.r.w.WebMonitorUtils:82] - JobManager log files are unavailable in the web dashboard. Log file location not found in environment variable 'log.file' or configuration key 'web.log.path'.
2025-08-20 19:58:17.129 [flink-pekko.actor.default-dispatcher-5] WARN  [o.a.f.r.s.t.DefaultDelegationTokenManager:320] - No tokens obtained so skipping notifications
2025-08-20 19:58:17.129 [flink-pekko.actor.default-dispatcher-5] WARN  [o.a.f.r.s.t.DefaultDelegationTokenManager:335] - Tokens update task not started because either no tokens obtained or none of the tokens specified its renewal date
2025-08-20 19:58:36.925 [com.futures.job.FuturesOrderBookKafkaJob.main()] WARN  [o.a.f.c.k.s.KafkaSourceBuilder:497] - Property auto.offset.reset is provided but will be overridden from earliest to earliest
2025-08-20 19:58:36.945 [com.futures.job.FuturesOrderBookKafkaJob.main()] WARN  [o.a.f.c.k.s.KafkaSourceBuilder:497] - Property auto.offset.reset is provided but will be overridden from earliest to earliest
2025-08-20 19:58:36.946 [com.futures.job.FuturesOrderBookKafkaJob.main()] WARN  [o.a.f.c.k.s.KafkaSourceBuilder:497] - Property auto.offset.reset is provided but will be overridden from earliest to earliest
2025-08-20 19:58:37.371 [com.futures.job.FuturesOrderBookKafkaJob.main()] WARN  [o.a.f.r.s.t.DefaultDelegationTokenManager:248] - No tokens obtained so skipping notifications
2025-08-20 19:58:37.477 [com.futures.job.FuturesOrderBookKafkaJob.main()] WARN  [o.a.f.r.w.WebMonitorUtils:76] - Log file environment variable 'log.file' is not set.
2025-08-20 19:58:37.478 [com.futures.job.FuturesOrderBookKafkaJob.main()] WARN  [o.a.f.r.w.WebMonitorUtils:82] - JobManager log files are unavailable in the web dashboard. Log file location not found in environment variable 'log.file' or configuration key 'web.log.path'.
2025-08-20 19:58:37.532 [flink-pekko.actor.default-dispatcher-6] WARN  [o.a.f.r.s.t.DefaultDelegationTokenManager:320] - No tokens obtained so skipping notifications
2025-08-20 19:58:37.533 [flink-pekko.actor.default-dispatcher-6] WARN  [o.a.f.r.s.t.DefaultDelegationTokenManager:335] - Tokens update task not started because either no tokens obtained or none of the tokens specified its renewal date
2025-08-20 19:58:51.137 [main] WARN  [o.a.f.r.s.t.DefaultDelegationTokenManager:248] - No tokens obtained so skipping notifications
2025-08-20 19:58:51.293 [main] WARN  [o.a.f.r.w.WebMonitorUtils:76] - Log file environment variable 'log.file' is not set.
2025-08-20 19:58:51.293 [main] WARN  [o.a.f.r.w.WebMonitorUtils:82] - JobManager log files are unavailable in the web dashboard. Log file location not found in environment variable 'log.file' or configuration key 'web.log.path'.
2025-08-20 19:58:51.351 [flink-pekko.actor.default-dispatcher-4] WARN  [o.a.f.r.s.t.DefaultDelegationTokenManager:320] - No tokens obtained so skipping notifications
2025-08-20 19:58:51.351 [flink-pekko.actor.default-dispatcher-4] WARN  [o.a.f.r.s.t.DefaultDelegationTokenManager:335] - Tokens update task not started because either no tokens obtained or none of the tokens specified its renewal date
2025-08-20 19:59:07.351 [main] WARN  [o.a.f.r.s.t.DefaultDelegationTokenManager:248] - No tokens obtained so skipping notifications
2025-08-20 19:59:07.511 [main] WARN  [o.a.f.r.w.WebMonitorUtils:76] - Log file environment variable 'log.file' is not set.
2025-08-20 19:59:07.511 [main] WARN  [o.a.f.r.w.WebMonitorUtils:82] - JobManager log files are unavailable in the web dashboard. Log file location not found in environment variable 'log.file' or configuration key 'web.log.path'.
2025-08-20 19:59:07.570 [flink-pekko.actor.default-dispatcher-4] WARN  [o.a.f.r.s.t.DefaultDelegationTokenManager:320] - No tokens obtained so skipping notifications
2025-08-20 19:59:07.570 [flink-pekko.actor.default-dispatcher-4] WARN  [o.a.f.r.s.t.DefaultDelegationTokenManager:335] - Tokens update task not started because either no tokens obtained or none of the tokens specified its renewal date
