package com.futures.demo;

import com.futures.function.BaseOrderBookBuilder;
import com.futures.function.VirtualOrderBookBuilder;
import com.futures.function.LayeredGlobalSnapshotAggregator;
import com.futures.pojo.*;
import org.apache.flink.api.common.eventtime.WatermarkStrategy;
import org.apache.flink.streaming.api.datastream.DataStream;
import org.apache.flink.streaming.api.datastream.SingleOutputStreamOperator;
import org.apache.flink.streaming.api.environment.StreamExecutionEnvironment;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.time.Duration;
import java.util.Arrays;
import java.util.List;

/**
 * 分层订单簿调试演示程序
 * 使用模拟数据测试分层架构和0.5秒输出功能
 */
public class LayeredOrderBookDebugDemo {
    private static final Logger logger = LoggerFactory.getLogger(LayeredOrderBookDebugDemo.class);
    
    public static void main(String[] args) throws Exception {
        logger.info("启动分层订单簿调试演示程序...");
        
        StreamExecutionEnvironment env = StreamExecutionEnvironment.getExecutionEnvironment();
        env.setParallelism(1);
        
        // 创建测试数据 - 使用当前时间确保水位线能够推进
        List<SingleLegOrderEvent> singleLegOrders = createTestSingleLegOrders();
        List<CombinationOrderEvent> comboOrders = createTestCombinationOrders();
        
        // 1. 基础层处理：单腿订单 -> 基础订单簿 + BBO流
        DataStream<SingleLegOrderEvent> singleLegStream = env.fromCollection(singleLegOrders)
                .assignTimestampsAndWatermarks(
                    WatermarkStrategy.<SingleLegOrderEvent>forBoundedOutOfOrderness(Duration.ofMillis(100))
                        .withIdleness(Duration.ofMillis(200))
                        .withTimestampAssigner((event, timestamp) -> {
                            System.out.println("*** [单腿订单] 时间戳分配: " + event.getOrd_nbr() + 
                                " -> " + event.getEventTimestamp() + " (" + formatTimestamp(event.getEventTimestamp()) + ")");
                            return event.getEventTimestamp();
                        })
                );
        
        SingleOutputStreamOperator<BaseOrderBook> baseOrderBookStream = singleLegStream
                .keyBy(SingleLegOrderEvent::getContract_cde)
                .process(new BaseOrderBookBuilder())
                .name("基础层订单簿构建");
        
        // 获取BBO广播流
        DataStream<BBO_Update> bboStream = baseOrderBookStream.getSideOutput(BaseOrderBookBuilder.BBO_OUTPUT_TAG);
        
        // 2. 创建BBO广播流
        org.apache.flink.streaming.api.datastream.BroadcastStream<BBO_Update> bboBroadcastStream = 
                bboStream.broadcast(VirtualOrderBookBuilder.BBO_STATE_DESCRIPTOR);
        
        // 3. 虚拟层处理：组合订单 + BBO广播 -> 虚拟订单簿
        DataStream<CombinationOrderEvent> comboStream = env.fromCollection(comboOrders)
                .assignTimestampsAndWatermarks(
                    WatermarkStrategy.<CombinationOrderEvent>forBoundedOutOfOrderness(Duration.ofMillis(100))
                        .withIdleness(Duration.ofMillis(200))
                        .withTimestampAssigner((event, timestamp) -> {
                            System.out.println("*** [组合订单] 时间戳分配: " + event.getOrd_nbr() + 
                                " -> " + event.getEventTimestamp() + " (" + formatTimestamp(event.getEventTimestamp()) + ")");
                            return event.getEventTimestamp();
                        })
                );
        
        DataStream<VirtualOrderBook> virtualOrderBookStream = comboStream
                .keyBy(CombinationOrderEvent::getLeg_1_contract_cde)
                .connect(bboBroadcastStream)
                .process(new VirtualOrderBookBuilder())
                .name("虚拟层订单簿构建");
        
        // 4. 转换为统一的OrderBookSnapshot格式
        DataStream<OrderBookSnapshot> baseSnapshots = baseOrderBookStream
                .map(baseBook -> {
                    OrderBookSnapshot snapshot = new OrderBookSnapshot(baseBook.getInstrumentId());
                    snapshot.setBids(baseBook.getBids());
                    snapshot.setAsks(baseBook.getAsks());
                    snapshot.setTimestamp(baseBook.getTimestamp());
                    System.out.println("*** [基础层转换] 合约=" + baseBook.getInstrumentId() + 
                        ", 时间戳=" + snapshot.getTimestamp() + " (" + formatTimestamp(snapshot.getTimestamp()) + ")");
                    return snapshot;
                })
                .name("基础层转换");
        
        DataStream<OrderBookSnapshot> virtualSnapshots = virtualOrderBookStream
                .filter(virtualBook -> !virtualBook.isEmpty())
                .map(virtualBook -> {
                    OrderBookSnapshot snapshot = new OrderBookSnapshot(virtualBook.getInstrumentId());
                    snapshot.setBids(virtualBook.getVirtualBids());
                    snapshot.setAsks(virtualBook.getVirtualAsks());
                    snapshot.setTimestamp(virtualBook.getTimestamp());
                    System.out.println("*** [虚拟层转换] 合约=" + virtualBook.getInstrumentId() + 
                        ", 时间戳=" + snapshot.getTimestamp() + " (" + formatTimestamp(snapshot.getTimestamp()) + ")");
                    return snapshot;
                })
                .name("虚拟层转换");
        
        // 5. 合并两个流并分配水位线
        DataStream<OrderBookSnapshot> allSnapshots = baseSnapshots.union(virtualSnapshots)
                .assignTimestampsAndWatermarks(
                    WatermarkStrategy.<OrderBookSnapshot>forBoundedOutOfOrderness(Duration.ofMillis(100))
                        .withIdleness(Duration.ofMillis(200))
                        .withTimestampAssigner((snapshot, timestamp) -> {
                            System.out.println("*** [合并流水位线] 合约=" + snapshot.getContract_cde() + 
                                ", 时间戳=" + snapshot.getTimestamp() + " (" + formatTimestamp(snapshot.getTimestamp()) + ")");
                            return snapshot.getTimestamp();
                        })
                );
        
        // 6. 全局快照聚合和输出
        DataStream<String> globalSnapshotStream = allSnapshots
                .keyBy(snapshot -> "GLOBAL_KEY")
                .process(new LayeredGlobalSnapshotAggregator())
                .setParallelism(1)
                .name("分层全局快照聚合器");
        
        // 输出结果
        baseOrderBookStream.print("🏗️ 基础层订单簿");
        bboStream.print("📡 BBO广播");
        virtualOrderBookStream.print("🔮 虚拟层订单簿");
        globalSnapshotStream.print("📊 全局快照");
        
        logger.info("开始执行分层订单簿调试演示...");
        env.execute("分层订单簿调试演示");
    }
    
    /**
     * 创建测试用的单腿订单数据 - 使用当前时间
     */
    private static List<SingleLegOrderEvent> createTestSingleLegOrders() {
        long baseTime = System.currentTimeMillis();
        
        SingleLegOrderEvent order1 = new SingleLegOrderEvent();
        order1.setOrd_nbr("SL001");
        order1.setContract_cde("EB2504");
        order1.setB_s_tag("B");
        order1.setOrd_prc(8095.0);
        order1.setOrd_vol(10);
        order1.setRmn_vol(10);
        order1.setOrd_sts("3"); // 未成交在队列中
        order1.setEventTimestamp(baseTime);
        
        SingleLegOrderEvent order2 = new SingleLegOrderEvent();
        order2.setOrd_nbr("SL002");
        order2.setContract_cde("EB2504");
        order2.setB_s_tag("S");
        order2.setOrd_prc(8096.0);
        order2.setOrd_vol(5);
        order2.setRmn_vol(5);
        order2.setOrd_sts("3"); // 未成交在队列中
        order2.setEventTimestamp(baseTime + 100);
        
        SingleLegOrderEvent order3 = new SingleLegOrderEvent();
        order3.setOrd_nbr("SL003");
        order3.setContract_cde("EB2505");
        order3.setB_s_tag("B");
        order3.setOrd_prc(8036.0);
        order3.setOrd_vol(1);
        order3.setRmn_vol(1);
        order3.setOrd_sts("3"); // 未成交在队列中
        order3.setEventTimestamp(baseTime + 200);
        
        SingleLegOrderEvent order4 = new SingleLegOrderEvent();
        order4.setOrd_nbr("SL004");
        order4.setContract_cde("EB2505");
        order4.setB_s_tag("S");
        order4.setOrd_prc(8037.0);
        order4.setOrd_vol(2);
        order4.setRmn_vol(2);
        order4.setOrd_sts("3"); // 未成交在队列中
        order4.setEventTimestamp(baseTime + 300);
        
        // 添加更多订单以触发0.5秒窗口
        SingleLegOrderEvent order5 = new SingleLegOrderEvent();
        order5.setOrd_nbr("SL005");
        order5.setContract_cde("EB2504");
        order5.setB_s_tag("B");
        order5.setOrd_prc(8094.0);
        order5.setOrd_vol(3);
        order5.setRmn_vol(3);
        order5.setOrd_sts("3");
        order5.setEventTimestamp(baseTime + 600); // 0.6秒后，应该触发第一个窗口
        
        return Arrays.asList(order1, order2, order3, order4, order5);
    }
    
    /**
     * 创建测试用的组合订单数据 - 使用当前时间
     */
    private static List<CombinationOrderEvent> createTestCombinationOrders() {
        long baseTime = System.currentTimeMillis();
        
        CombinationOrderEvent comboOrder1 = new CombinationOrderEvent();
        comboOrder1.setOrd_nbr("COMBO001");
        comboOrder1.setContract_cde("SPD_EB2504_EB2505");
        comboOrder1.setLeg_1_contract_cde("EB2504");
        comboOrder1.setLeg_2_contract_cde("EB2505");
        comboOrder1.setB_s_tag("B");
        comboOrder1.setOrd_prc(59.0); // 价差 8095-8036=59
        comboOrder1.setOrd_vol(1);
        comboOrder1.setRmn_vol(1);
        comboOrder1.setOrd_sts("3"); // 未成交在队列中
        comboOrder1.setEventTimestamp(baseTime + 400);
        
        return Arrays.asList(comboOrder1);
    }
    
    /**
     * 格式化时间戳
     */
    private static String formatTimestamp(long timestamp) {
        return java.time.LocalDateTime.ofInstant(
                java.time.Instant.ofEpochMilli(timestamp),
                java.time.ZoneId.of("Asia/Shanghai")
        ).format(java.time.format.DateTimeFormatter.ofPattern("HH:mm:ss.SSS"));
    }
}
