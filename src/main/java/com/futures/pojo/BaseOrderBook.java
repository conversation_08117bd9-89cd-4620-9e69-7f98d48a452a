package com.futures.pojo;

import java.io.Serializable;
import java.util.Collections;
import java.util.Map;
import java.util.TreeMap;

/**
 * 基础层订单簿
 * 只包含真实的单腿订单，不包含组合订单的虚拟挂单
 */
public class BaseOrderBook implements Serializable {
    private static final long serialVersionUID = 1L;
    
    private String instrumentId;                    // 合约代码
    private Map<Double, Long> bids;                // 买盘 (价格 -> 数量)
    private Map<Double, Long> asks;                // 卖盘 (价格 -> 数量)
    private long timestamp;                        // 快照时间戳
    private int totalBidOrders;                    // 买单总数
    private int totalAskOrders;                    // 卖单总数
    
    public BaseOrderBook() {
        this.bids = new TreeMap<>(Collections.reverseOrder()); // 买盘降序
        this.asks = new TreeMap<>();                           // 卖盘升序
        this.timestamp = System.currentTimeMillis();
    }
    
    public BaseOrderBook(String instrumentId) {
        this();
        this.instrumentId = instrumentId;
    }
    
    // Getter and Setter methods
    public String getInstrumentId() {
        return instrumentId;
    }
    
    public void setInstrumentId(String instrumentId) {
        this.instrumentId = instrumentId;
    }
    
    public Map<Double, Long> getBids() {
        return bids;
    }
    
    public void setBids(Map<Double, Long> bids) {
        this.bids = bids;
    }
    
    public Map<Double, Long> getAsks() {
        return asks;
    }
    
    public void setAsks(Map<Double, Long> asks) {
        this.asks = asks;
    }
    
    public long getTimestamp() {
        return timestamp;
    }
    
    public void setTimestamp(long timestamp) {
        this.timestamp = timestamp;
    }
    
    public int getTotalBidOrders() {
        return totalBidOrders;
    }
    
    public void setTotalBidOrders(int totalBidOrders) {
        this.totalBidOrders = totalBidOrders;
    }
    
    public int getTotalAskOrders() {
        return totalAskOrders;
    }
    
    public void setTotalAskOrders(int totalAskOrders) {
        this.totalAskOrders = totalAskOrders;
    }
    
    /**
     * 获取最优买价
     */
    public Double getBestBid() {
        return bids.isEmpty() ? null : bids.keySet().iterator().next();
    }
    
    /**
     * 获取最优卖价
     */
    public Double getBestAsk() {
        return asks.isEmpty() ? null : asks.keySet().iterator().next();
    }
    
    /**
     * 获取最优买价对应的数量
     */
    public Long getBestBidVolume() {
        Double bestBid = getBestBid();
        return bestBid != null ? bids.get(bestBid) : 0L;
    }
    
    /**
     * 获取最优卖价对应的数量
     */
    public Long getBestAskVolume() {
        Double bestAsk = getBestAsk();
        return bestAsk != null ? asks.get(bestAsk) : 0L;
    }
    
    /**
     * 计算买卖价差
     */
    public double getSpread() {
        Double bestBid = getBestBid();
        Double bestAsk = getBestAsk();
        return (bestBid != null && bestAsk != null) ? bestAsk - bestBid : 0.0;
    }
    
    /**
     * 检查订单簿是否为空
     */
    public boolean isEmpty() {
        return bids.isEmpty() && asks.isEmpty();
    }
    
    /**
     * 获取买盘深度（档数）
     */
    public int getBidDepth() {
        return bids.size();
    }
    
    /**
     * 获取卖盘深度（档数）
     */
    public int getAskDepth() {
        return asks.size();
    }
    
    /**
     * 生成BBO更新事件
     */
    public BBO_Update generateBBO() {
        Double bestBid = getBestBid();
        Double bestAsk = getBestAsk();
        
        return new BBO_Update(
            instrumentId,
            bestBid != null ? bestBid : 0.0,
            bestAsk != null ? bestAsk : Double.MAX_VALUE,
            getBestBidVolume(),
            getBestAskVolume(),
            timestamp
        );
    }
    
    @Override
    public String toString() {
        return String.format("BaseOrderBook{instrument='%s', bidDepth=%d, askDepth=%d, spread=%.2f, time=%d}",
                instrumentId, getBidDepth(), getAskDepth(), getSpread(), timestamp);
    }
}
