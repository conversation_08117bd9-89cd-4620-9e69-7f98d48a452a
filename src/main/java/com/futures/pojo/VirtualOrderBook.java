package com.futures.pojo;

import java.io.Serializable;
import java.util.Collections;
import java.util.Map;
import java.util.TreeMap;

/**
 * 虚拟层订单簿
 * 包含组合订单的虚拟挂单，基于基础层BBO计算得出
 */
public class VirtualOrderBook implements Serializable {
    private static final long serialVersionUID = 1L;
    
    private String instrumentId;                    // 合约代码（通常是组合合约的某一腿）
    private Map<Double, Long> virtualBids;         // 虚拟买盘 (价格 -> 数量)
    private Map<Double, Long> virtualAsks;         // 虚拟卖盘 (价格 -> 数量)
    private long timestamp;                        // 快照时间戳
    private int totalVirtualBidOrders;             // 虚拟买单总数
    private int totalVirtualAskOrders;             // 虚拟卖单总数
    private String sourceComboContract;            // 来源组合合约代码
    
    public VirtualOrderBook() {
        this.virtualBids = new TreeMap<>(Collections.reverseOrder()); // 买盘降序
        this.virtualAsks = new TreeMap<>();                           // 卖盘升序
        this.timestamp = System.currentTimeMillis();
    }
    
    public VirtualOrderBook(String instrumentId) {
        this();
        this.instrumentId = instrumentId;
    }
    
    // Getter and Setter methods
    public String getInstrumentId() {
        return instrumentId;
    }
    
    public void setInstrumentId(String instrumentId) {
        this.instrumentId = instrumentId;
    }
    
    public Map<Double, Long> getVirtualBids() {
        return virtualBids;
    }
    
    public void setVirtualBids(Map<Double, Long> virtualBids) {
        this.virtualBids = virtualBids;
    }
    
    public Map<Double, Long> getVirtualAsks() {
        return virtualAsks;
    }
    
    public void setVirtualAsks(Map<Double, Long> virtualAsks) {
        this.virtualAsks = virtualAsks;
    }
    
    public long getTimestamp() {
        return timestamp;
    }
    
    public void setTimestamp(long timestamp) {
        this.timestamp = timestamp;
    }
    
    public int getTotalVirtualBidOrders() {
        return totalVirtualBidOrders;
    }
    
    public void setTotalVirtualBidOrders(int totalVirtualBidOrders) {
        this.totalVirtualBidOrders = totalVirtualBidOrders;
    }
    
    public int getTotalVirtualAskOrders() {
        return totalVirtualAskOrders;
    }
    
    public void setTotalVirtualAskOrders(int totalVirtualAskOrders) {
        this.totalVirtualAskOrders = totalVirtualAskOrders;
    }
    
    public String getSourceComboContract() {
        return sourceComboContract;
    }
    
    public void setSourceComboContract(String sourceComboContract) {
        this.sourceComboContract = sourceComboContract;
    }
    
    /**
     * 获取最优虚拟买价
     */
    public Double getBestVirtualBid() {
        return virtualBids.isEmpty() ? null : virtualBids.keySet().iterator().next();
    }
    
    /**
     * 获取最优虚拟卖价
     */
    public Double getBestVirtualAsk() {
        return virtualAsks.isEmpty() ? null : virtualAsks.keySet().iterator().next();
    }
    
    /**
     * 获取最优虚拟买价对应的数量
     */
    public Long getBestVirtualBidVolume() {
        Double bestBid = getBestVirtualBid();
        return bestBid != null ? virtualBids.get(bestBid) : 0L;
    }
    
    /**
     * 获取最优虚拟卖价对应的数量
     */
    public Long getBestVirtualAskVolume() {
        Double bestAsk = getBestVirtualAsk();
        return bestAsk != null ? virtualAsks.get(bestAsk) : 0L;
    }
    
    /**
     * 计算虚拟买卖价差
     */
    public double getVirtualSpread() {
        Double bestBid = getBestVirtualBid();
        Double bestAsk = getBestVirtualAsk();
        return (bestBid != null && bestAsk != null) ? bestAsk - bestBid : 0.0;
    }
    
    /**
     * 检查虚拟订单簿是否为空
     */
    public boolean isEmpty() {
        return virtualBids.isEmpty() && virtualAsks.isEmpty();
    }
    
    /**
     * 获取虚拟买盘深度（档数）
     */
    public int getVirtualBidDepth() {
        return virtualBids.size();
    }
    
    /**
     * 获取虚拟卖盘深度（档数）
     */
    public int getVirtualAskDepth() {
        return virtualAsks.size();
    }
    
    /**
     * 添加虚拟买单
     */
    public void addVirtualBid(double price, long volume) {
        virtualBids.merge(price, volume, Long::sum);
    }
    
    /**
     * 添加虚拟卖单
     */
    public void addVirtualAsk(double price, long volume) {
        virtualAsks.merge(price, volume, Long::sum);
    }
    
    /**
     * 清空虚拟订单簿
     */
    public void clear() {
        virtualBids.clear();
        virtualAsks.clear();
        totalVirtualBidOrders = 0;
        totalVirtualAskOrders = 0;
    }
    
    @Override
    public String toString() {
        return String.format("VirtualOrderBook{instrument='%s', source='%s', bidDepth=%d, askDepth=%d, spread=%.2f, time=%d}",
                instrumentId, sourceComboContract, getVirtualBidDepth(), getVirtualAskDepth(), getVirtualSpread(), timestamp);
    }
}
