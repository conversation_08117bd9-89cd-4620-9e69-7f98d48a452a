package com.futures.pojo;

import java.io.Serializable;

/**
 * BBO (Best Bid and Offer) 更新事件
 * 用于在基础层和虚拟层之间传递最优买卖价信息
 */
public class BBO_Update implements Serializable {
    private static final long serialVersionUID = 1L;
    
    private String instrumentId;  // 合约代码
    private double bestBid;       // 最优买价
    private double bestAsk;       // 最优卖价
    private long bestBidVolume;   // 最优买价对应的数量
    private long bestAskVolume;   // 最优卖价对应的数量
    private long timestamp;       // 更新时间戳
    
    public BBO_Update() {}
    
    public BBO_Update(String instrumentId, double bestBid, double bestAsk, 
                     long bestBidVolume, long bestAskVolume, long timestamp) {
        this.instrumentId = instrumentId;
        this.bestBid = bestBid;
        this.bestAsk = bestAsk;
        this.bestBidVolume = bestBidVolume;
        this.bestAskVolume = bestAskVolume;
        this.timestamp = timestamp;
    }
    
    // Getter and Setter methods
    public String getInstrumentId() {
        return instrumentId;
    }
    
    public void setInstrumentId(String instrumentId) {
        this.instrumentId = instrumentId;
    }
    
    public double getBestBid() {
        return bestBid;
    }
    
    public void setBestBid(double bestBid) {
        this.bestBid = bestBid;
    }
    
    public double getBestAsk() {
        return bestAsk;
    }
    
    public void setBestAsk(double bestAsk) {
        this.bestAsk = bestAsk;
    }
    
    public long getBestBidVolume() {
        return bestBidVolume;
    }
    
    public void setBestBidVolume(long bestBidVolume) {
        this.bestBidVolume = bestBidVolume;
    }
    
    public long getBestAskVolume() {
        return bestAskVolume;
    }
    
    public void setBestAskVolume(long bestAskVolume) {
        this.bestAskVolume = bestAskVolume;
    }
    
    public long getTimestamp() {
        return timestamp;
    }
    
    public void setTimestamp(long timestamp) {
        this.timestamp = timestamp;
    }
    
    /**
     * 检查BBO是否有效（买卖价都大于0）
     */
    public boolean isValid() {
        return bestBid > 0 && bestAsk > 0 && bestAsk >= bestBid;
    }
    
    /**
     * 计算买卖价差
     */
    public double getSpread() {
        return isValid() ? bestAsk - bestBid : 0.0;
    }
    
    /**
     * 计算中间价
     */
    public double getMidPrice() {
        return isValid() ? (bestBid + bestAsk) / 2.0 : 0.0;
    }
    
    @Override
    public String toString() {
        return String.format("BBO_Update{instrument='%s', bid=%.2f(%d), ask=%.2f(%d), spread=%.2f, time=%d}",
                instrumentId, bestBid, bestBidVolume, bestAsk, bestAskVolume, getSpread(), timestamp);
    }
}
