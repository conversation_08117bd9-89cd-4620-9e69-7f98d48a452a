package com.futures.pojo;

import java.io.Serializable;
import java.util.Collections;
import java.util.Map;
import java.util.TreeMap;

// 订单簿快照
public class OrderBookSnapshot implements Serializable {
    private static final long serialVersionUID = 1L;
    private String contract_cde;
    private Map<Double, Long> bids;  // 买单簿 (价格 -> 数量)
    private Map<Double, Long> asks;  // 卖单簿 (价格 -> 数量)
    private long timestamp;

    public OrderBookSnapshot(String contract_cde) {
        this.contract_cde = contract_cde;
        this.bids = new TreeMap<>(Collections.reverseOrder()); // 降序排列
        this.asks = new TreeMap<>(); // 升序排列
        this.timestamp = System.currentTimeMillis();
    }

    // getter/setter方法
    public String getContract_cde() { return contract_cde; }
    public Map<Double, Long> getBids() { return bids; }
    public Map<Double, Long> getAsks() { return asks; }
    public void setBids(Map<Double, Long> bids) { this.bids = bids; }
    public void setAsks(Map<Double, Long> asks) { this.asks = asks; }
    public long getTimestamp() { return timestamp; }
    public void setTimestamp(long timestamp) { this.timestamp = timestamp; }

    public Double getBestBid() {
        return bids.isEmpty() ? null : bids.keySet().iterator().next();
    }

    public Double getBestAsk() {
        return asks.isEmpty() ? null : asks.keySet().iterator().next();
    }
}
