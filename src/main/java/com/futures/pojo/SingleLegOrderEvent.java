package com.futures.pojo;

import java.io.Serializable;

// 单腿委托事件
public class SingleLegOrderEvent implements Serializable {
    private static final long serialVersionUID = 1L;
    private String ord_nbr;
    private String contract_cde;
    private String b_s_tag;
    private String ocpos_type;  // 开平仓类型
    private double ord_prc;
    private long ord_vol;
    private long rmn_vol;  // 剩余数量
    private long trd_vol;   // 成交数量
    private String ord_sts;  // 订单状态
    private String settle_memb_memb_cde;  // 结算会员代码
    private String ord_dt;  // 订单日期
    private String ord_tm;  // 订单时间
    private long event_timestamp;

    public SingleLegOrderEvent() {}

    // getter/setter方法
    public String getOrd_nbr() { return ord_nbr; }
    public void setOrd_nbr(String ord_nbr) { this.ord_nbr = ord_nbr; }

    public String getContract_cde() { return contract_cde; }
    public void setContract_cde(String contract_cde) { this.contract_cde = contract_cde; }

    public String getB_s_tag() { return b_s_tag; }
    public void setB_s_tag(String b_s_tag) { this.b_s_tag = b_s_tag; }

    public double getOrd_prc() { return ord_prc; }
    public void setOrd_prc(double ord_prc) { this.ord_prc = ord_prc; }

    public long getOrd_vol() { return ord_vol; }
    public void setOrd_vol(long ord_vol) { this.ord_vol = ord_vol; }

    public long getRmn_vol() { return rmn_vol; }
    public void setRmn_vol(long rmn_vol) { this.rmn_vol = rmn_vol; }

    public String getOcpos_type() { return ocpos_type; }
    public void setOcpos_type(String ocpos_type) { this.ocpos_type = ocpos_type; }

    public long getTrd_vol() { return trd_vol; }
    public void setTrd_vol(long trd_vol) { this.trd_vol = trd_vol; }

    public String getOrd_sts() { return ord_sts; }
    public void setOrd_sts(String ord_sts) { this.ord_sts = ord_sts; }

    public String getSettle_memb_memb_cde() { return settle_memb_memb_cde; }
    public void setSettle_memb_memb_cde(String settle_memb_memb_cde) { this.settle_memb_memb_cde = settle_memb_memb_cde; }

    public String getOrd_dt() { return ord_dt; }
    public void setOrd_dt(String ord_dt) { this.ord_dt = ord_dt; }

    public String getOrd_tm() { return ord_tm; }
    public void setOrd_tm(String ord_tm) { this.ord_tm = ord_tm; }

    public long getEventTimestamp() { return event_timestamp; }
    public void setEventTimestamp(long timestamp) { this.event_timestamp = timestamp; }
}