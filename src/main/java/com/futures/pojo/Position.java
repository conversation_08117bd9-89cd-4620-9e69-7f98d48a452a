package com.futures.pojo;

import java.io.Serializable;

// 持仓信息 - 支持FIFO平仓
public class Position implements Serializable {
    private static final long serialVersionUID = 1L;
    private String contract_cde;
    private String b_s_tag;
    private double open_price;
    private long volume;
    private long timestamp; // 记录创建时间
    private long openTimestamp; // 记录开仓事件时间，用于FIFO排序

    public Position(String contract_cde, String b_s_tag, double open_price, long volume) {
        this.contract_cde = contract_cde;
        this.b_s_tag = b_s_tag;
        this.open_price = open_price;
        this.volume = volume;
        this.timestamp = System.currentTimeMillis();
        this.openTimestamp = System.currentTimeMillis(); // 默认为当前时间
    }

    // getter/setter方法
    public String getContract_cde() { return contract_cde; }
    public String getB_s_tag() { return b_s_tag; }
    public double getOpen_price() { return open_price; }
    public long getVolume() { return volume; }
    public void setVolume(long volume) { this.volume = volume; }
    public long getTimestamp() { return timestamp; }
    
    // 新增开仓时间戳相关方法
    public long getOpenTimestamp() { return openTimestamp; }
    public void setOpenTimestamp(long openTimestamp) { this.openTimestamp = openTimestamp; }
}
