package com.futures.pojo;

import java.io.Serializable;
import java.util.Collections;
import java.util.Map;
import java.util.TreeMap;

/**
 * 分层订单簿快照
 * 合并基础层和虚拟层的订单簿数据，用于最终输出
 */
public class LayeredOrderBookSnapshot implements Serializable {
    private static final long serialVersionUID = 1L;
    
    private String instrumentId;                    // 合约代码
    private Map<Double, Long> totalBids;           // 合并后的买盘 (基础层 + 虚拟层)
    private Map<Double, Long> totalAsks;           // 合并后的卖盘 (基础层 + 虚拟层)
    private Map<Double, Long> baseBids;            // 基础层买盘
    private Map<Double, Long> baseAsks;            // 基础层卖盘
    private Map<Double, Long> virtualBids;         // 虚拟层买盘
    private Map<Double, Long> virtualAsks;         // 虚拟层卖盘
    private long timestamp;                        // 快照时间戳
    private int baseOrderCount;                    // 基础层订单数
    private int virtualOrderCount;                 // 虚拟层订单数
    
    public LayeredOrderBookSnapshot() {
        this.totalBids = new TreeMap<>(Collections.reverseOrder());
        this.totalAsks = new TreeMap<>();
        this.baseBids = new TreeMap<>(Collections.reverseOrder());
        this.baseAsks = new TreeMap<>();
        this.virtualBids = new TreeMap<>(Collections.reverseOrder());
        this.virtualAsks = new TreeMap<>();
        this.timestamp = System.currentTimeMillis();
    }
    
    public LayeredOrderBookSnapshot(String instrumentId) {
        this();
        this.instrumentId = instrumentId;
    }
    
    /**
     * 从基础层和虚拟层订单簿构建分层快照
     */
    public static LayeredOrderBookSnapshot merge(BaseOrderBook baseBook, VirtualOrderBook virtualBook) {
        String instrumentId = baseBook != null ? baseBook.getInstrumentId() : 
                             (virtualBook != null ? virtualBook.getInstrumentId() : "UNKNOWN");
        
        LayeredOrderBookSnapshot snapshot = new LayeredOrderBookSnapshot(instrumentId);
        
        // 复制基础层数据
        if (baseBook != null) {
            snapshot.baseBids.putAll(baseBook.getBids());
            snapshot.baseAsks.putAll(baseBook.getAsks());
            snapshot.baseOrderCount = baseBook.getTotalBidOrders() + baseBook.getTotalAskOrders();
            snapshot.timestamp = baseBook.getTimestamp();
        }
        
        // 复制虚拟层数据
        if (virtualBook != null) {
            snapshot.virtualBids.putAll(virtualBook.getVirtualBids());
            snapshot.virtualAsks.putAll(virtualBook.getVirtualAsks());
            snapshot.virtualOrderCount = virtualBook.getTotalVirtualBidOrders() + virtualBook.getTotalVirtualAskOrders();
            if (baseBook == null) {
                snapshot.timestamp = virtualBook.getTimestamp();
            }
        }
        
        // 合并总订单簿
        snapshot.totalBids.putAll(snapshot.baseBids);
        snapshot.totalAsks.putAll(snapshot.baseAsks);
        
        // 合并虚拟层到总订单簿
        for (Map.Entry<Double, Long> entry : snapshot.virtualBids.entrySet()) {
            snapshot.totalBids.merge(entry.getKey(), entry.getValue(), Long::sum);
        }
        for (Map.Entry<Double, Long> entry : snapshot.virtualAsks.entrySet()) {
            snapshot.totalAsks.merge(entry.getKey(), entry.getValue(), Long::sum);
        }
        
        return snapshot;
    }
    
    // Getter and Setter methods
    public String getInstrumentId() {
        return instrumentId;
    }
    
    public void setInstrumentId(String instrumentId) {
        this.instrumentId = instrumentId;
    }
    
    public Map<Double, Long> getTotalBids() {
        return totalBids;
    }
    
    public Map<Double, Long> getTotalAsks() {
        return totalAsks;
    }
    
    public Map<Double, Long> getBaseBids() {
        return baseBids;
    }
    
    public Map<Double, Long> getBaseAsks() {
        return baseAsks;
    }
    
    public Map<Double, Long> getVirtualBids() {
        return virtualBids;
    }
    
    public Map<Double, Long> getVirtualAsks() {
        return virtualAsks;
    }
    
    public long getTimestamp() {
        return timestamp;
    }
    
    public void setTimestamp(long timestamp) {
        this.timestamp = timestamp;
    }
    
    public int getBaseOrderCount() {
        return baseOrderCount;
    }
    
    public int getVirtualOrderCount() {
        return virtualOrderCount;
    }
    
    /**
     * 获取最优买价（合并后）
     */
    public Double getBestBid() {
        return totalBids.isEmpty() ? null : totalBids.keySet().iterator().next();
    }
    
    /**
     * 获取最优卖价（合并后）
     */
    public Double getBestAsk() {
        return totalAsks.isEmpty() ? null : totalAsks.keySet().iterator().next();
    }
    
    /**
     * 计算价差（合并后）
     */
    public double getSpread() {
        Double bestBid = getBestBid();
        Double bestAsk = getBestAsk();
        return (bestBid != null && bestAsk != null) ? bestAsk - bestBid : 0.0;
    }
    
    /**
     * 检查是否有数据
     */
    public boolean hasData() {
        return !totalBids.isEmpty() || !totalAsks.isEmpty();
    }
    
    /**
     * 检查是否只有基础层数据
     */
    public boolean hasOnlyBaseLayer() {
        return (!baseBids.isEmpty() || !baseAsks.isEmpty()) && 
               (virtualBids.isEmpty() && virtualAsks.isEmpty());
    }
    
    /**
     * 检查是否只有虚拟层数据
     */
    public boolean hasOnlyVirtualLayer() {
        return (baseBids.isEmpty() && baseAsks.isEmpty()) && 
               (!virtualBids.isEmpty() || !virtualAsks.isEmpty());
    }
    
    /**
     * 检查是否有混合层数据
     */
    public boolean hasMixedLayers() {
        return (!baseBids.isEmpty() || !baseAsks.isEmpty()) && 
               (!virtualBids.isEmpty() || !virtualAsks.isEmpty());
    }
    
    @Override
    public String toString() {
        return String.format("LayeredOrderBook{instrument='%s', total=%d档, base=%d档, virtual=%d档, spread=%.2f}",
                instrumentId, totalBids.size() + totalAsks.size(), 
                baseBids.size() + baseAsks.size(), virtualBids.size() + virtualAsks.size(), getSpread());
    }
}
