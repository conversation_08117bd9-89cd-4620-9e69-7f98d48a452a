package com.futures.pojo;

import java.io.Serializable;

// 成交明细事件
public class TradeEvent implements Serializable {
    private static final long serialVersionUID = 1L;
    private String trd_dt;
    private String trd_tm;    // 成交时间
    private String trd_nbr;   // 成交编号，用于识别配对交易
    private String ord_nbr;
    private String contract_cde;
    private String b_s_tag;
    private double trd_prc;
    private long trd_vol;
    private String trd_type;  // "2"表示组合套利衍生成交
    private String settle_memb_unfy_cde;
    private String settle_memb_memb_cde;
    private String ocpos_type;  // 开平仓类型
    private long event_timestamp;

    // 构造函数、getter、setter省略...
    public TradeEvent() {}

    public long getEventTimestamp() {
        return event_timestamp;
    }

    public void setEventTimestamp(long timestamp) {
        this.event_timestamp = timestamp;
    }

    public String getTrd_dt() {
        return trd_dt;
    }

    public void setTrd_dt(String trd_dt) {
        this.trd_dt = trd_dt;
    }

    public String getTrd_tm() { return trd_tm; }
    public void setTrd_tm(String trd_tm) { this.trd_tm = trd_tm; }

    // 其他getter/setter方法...
    public String getTrd_nbr() { return trd_nbr; }
    public void setTrd_nbr(String trd_nbr) { this.trd_nbr = trd_nbr; }

    public String getOrd_nbr() { return ord_nbr; }
    public void setOrd_nbr(String ord_nbr) { this.ord_nbr = ord_nbr; }

    public String getContract_cde() { return contract_cde; }
    public void setContract_cde(String contract_cde) { this.contract_cde = contract_cde; }

    public String getB_s_tag() { return b_s_tag; }
    public void setB_s_tag(String b_s_tag) { this.b_s_tag = b_s_tag; }

    public double getTrd_prc() { return trd_prc; }
    public void setTrd_prc(double trd_prc) { this.trd_prc = trd_prc; }

    public long getTrd_vol() { return trd_vol; }
    public void setTrd_vol(long trd_vol) { this.trd_vol = trd_vol; }

    public String getTrd_type() { return trd_type; }
    public void setTrd_type(String trd_type) { this.trd_type = trd_type; }

    public String getSettle_memb_unfy_cde() { return settle_memb_unfy_cde; }
    public void setSettle_memb_unfy_cde(String settle_memb_unfy_cde) { this.settle_memb_unfy_cde = settle_memb_unfy_cde; }

    public String getSettle_memb_memb_cde() { return settle_memb_memb_cde; }
    public void setSettle_memb_memb_cde(String settle_memb_memb_cde) { this.settle_memb_memb_cde = settle_memb_memb_cde; }

    public String getOcpos_type() { return ocpos_type; }
    public void setOcpos_type(String ocpos_type) { this.ocpos_type = ocpos_type; }
}