package com.futures.job;

import com.futures.function.OrderBookReconstructionFunction;
import com.futures.function.PnLCalculationFunction;
import com.futures.function.CombinationOrderSplitter;
import com.futures.function.BaseOrderBookBuilder;
import com.futures.function.VirtualOrderBookBuilder;
import com.futures.function.LayeredGlobalSnapshotAggregator;
import com.futures.pojo.*;
import org.apache.flink.api.common.eventtime.WatermarkStrategy;
import org.apache.flink.api.common.serialization.SimpleStringSchema;
import org.apache.flink.api.common.state.ValueState;
import org.apache.flink.api.common.state.ValueStateDescriptor;
import org.apache.flink.streaming.api.datastream.DataStream;
import org.apache.flink.streaming.api.datastream.SingleOutputStreamOperator;
import org.apache.flink.streaming.api.environment.StreamExecutionEnvironment;
import org.apache.flink.connector.kafka.source.KafkaSource;
import org.apache.flink.connector.kafka.source.enumerator.initializer.OffsetsInitializer;
import org.apache.flink.api.common.functions.MapFunction;
import org.apache.flink.streaming.api.functions.KeyedProcessFunction;
import org.apache.flink.util.Collector;
import org.apache.flink.streaming.api.functions.ProcessFunction;
import org.apache.flink.api.common.state.MapState;
import org.apache.flink.api.common.state.MapStateDescriptor;
import org.apache.flink.api.common.typeinfo.TypeHint;
import org.apache.flink.api.common.typeinfo.TypeInformation;
import org.apache.flink.configuration.Configuration;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.ObjectMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.time.Duration;
import java.util.*;
import java.util.Properties;
import java.util.Map;

/**
 * 基于Kafka数据源的期货订单簿重建作业
 * 支持只消费部分消息和debug日志输出
 */
public class FuturesOrderBookKafkaJob {
    private static final Logger logger = LoggerFactory.getLogger(FuturesOrderBookKafkaJob.class);
    private static final ObjectMapper objectMapper = new ObjectMapper();

    public static void main(String[] args) throws Exception {
        logger.info("启动期货全量订单簿重建系统(Kafka版)...");
        
        // 解析环境参数
        String environment = "local"; // 默认本地测试
        String kafkaBootstrapServers = "localhost:9092"; // 默认本地Kafka
        String consumerGroupId = "orderbook-local-test-group"; // 默认本地测试组
        
        if (args.length > 0) {
            environment = args[0];
        }
        
        // 根据环境选择配置
        if ("server".equalsIgnoreCase(environment) || "production".equalsIgnoreCase(environment)) {
            kafkaBootstrapServers = "************:8087";
            consumerGroupId = "orderbook-production-group";
            logger.info("使用服务器环境配置: {}", kafkaBootstrapServers);
        } else {
            logger.info("使用本地测试环境配置: {}", kafkaBootstrapServers);
        }
        
        StreamExecutionEnvironment env = StreamExecutionEnvironment.getExecutionEnvironment();
        env.setParallelism(1);

        // Kafka配置 - 根据环境动态配置
        Properties props = new Properties();
        props.setProperty("bootstrap.servers", kafkaBootstrapServers);
        props.setProperty("group.id", consumerGroupId);
        props.setProperty("auto.offset.reset", "earliest");
        props.setProperty("enable.auto.commit", "false");
        
        // 根据环境调整性能配置
        if ("server".equalsIgnoreCase(environment) || "production".equalsIgnoreCase(environment)) {
            // 生产环境性能优化配置
            props.setProperty("fetch.min.bytes", "1024");
            props.setProperty("fetch.max.wait.ms", "100");
            props.setProperty("max.poll.records", "1000");
            props.setProperty("session.timeout.ms", "30000");
            props.setProperty("heartbeat.interval.ms", "10000");
            logger.info("应用生产环境Kafka优化配置");
        } else {
            // 本地测试配置，更快的响应
            props.setProperty("fetch.min.bytes", "1");
            props.setProperty("fetch.max.wait.ms", "500");
            props.setProperty("max.poll.records", "100");
            props.setProperty("session.timeout.ms", "10000");
            props.setProperty("heartbeat.interval.ms", "3000");
            logger.info("应用本地测试Kafka配置");
        }

        // 只消费部分消息（本地测试时限制更少，生产环境可以更多）
        int maxRecords = "server".equalsIgnoreCase(environment) ? 5000 : 500;

        // 单腿订单流 - 使用动态配置的KafkaSource
        KafkaSource<String> singleLegSource = KafkaSource.<String>builder()
                .setBootstrapServers(kafkaBootstrapServers)
                .setTopics("singleleg_order_data_event")
                .setGroupId(consumerGroupId)
                .setStartingOffsets(OffsetsInitializer.earliest())
                .setValueOnlyDeserializer(new SimpleStringSchema())
                .setProperties(props)
                .build();

        DataStream<String> singleLegStringStream = env
                .fromSource(singleLegSource,
                    WatermarkStrategy.<String>forBoundedOutOfOrderness(Duration.ofSeconds(2))
                        .withIdleness(Duration.ofMillis(500)) // 减少空闲检测时间，确保水位线推进
                        .withTimestampAssigner((event, timestamp) -> {
                            // 解析时间戳用于watermark - 使用统一时区
                            try {
                                Map<String, Object> jsonMap = objectMapper.readValue(event, Map.class);
                                String ordDt = (String) jsonMap.get("ord_dt");
                                String ordTm = (String) jsonMap.get("ord_tm");
                                Object ordTmMillisecObj = jsonMap.get("ord_tm_millisec");
                                long ordTmMillisec = ordTmMillisecObj != null ? ((Number) ordTmMillisecObj).longValue() : 0L;
                                long eventTimestamp = FuturesOrderBookJob.parseTimestampWithMillisec(ordDt, ordTm, ordTmMillisec);
                                System.out.println("*** 单腿订单时间戳分配: " + jsonMap.get("ord_nbr") + " -> " + eventTimestamp + " (" + ordDt + " " + ordTm + "." + ordTmMillisec + ")");
                                return eventTimestamp;
                            } catch (Exception e) {
                                System.out.println("*** 单腿订单时间戳解析失败: " + e.getMessage());
                                // 返回当前时间确保水位线能够推进
                                return System.currentTimeMillis();
                            }
                        }), "Kafka-单腿订单")
                .process(new LimitProcessFunction(maxRecords, "[Kafka-SingleLeg]"));

        // 组合订单流 - 使用动态配置
        KafkaSource<String> combSource = KafkaSource.<String>builder()
                .setBootstrapServers(kafkaBootstrapServers)
                .setTopics("cmb_order_data_event")
                .setGroupId(consumerGroupId)
                .setStartingOffsets(OffsetsInitializer.earliest())
                .setValueOnlyDeserializer(new SimpleStringSchema())
                .setProperties(props)
                .build();

        DataStream<String> combStringStream = env
                .fromSource(combSource,
                    WatermarkStrategy.<String>forBoundedOutOfOrderness(Duration.ofSeconds(2))
                        .withIdleness(Duration.ofMillis(500)) // 减少空闲检测时间，确保水位线推进
                        .withTimestampAssigner((event, timestamp) -> {
                            try {
                                Map<String, Object> jsonMap = objectMapper.readValue(event, Map.class);
                                String ordDt = (String) jsonMap.get("ord_dt");
                                String ordTm = (String) jsonMap.get("ord_tm");
                                Object ordTmMillisecObj = jsonMap.get("ord_tm_millisec");
                                long ordTmMillisec = ordTmMillisecObj != null ? ((Number) ordTmMillisecObj).longValue() : 0L;
                                return FuturesOrderBookJob.parseTimestampWithMillisec(ordDt, ordTm, ordTmMillisec);
                            } catch (Exception e) {
                                // 返回当前时间确保水位线能够推进
                                return System.currentTimeMillis();
                            }
                        }), "Kafka-组合订单")
                .process(new LimitProcessFunction(maxRecords, "[Kafka-Comb]"));

        // 交易流 - 使用动态配置
        KafkaSource<String> tradeSource = KafkaSource.<String>builder()
                .setBootstrapServers(kafkaBootstrapServers)
                .setTopics("trade_data_event")
                .setGroupId(consumerGroupId)
                .setStartingOffsets(OffsetsInitializer.earliest())
                .setValueOnlyDeserializer(new SimpleStringSchema())
                .setProperties(props)
                .build();

        DataStream<String> tradeStringStream = env
                .fromSource(tradeSource,
                    WatermarkStrategy.<String>forBoundedOutOfOrderness(Duration.ofSeconds(2))
                        .withIdleness(Duration.ofMillis(500)) // 减少空闲检测时间，确保水位线推进
                        .withTimestampAssigner((event, timestamp) -> {
                            try {
                                Map<String, Object> jsonMap = objectMapper.readValue(event, Map.class);
                                String trdDt = (String) jsonMap.get("trd_dt");
                                String trdTm = (String) jsonMap.get("trd_tm");
                                Object trdTmMillisecObj = jsonMap.get("trd_tm_millisec");
                                long trdTmMillisec = trdTmMillisecObj != null ? ((Number) trdTmMillisecObj).longValue() : 0L;
                                return FuturesOrderBookJob.parseTimestampWithMillisec(trdDt, trdTm, trdTmMillisec);
                            } catch (Exception e) {
                                // 返回当前时间确保水位线能够推进
                                return System.currentTimeMillis();
                            }
                        }), "Kafka-交易")
                .process(new LimitProcessFunction(maxRecords, "[Kafka-Trade]"));

        // 解析与处理
        DataStream<SingleLegOrderEvent> singleLegStream = singleLegStringStream
                .map(new DebugSingleLegOrderParser())
                .filter(order -> order != null)
                .name("解析单腿订单");

        DataStream<CombinationOrderEvent> combStream = combStringStream
                .map(new DebugCombinationOrderParser())
                .filter(order -> order != null)
                .name("解析组合订单");

        DataStream<TradeEvent> tradeStream = tradeStringStream
                .map(new DebugTradeEventParser())
                .filter(trade -> trade != null)
                .name("解析交易事件");

        // === 新的分层架构：基础层和虚拟层分离处理 ===

        // 1. 基础层处理：单腿订单 -> 基础订单簿 + BBO流
        SingleOutputStreamOperator<BaseOrderBook> baseOrderBookStream = singleLegStream
                .keyBy(SingleLegOrderEvent::getContract_cde)
                .process(new BaseOrderBookBuilder())
                .name("基础层订单簿构建");

        // 获取BBO广播流
        DataStream<BBO_Update> bboStream = baseOrderBookStream.getSideOutput(BaseOrderBookBuilder.BBO_OUTPUT_TAG);

        // 2. 创建BBO广播流
        org.apache.flink.streaming.api.datastream.BroadcastStream<BBO_Update> bboBroadcastStream =
                bboStream.broadcast(VirtualOrderBookBuilder.BBO_STATE_DESCRIPTOR);

        // 3. 虚拟层处理：组合订单 + BBO广播 -> 虚拟订单簿
        DataStream<VirtualOrderBook> virtualOrderBookStream = combStream
                .keyBy(CombinationOrderEvent::getLeg_1_contract_cde) // 使用腿1合约作为分组键
                .connect(bboBroadcastStream)
                .process(new VirtualOrderBookBuilder())
                .name("虚拟层订单簿构建");

        // 4. 为了兼容现有的PnL计算和输出，将分层订单簿转换为原有格式
        DataStream<OrderBookSnapshot> orderBookStream = mergeLayeredOrderBooks(baseOrderBookStream, virtualOrderBookStream);

        // 5. 为合并后的订单簿流重新分配水位线，确保定时器能够正常触发
        // 针对历史数据使用特殊的水位线策略
        DataStream<OrderBookSnapshot> watermarkedOrderBookStream = orderBookStream
                .assignTimestampsAndWatermarks(
                    WatermarkStrategy.<OrderBookSnapshot>forMonotonousTimestamps()
                        .withIdleness(Duration.ofMillis(100))
                        .withTimestampAssigner((snapshot, timestamp) -> {
                            long eventTime = snapshot.getTimestamp();
                            // 对于历史数据，使用当前时间确保水位线能够推进
                            long adjustedTime = System.currentTimeMillis();
                            System.out.println("*** [水位线分配] 合约=" + snapshot.getContract_cde() +
                                ", 原始时间=" + eventTime + " (" + formatTimestamp(eventTime) + ")" +
                                ", 调整时间=" + adjustedTime + " (" + formatTimestamp(adjustedTime) + ")");
                            return adjustedTime;
                        })
                )
                .name("订单簿水位线分配");

        // PnL计算
        SingleOutputStreamOperator<PnLResult> pnlStream = tradeStream
                .keyBy(TradeEvent::getSettle_memb_memb_cde)
                .process(new PnLCalculationFunction())
                .name("PnL计算");
                
        // 获取异常交易和持仓盈亏侧输出流
        DataStream<TradeEvent> anomalyTradeStream = pnlStream.getSideOutput(PnLCalculationFunction.ANOMALY_TRADE_OUTPUT_TAG);
        DataStream<PnLResult> mtmPnlStream = pnlStream.getSideOutput(PnLCalculationFunction.MTM_PNL_OUTPUT_TAG);

        // 全局快照聚合和输出 - 使用优化的分层聚合器
        DataStream<String> globalSnapshotStream = watermarkedOrderBookStream
                .keyBy(snapshot -> "GLOBAL_KEY")
                .process(new LayeredGlobalSnapshotAggregator())
                .setParallelism(1)
                .name("分层全局快照聚合器");

        // 输出完整监控信息
        globalSnapshotStream.print("全量订单簿");
        pnlStream.map(FuturesOrderBookKafkaJob::formatPnLResult).print("PnL结果");
        
        // 输出异常交易和持仓盈亏监控
        anomalyTradeStream.map(trade -> String.format("[Kafka-异常交易] 合约:%s, 交易号:%s, 方向:%s, 数量:%d - 无对应持仓",
            trade.getContract_cde(), trade.getTrd_nbr(), trade.getB_s_tag(), trade.getTrd_vol()))
            .print("Kafka-异常交易监控");
            
        mtmPnlStream.map(pnl -> String.format("[Kafka-每日会员盈亏] 会员:%s, 日期:%s, 持仓盈亏:%.2f",
            pnl.getSettle_memb_memb_cde(), pnl.getDate(), pnl.getTotal_pnl()))
            .print("Kafka-每日会员盈亏");

        // 添加每日会员盈亏汇总
        DataStream<String> dailyPnLSummary = mtmPnlStream
            .keyBy(pnl -> pnl.getDate()) // 按日期分组
            .process(new DailyPnLSummaryFunction())
            .name("每日盈亏汇总");
            
        dailyPnLSummary.print("每日盈亏汇总");

        logger.info("开始执行Flink作业(Kafka版 - {} 环境)...", environment);
        env.execute("期货全量订单簿重建系统-Kafka-" + environment);
    }

    // 只取部分消息的ProcessFunction
    public static class LimitProcessFunction extends ProcessFunction<String, String> {
        private static final Logger logger = LoggerFactory.getLogger(LimitProcessFunction.class);
        private final int maxCount;
        private final String debugPrefix;
        private int count = 0;
        public LimitProcessFunction(int maxCount, String debugPrefix) {
            this.maxCount = maxCount;
            this.debugPrefix = debugPrefix;
        }
        @Override
        public void processElement(String value, Context ctx, Collector<String> out) throws Exception {
            if (count < maxCount) {
                logger.debug("{} {}", debugPrefix, value);
                out.collect(value);
                count++;
            }
        }
    }

    // Debug解析器
    private static class DebugSingleLegOrderParser implements MapFunction<String, SingleLegOrderEvent> {
        private static final Logger logger = LoggerFactory.getLogger("TRADE_PROCESSING");

        @Override
        public SingleLegOrderEvent map(String jsonStr) throws Exception {
            SingleLegOrderEvent order = FuturesOrderBookJob.parseSingleLegOrder(jsonStr);
            if (order != null) {
                logger.debug("单腿订单解析: 合约={}, 订单号={}, 状态={}, 剩余量={}",
                    order.getContract_cde(), order.getOrd_nbr(), order.getOrd_sts(), order.getRmn_vol());
            }
            return order;
        }
    }

    // 调试订单分组映射器
    private static class DebugOrderGroupingMapper implements MapFunction<Object, Object> {
        @Override
        public Object map(Object order) throws Exception {
            String contractCode = extractContractCode(order);

            if (order instanceof SingleLegOrderEvent) {
                SingleLegOrderEvent singleOrder = (SingleLegOrderEvent) order;
            }

            return order;
        }
    }
    private static class DebugCombinationOrderParser implements MapFunction<String, CombinationOrderEvent> {
        private static final Logger logger = LoggerFactory.getLogger("TRADE_PROCESSING");
        
        @Override
        public CombinationOrderEvent map(String jsonStr) throws Exception {
            CombinationOrderEvent order = FuturesOrderBookJob.parseCombinationOrder(jsonStr);
            if (order != null) {
                logger.debug("组合订单解析: 合约={}, 订单号={}, 状态={}, 剩余量={}", 
                    order.getContract_cde(), order.getOrd_nbr(), order.getOrd_sts(), order.getRmn_vol());
            }
            return order;
        }
    }
    private static class DebugTradeEventParser implements MapFunction<String, TradeEvent> {
        private static final Logger logger = LoggerFactory.getLogger("TRADE_PROCESSING");
        
        @Override
        public TradeEvent map(String jsonStr) throws Exception {
            TradeEvent trade = FuturesOrderBookJob.parseTradeEvent(jsonStr);
            if (trade != null) {
                logger.info("交易解析: 合约={}, 交易号={}, 会员={}, 方向={}, 成交量={}, 成交价={}, 开平={}, 类型={}", 
                    trade.getContract_cde(), 
                    trade.getTrd_nbr(), 
                    trade.getSettle_memb_memb_cde(),
                    trade.getB_s_tag(),
                    trade.getTrd_vol(),
                    trade.getTrd_prc(),
                    trade.getOcpos_type(),
                    trade.getTrd_type());
            }
            return trade;
        }
    }

    private static String extractContractCode(Object order) {
        String contractCode = null;

        if (order instanceof SingleLegOrderEvent) {
            SingleLegOrderEvent singleOrder = (SingleLegOrderEvent) order;
            contractCode = singleOrder.getContract_cde();


        } else if (order instanceof CombinationOrderEvent) {
            // 对于组合订单，我们使用腿1合约作为主要分组键
            // 这样确保组合订单的两条腿都能在同一个处理实例中协调处理
            CombinationOrderEvent combOrder = (CombinationOrderEvent) order;
            contractCode = combOrder.getLeg_1_contract_cde();

            System.out.println("*** [合约代码提取] 组合订单: 订单号=" + combOrder.getOrd_nbr() +
                ", 提取的合约代码=" + contractCode + ", 腿1=" + combOrder.getLeg_1_contract_cde() +
                ", 腿2=" + combOrder.getLeg_2_contract_cde());

        } else if (order instanceof CombinationOrderSplitter.CombinationOrderLegInfo) {
            CombinationOrderSplitter.CombinationOrderLegInfo legInfo =
                (CombinationOrderSplitter.CombinationOrderLegInfo) order;
            contractCode = legInfo.getCurrentLegContract();

            System.out.println("*** [合约代码提取] 组合订单腿: 订单号=" + legInfo.getOrdNbr() +
                ", 腿号=" + legInfo.getLegNumber() + ", 提取的合约代码=" + contractCode);
        }

        if (contractCode == null || contractCode.trim().isEmpty()) {
            System.out.println("*** [合约代码提取错误] 无法提取合约代码: 订单类型=" + order.getClass().getSimpleName());
            contractCode = "UNKNOWN_CONTRACT";
        }

        return contractCode;
    }
    private static String formatPnLResult(PnLResult pnl) {
        return String.format("[PnL] 会员:%s PnL:%.2f 日期:%s",
                pnl.getSettle_memb_memb_cde(),
                pnl.getTotal_pnl(),
                pnl.getDate());
    }

    /**
     * Kafka专用全局快照聚合器
     * 针对流式数据和实时性进行优化
     */
    public static class KafkaGlobalSnapshotAggregator extends ProcessFunction<OrderBookSnapshot, String> {
        private static final Logger logger = LoggerFactory.getLogger("ORDERBOOK_SNAPSHOT");
        private static final long SNAPSHOT_INTERVAL_MS = 500; // 改为500毫秒间隔，满足0.5秒生成全量订单簿要求
        private static final long MAX_OUT_OF_ORDERNESS_MS = 250; // 最大乱序容忍时间，调整为0.25秒

        private transient MapState<Long, Map<String, OrderBookSnapshot>> snapshotBuffer;
        private transient MapState<String, Long> lastSnapshotTime; // 记录每个合约的最后快照时间
        private long lastWatermark = Long.MIN_VALUE;

        @Override
        public void open(Configuration parameters) throws Exception {
            MapStateDescriptor<Long, Map<String, OrderBookSnapshot>> bufferDescriptor = 
                new MapStateDescriptor<>(
                    "kafka-global-snapshot-buffer",
                    TypeInformation.of(Long.class),
                    TypeInformation.of(new TypeHint<Map<String, OrderBookSnapshot>>() {})
                );
            snapshotBuffer = getRuntimeContext().getMapState(bufferDescriptor);

            MapStateDescriptor<String, Long> timeDescriptor = 
                new MapStateDescriptor<>(
                    "kafka-last-snapshot-time",
                    TypeInformation.of(String.class),
                    TypeInformation.of(Long.class)
                );
            lastSnapshotTime = getRuntimeContext().getMapState(timeDescriptor);
        }

        @Override
        public void processElement(OrderBookSnapshot snapshot, Context ctx, Collector<String> out) throws Exception {
            long currentWatermark = ctx.timerService().currentWatermark();
            long eventTime = snapshot.getTimestamp();
            String contractCode = snapshot.getContract_cde();


            logger.debug("接收快照: 合约={}, 事件时间={}, 水位线={}",
                contractCode, eventTime, currentWatermark);

            // 检查事件时间的有效性
            Long lastTime = lastSnapshotTime.get(contractCode);
            if (lastTime != null && eventTime < lastTime - MAX_OUT_OF_ORDERNESS_MS) {
                logger.warn("丢弃过期快照: 合约={}, 事件时间={}, 最后时间={}",
                    contractCode, eventTime, lastTime);
                return; // 丢弃过期数据
            }

            // 修复时间窗口计算逻辑 - 确保正确的0.5秒间隔窗口
            // 将事件时间向上取整到最近的500ms边界
            long windowEnd = ((eventTime / SNAPSHOT_INTERVAL_MS) + 1) * SNAPSHOT_INTERVAL_MS;


            Map<String, OrderBookSnapshot> buffer = snapshotBuffer.get(windowEnd);
            if (buffer == null) {
                buffer = new HashMap<>();
                snapshotBuffer.put(windowEnd, buffer);
            }

            // 只保留最新的快照（同一窗口内同一合约可能有多个快照）
            OrderBookSnapshot existing = buffer.get(contractCode);
            if (existing == null || snapshot.getTimestamp() >= existing.getTimestamp()) {
                buffer.put(contractCode, snapshot);
                lastSnapshotTime.put(contractCode, eventTime);

            }

            System.out.println("*** 缓存快照到窗口: " + windowEnd + " (" + formatTimestamp(windowEnd) +
                "), 当前合约数: " + buffer.size());
            logger.debug("缓存快照到窗口: {}, 当前合约数: {}", windowEnd, buffer.size());

            // 注册事件时间定时器
            ctx.timerService().registerEventTimeTimer(windowEnd + MAX_OUT_OF_ORDERNESS_MS);

            // 实时输出单个合约快照（可选，用于调试）
            if (snapshot.getBids().size() > 0 || snapshot.getAsks().size() > 0) {
                String singleSnapshot = formatSingleContractSnapshot(snapshot);
                logger.debug("实时快照: {}", singleSnapshot);

            }
        }

        @Override
        public void onTimer(long timestamp, OnTimerContext ctx, Collector<String> out) throws Exception {
            long actualWindowEnd = timestamp - MAX_OUT_OF_ORDERNESS_MS;
            long currentWatermark = ctx.timerService().currentWatermark();

            System.out.println("*** [定时器触发] 窗口结束时间=" + actualWindowEnd +
                " (" + formatTimestamp(actualWindowEnd) + "), 触发时间=" + timestamp +
                ", 水位线=" + currentWatermark);

            logger.debug("定时器触发: 窗口结束时间={}, 触发时间={}, 水位线={}",
                actualWindowEnd, timestamp, currentWatermark);

            Map<String, OrderBookSnapshot> finalSnapshots = snapshotBuffer.get(actualWindowEnd);

            if (finalSnapshots != null && !finalSnapshots.isEmpty()) {

                logger.info("输出全局快照: 窗口时间={}, 合约数={}",
                    actualWindowEnd, finalSnapshots.size());

                outputKafkaGlobalSnapshot(finalSnapshots, actualWindowEnd, out);
                snapshotBuffer.remove(actualWindowEnd);

                // 清理过期的快照时间记录
                cleanupExpiredTimestamps(actualWindowEnd);
            } else {
                System.out.println("*** [定时器触发] 窗口 " + actualWindowEnd +
                    " (" + formatTimestamp(actualWindowEnd) + ") 无快照数据");
                logger.debug("窗口 {} 无快照数据", actualWindowEnd);
            }
        }

        /**
         * 格式化时间戳为可读格式
         */
        private String formatTimestamp(long timestamp) {
            return java.time.LocalDateTime.ofInstant(
                java.time.Instant.ofEpochMilli(timestamp),
                java.time.ZoneId.of("Asia/Shanghai")
            ).format(java.time.format.DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss.SSS"));
        }

        private void cleanupExpiredTimestamps(long windowEnd) throws Exception {
            // 清理超过5分钟的旧记录
            long expiredThreshold = windowEnd - 5 * 60 * 1000;
            
            Iterator<Map.Entry<String, Long>> iterator = lastSnapshotTime.entries().iterator();
            while (iterator.hasNext()) {
                Map.Entry<String, Long> entry = iterator.next();
                if (entry.getValue() < expiredThreshold) {
                    iterator.remove();
                }
            }
        }

        private String formatSingleContractSnapshot(OrderBookSnapshot snapshot) {
            StringBuilder sb = new StringBuilder();
            sb.append(String.format("合约:%s ", snapshot.getContract_cde()));
            
            if (!snapshot.getAsks().isEmpty()) {
                double bestAsk = snapshot.getAsks().keySet().stream().mapToDouble(Double::doubleValue).min().orElse(0.0);
                sb.append(String.format("卖一:%.2f(%d) ", bestAsk, snapshot.getAsks().get(bestAsk)));
            }
            
            if (!snapshot.getBids().isEmpty()) {
                double bestBid = snapshot.getBids().keySet().stream().mapToDouble(Double::doubleValue).max().orElse(0.0);
                sb.append(String.format("买一:%.2f(%d) ", bestBid, snapshot.getBids().get(bestBid)));
            }
            
            return sb.toString().trim();
        }
        
        /**
         * Kafka优化的全局快照聚合器 - 支持基础层和虚拟层分析
         * 针对流式数据和实时性进行优化
         */
        private void outputKafkaGlobalSnapshot(Map<String, OrderBookSnapshot> finalSnapshots, long windowEnd,
                                             Collector<String> out) {
            StringBuilder sb = new StringBuilder();
            sb.append(String.format("\n=============== Kafka分层全局订单簿快照 (事件时间: %s) ===============\n",
                    formatTimestamp(windowEnd)));

            List<String> sortedContracts = new ArrayList<>(finalSnapshots.keySet());
            Collections.sort(sortedContracts);

            int totalContracts = sortedContracts.size();
            int activeContracts = 0;


            for (String contract : sortedContracts) {
                OrderBookSnapshot snapshot = finalSnapshots.get(contract);
                boolean hasData = !snapshot.getAsks().isEmpty() || !snapshot.getBids().isEmpty();
                if (hasData) activeContracts++;


                sb.append(String.format("--- 合约: %s %s---\n", contract, hasData ? "" : "(空) "));

                if (hasData) {
                    Map<Double, Long> sortedAsks = new TreeMap<>(snapshot.getAsks());
                    Map<Double, Long> sortedBids = new TreeMap<>(Collections.reverseOrder());
                    sortedBids.putAll(snapshot.getBids());

                    // 显示前5档，包含层次信息提示
                    sb.append("  卖盘 (基础层+虚拟层):\n");
                    sortedAsks.entrySet().stream().limit(5).forEach(entry ->
                        sb.append(String.format("    %-10.2f -> %d\n", entry.getKey(), entry.getValue())));

                    sb.append("  买盘 (基础层+虚拟层):\n");
                    sortedBids.entrySet().stream().limit(5).forEach(entry ->
                        sb.append(String.format("    %-10.2f -> %d\n", entry.getKey(), entry.getValue())));

                    // 添加统计信息
                    double spread = 0.0;
                    if (!sortedAsks.isEmpty() && !sortedBids.isEmpty()) {
                        double bestAsk = sortedAsks.keySet().iterator().next();
                        double bestBid = sortedBids.keySet().iterator().next();
                        spread = bestAsk - bestBid;
                    }
                    sb.append(String.format("  价差: %.2f, 买盘档数: %d, 卖盘档数: %d\n",
                        spread, sortedBids.size(), sortedAsks.size()));
                }
            }
            
            sb.append(String.format("=============== 总合约: %d, 活跃合约: %d (含基础层+虚拟层) ===============\n", 
                totalContracts, activeContracts));
            out.collect(sb.toString());
        }
    }
    
    /**
     * 每日盈亏汇总函数
     * 按日期汇总所有会员的盈亏情况
     */
    public static class DailyPnLSummaryFunction extends KeyedProcessFunction<String, PnLResult, String> {
        private static final Logger logger = LoggerFactory.getLogger("PNL_CALCULATION");
        private transient MapState<String, Double> memberPnLs; // 会员代码 -> 盈亏金额
        private transient ValueState<Long> lastProcessTime;
        
        @Override
        public void open(Configuration parameters) throws Exception {
            MapStateDescriptor<String, Double> pnlDescriptor = 
                new MapStateDescriptor<>("member-pnls", String.class, Double.class);
            memberPnLs = getRuntimeContext().getMapState(pnlDescriptor);
            
            ValueStateDescriptor<Long> timeDescriptor =
                new ValueStateDescriptor<>("last-process-time", Long.class, 0L);
            lastProcessTime = getRuntimeContext().getState(timeDescriptor);
        }
        
        @Override
        public void processElement(PnLResult pnl, Context ctx, Collector<String> out) throws Exception {
            String memberCode = pnl.getSettle_memb_memb_cde();
            String date = pnl.getDate();
            
            // 更新会员盈亏
            memberPnLs.put(memberCode, pnl.getTotal_pnl());
            
            // 每5秒输出一次汇总（避免过于频繁的输出）
            long currentTime = System.currentTimeMillis();
            Long lastTime = lastProcessTime.value();
            
            if (currentTime - lastTime > 5000) { // 5秒间隔
                outputDailySummary(date, out);
                lastProcessTime.update(currentTime);
            }
        }
        
        private void outputDailySummary(String date, Collector<String> out) throws Exception {
            StringBuilder summary = new StringBuilder();
            summary.append(String.format("\n========== %s 每日会员盈亏汇总 ==========\n", date));
            
            double totalPnL = 0.0;
            int memberCount = 0;
            double maxPnL = Double.NEGATIVE_INFINITY;
            double minPnL = Double.POSITIVE_INFINITY;
            String maxPnLMember = "";
            String minPnLMember = "";
            
            for (Map.Entry<String, Double> entry : memberPnLs.entries()) {
                String member = entry.getKey();
                double pnl = entry.getValue();
                
                summary.append(String.format("会员 %s: %.2f\n", member, pnl));
                
                totalPnL += pnl;
                memberCount++;
                
                if (pnl > maxPnL) {
                    maxPnL = pnl;
                    maxPnLMember = member;
                }
                
                if (pnl < minPnL) {
                    minPnL = pnl;
                    minPnLMember = member;
                }
            }
            
            summary.append("----------------------------------------\n");
            summary.append(String.format("会员总数: %d\n", memberCount));
            summary.append(String.format("总盈亏: %.2f\n", totalPnL));
            if (memberCount > 0) {
                summary.append(String.format("平均盈亏: %.2f\n", totalPnL / memberCount));
                summary.append(String.format("最大盈亏: %.2f (会员: %s)\n", maxPnL, maxPnLMember));
                summary.append(String.format("最小盈亏: %.2f (会员: %s)\n", minPnL, minPnLMember));
            }
            summary.append("==========================================\n");
            
            out.collect(summary.toString());
        }
    }

    /**
     * 合并基础层和虚拟层订单簿，转换为兼容的OrderBookSnapshot格式
     */
    private static DataStream<OrderBookSnapshot> mergeLayeredOrderBooks(
            DataStream<BaseOrderBook> baseStream,
            DataStream<VirtualOrderBook> virtualStream) {

        // 将基础层订单簿转换为OrderBookSnapshot
        DataStream<OrderBookSnapshot> baseSnapshots = baseStream
                .map(baseBook -> {
                    OrderBookSnapshot snapshot = new OrderBookSnapshot(baseBook.getInstrumentId());
                    snapshot.setBids(baseBook.getBids());
                    snapshot.setAsks(baseBook.getAsks());
                    snapshot.setTimestamp(baseBook.getTimestamp());
                    return snapshot;
                })
                .name("基础层转换");

        // 将虚拟层订单簿转换为OrderBookSnapshot
        DataStream<OrderBookSnapshot> virtualSnapshots = virtualStream
                .filter(virtualBook -> !virtualBook.isEmpty()) // 只输出非空的虚拟订单簿
                .map(virtualBook -> {
                    OrderBookSnapshot snapshot = new OrderBookSnapshot(virtualBook.getInstrumentId());
                    snapshot.setBids(virtualBook.getVirtualBids());
                    snapshot.setAsks(virtualBook.getVirtualAsks());
                    snapshot.setTimestamp(virtualBook.getTimestamp());
                    return snapshot;
                })
                .name("虚拟层转换");

        // 合并两个流
        return baseSnapshots.union(virtualSnapshots);
    }

    /**
     * 格式化时间戳为可读格式
     */
    private static String formatTimestamp(long timestamp) {
        return java.time.LocalDateTime.ofInstant(
                java.time.Instant.ofEpochMilli(timestamp),
                java.time.ZoneId.of("Asia/Shanghai")
        ).format(java.time.format.DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss.SSS"));
    }
}
