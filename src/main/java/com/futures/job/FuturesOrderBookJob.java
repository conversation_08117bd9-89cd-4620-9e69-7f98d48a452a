package com.futures.job;

import com.futures.function.OrderBookReconstructionFunction;
import com.futures.function.PnLCalculationFunction;
import com.futures.function.CombinationOrderSplitter;
import com.futures.pojo.*;
import org.apache.flink.api.common.eventtime.WatermarkStrategy;
import org.apache.flink.api.common.state.MapState;
import org.apache.flink.api.common.state.MapStateDescriptor;
import org.apache.flink.api.common.state.ValueState;
import org.apache.flink.api.common.state.ValueStateDescriptor;
import org.apache.flink.api.common.typeinfo.TypeHint;
import org.apache.flink.api.common.typeinfo.TypeInformation;
import org.apache.flink.configuration.Configuration;
import org.apache.flink.streaming.api.datastream.DataStream;
import org.apache.flink.streaming.api.datastream.SingleOutputStreamOperator;
import org.apache.flink.streaming.api.environment.StreamExecutionEnvironment;
import org.apache.flink.api.common.functions.MapFunction;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.ObjectMapper;
import org.apache.flink.streaming.api.functions.ProcessFunction;
import org.apache.flink.streaming.api.functions.source.SourceFunction;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.apache.flink.util.Collector;

import java.time.Duration;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;

/**
 * 期货全量订单簿重建系统 - 本地文件版本
 * 支持单腿订单和组合订单的订单簿重建，以及PnL计算
 */
public class FuturesOrderBookJob {
    
    private static final Logger logger = LoggerFactory.getLogger(FuturesOrderBookJob.class);
    private static final ObjectMapper objectMapper = new ObjectMapper();    public static void main(String[] args) throws Exception {
        logger.info("启动期货全量订单簿重建系统...");

        // 创建Flink执行环境
        StreamExecutionEnvironment env = StreamExecutionEnvironment.getExecutionEnvironment();
        env.setParallelism(1); // 本地调试使用单并行度

        logger.info("使用本地文件数据源进行测试...");

        // ============ 数据源配置 ============

        // 单腿订单数据流
        DataStream<String> singleLegStringStream = env
                .addSource(new JsonFileSource("data/single_leg_orders.json"))
                .assignTimestampsAndWatermarks(createWatermarkStrategy())
                .name("单腿订单文件源");

        // 组合订单数据流
        DataStream<String> combStringStream = env
                .addSource(new JsonFileSource("data/combination_orders.json"))
                .assignTimestampsAndWatermarks(createWatermarkStrategy())
                .name("组合订单文件源");

        // 交易数据流
        DataStream<String> tradeStringStream = env
                .addSource(new JsonFileSource("data/trades.json"))
                .assignTimestampsAndWatermarks(createTradeWatermarkStrategy())
                .name("交易数据文件源");

        // ============ JSON数据解析方法 ============

    /**
     * 解析单腿订单JSON数据
     */

        // 解析单腿订单
        DataStream<SingleLegOrderEvent> singleLegStream = singleLegStringStream
                .map(new SingleLegOrderParser())
                .filter(order -> order != null)
                .name("解析单腿订单");

        // 解析组合订单
        DataStream<CombinationOrderEvent> combStream = combStringStream
                .map(new CombinationOrderParser())
                .filter(order -> order != null)
                .name("解析组合订单");

        // 解析交易事件
        DataStream<TradeEvent> tradeStream = tradeStringStream
                .map(new TradeEventParser())
                .filter(trade -> trade != null)
                .name("解析交易事件");

        // ============ 订单簿重建处理 ============

        // 拆分组合订单
        SingleOutputStreamOperator<Object> combOrderSplitterStream = combStream
                .process(new CombinationOrderSplitter())
                .name("拆分组合订单");

        // 获取拆分后的腿数据流
        DataStream<CombinationOrderSplitter.CombinationOrderLegInfo> leg1Stream = 
            combOrderSplitterStream.getSideOutput(CombinationOrderSplitter.LEG1_OUTPUT_TAG);
        DataStream<CombinationOrderSplitter.CombinationOrderLegInfo> leg2Stream = 
            combOrderSplitterStream.getSideOutput(CombinationOrderSplitter.LEG2_OUTPUT_TAG);

        // 合并所有订单事件
        DataStream<Object> allOrderEvents = singleLegStream.<Object>map(order -> order)
                .union(leg1Stream.map(leg -> (Object) leg))
                .union(leg2Stream.map(leg -> (Object) leg));

        // 按合约分区并重建订单簿
        DataStream<OrderBookSnapshot> orderBookStream = allOrderEvents
                .keyBy(FuturesOrderBookJob::extractContractCode)
                .process(new OrderBookReconstructionFunction())
                .name("订单簿重建");

        // ============ PnL计算处理 ============

        SingleOutputStreamOperator<PnLResult> pnlStream = tradeStream
                .keyBy(TradeEvent::getSettle_memb_memb_cde)
                .process(new PnLCalculationFunction())
                .name("PnL计算");
                
        // 获取异常交易侧输出流
        DataStream<TradeEvent> anomalyTradeStream = pnlStream.getSideOutput(PnLCalculationFunction.ANOMALY_TRADE_OUTPUT_TAG);
        // 获取持仓盈亏侧输出流
        DataStream<PnLResult> mtmPnlStream = pnlStream.getSideOutput(PnLCalculationFunction.MTM_PNL_OUTPUT_TAG);

        // ============ 全局快照聚合和输出 ============

        DataStream<String> globalSnapshotStream = orderBookStream
                .keyBy(snapshot -> "GLOBAL_KEY")
                .process(new GlobalSnapshotAggregator())
                .setParallelism(1)
                .name("全局快照聚合器");

        // 输出结果
        globalSnapshotStream.print("全量订单簿");
        pnlStream.map(FuturesOrderBookJob::formatPnLResult).print("PnL结果");
        
        // 输出异常交易和持仓盈亏监控
        anomalyTradeStream.map(trade -> String.format("[异常交易] 合约:%s, 交易号:%s, 方向:%s, 数量:%d, 价格:%.2f - 无对应持仓",
            trade.getContract_cde(), trade.getTrd_nbr(), trade.getB_s_tag(), trade.getTrd_vol(), trade.getTrd_prc()))
            .print("异常交易监控");
            
        mtmPnlStream.map(pnl -> String.format("[持仓盈亏] 会员:%s, 日期:%s, 持仓盈亏:%.2f",
            pnl.getSettle_memb_memb_cde(), pnl.getDate(), pnl.getTotal_pnl()))
            .print("持仓盈亏");

        logger.info("开始执行Flink作业...");
        env.execute("期货全量订单簿重建系统");
    }

    // ============ 辅助方法 ============

    /**
     * 创建订单数据的水印策略
     */
    private static WatermarkStrategy<String> createWatermarkStrategy() {
        return WatermarkStrategy.<String>forBoundedOutOfOrderness(Duration.ofSeconds(1))
                .withTimestampAssigner((element, recordTimestamp) -> {
                    try {
                        Map<String, Object> jsonMap = objectMapper.readValue(element, Map.class);
                        String ordDt = getString(jsonMap, "ord_dt");
                        String ordTm = getString(jsonMap, "ord_tm");
                        long ordTmMillisec = getLong(jsonMap, "ord_tm_millisec");
                        return parseTimestampWithMillisec(ordDt, ordTm, ordTmMillisec);
                    } catch (Exception e) {
                        return System.currentTimeMillis();
                    }
                });
    }

    /**
     * 创建交易数据的水印策略
     */
    private static WatermarkStrategy<String> createTradeWatermarkStrategy() {
        return WatermarkStrategy.<String>forBoundedOutOfOrderness(Duration.ofSeconds(1))
                .withTimestampAssigner((element, recordTimestamp) -> {
                    try {
                        Map<String, Object> jsonMap = objectMapper.readValue(element, Map.class);
                        String trdDt = getString(jsonMap, "trd_dt");
                        String trdTm = getString(jsonMap, "trd_tm");
                        long trdTmMillisec = getLong(jsonMap, "trd_tm_millisec");
                        return parseTimestampWithMillisec(trdDt, trdTm, trdTmMillisec);
                    } catch (Exception e) {
                        return System.currentTimeMillis();
                    }
                });
    }

    /**
     * 提取合约代码用于分区
     */
    private static String extractContractCode(Object order) {
        if (order instanceof SingleLegOrderEvent) {
            return ((SingleLegOrderEvent) order).getContract_cde();
        } else if (order instanceof CombinationOrderSplitter.CombinationOrderLegInfo) {
            return ((CombinationOrderSplitter.CombinationOrderLegInfo) order).getCurrentLegContract();
        }
        return "UNKNOWN_CONTRACT";
    }

    /**
     * 格式化PnL结果
     */
    private static String formatPnLResult(PnLResult pnl) {
        return String.format("[PnL] 会员:%s PnL:%.2f 日期:%s",
                pnl.getSettle_memb_memb_cde(),
                pnl.getTotal_pnl(),
                pnl.getDate());
    }

    // ============ 数据解析器类 ============

    /**
     * 单腿订单解析器
     */
    private static class SingleLegOrderParser implements MapFunction<String, SingleLegOrderEvent> {
        @Override
        public SingleLegOrderEvent map(String jsonStr) throws Exception {
            return parseSingleLegOrder(jsonStr);
        }
    }

    /**
     * 组合订单解析器
     */
    private static class CombinationOrderParser implements MapFunction<String, CombinationOrderEvent> {
        @Override
        public CombinationOrderEvent map(String jsonStr) throws Exception {
            return parseCombinationOrder(jsonStr);
        }
    }

    /**
     * 交易事件解析器
     */
    private static class TradeEventParser implements MapFunction<String, TradeEvent> {
        @Override
        public TradeEvent map(String jsonStr) throws Exception {
            return parseTradeEvent(jsonStr);
        }
    }

    public static SingleLegOrderEvent parseSingleLegOrder(String jsonStr) throws Exception {
        // Clean up JSON string
        jsonStr = jsonStr.trim();
        if (jsonStr.isEmpty() || jsonStr.equals("{}")) {
            return null;
        }

        try {
            Map<String, Object> jsonMap = objectMapper.readValue(jsonStr, Map.class);

            // Check if this is a valid order record (must have ord_nbr)
            if (!jsonMap.containsKey("ord_nbr") || getString(jsonMap, "ord_nbr").isEmpty()) {
                return null;
            }

        SingleLegOrderEvent event = new SingleLegOrderEvent();
        event.setOrd_nbr(getString(jsonMap, "ord_nbr"));
        event.setContract_cde(getString(jsonMap, "contract_cde"));
        event.setB_s_tag(getString(jsonMap, "b_s_tag"));
        event.setOcpos_type(getString(jsonMap, "ocpos_type"));
        event.setOrd_prc(getDouble(jsonMap, "ord_prc"));
        event.setOrd_vol(getLong(jsonMap, "ord_vol"));
        event.setRmn_vol(getLong(jsonMap, "rmn_vol"));
        event.setTrd_vol(getLong(jsonMap, "trd_vol"));
        event.setOrd_sts(getString(jsonMap, "ord_sts"));
        event.setSettle_memb_memb_cde(getString(jsonMap, "settle_memb_memb_cde"));

            // Parse timestamp with milliseconds
            String ordDt = getString(jsonMap, "ord_dt");
            String ordTm = getString(jsonMap, "ord_tm");
            long ordTmMillisec = getLong(jsonMap, "ord_tm_millisec");
            event.setEventTimestamp(parseTimestampWithMillisec(ordDt, ordTm, ordTmMillisec));

            return event;
        } catch (Exception e) {
            System.err.println("解析单腿订单失败: " + jsonStr + ", 错误: " + e.getMessage());
            return null;
        }
    }

    public static CombinationOrderEvent parseCombinationOrder(String jsonStr) throws Exception {
        // Clean up JSON string
        jsonStr = jsonStr.trim();
        if (jsonStr.isEmpty() || jsonStr.equals("{}")) {
            return null;
        }

        try {
            Map<String, Object> jsonMap = objectMapper.readValue(jsonStr, Map.class);

            // Check if this is a valid order record (must have ord_nbr)
            if (!jsonMap.containsKey("ord_nbr") || getString(jsonMap, "ord_nbr").isEmpty()) {
                return null;
            }

        CombinationOrderEvent event = new CombinationOrderEvent();
        event.setOrd_nbr(getString(jsonMap, "ord_nbr"));
        event.setContract_cde(getString(jsonMap, "contract_cde"));
        
        // 解析组合合约代码，提取腿信息
        // 格式：SPD_EB2504_EB2505 -> leg1: EB2504, leg2: EB2505
        String contractCode = getString(jsonMap, "contract_cde");
        if (contractCode.startsWith("SPD_")) {
            String[] parts = contractCode.split("_");
            if (parts.length >= 3) {
                event.setLeg_1_contract_cde(parts[1]); // EB2504
                event.setLeg_2_contract_cde(parts[2]); // EB2505
            } else {
                // 如果解析失败，设置默认值
                event.setLeg_1_contract_cde("");
                event.setLeg_2_contract_cde("");
            }
        } else {
            // 非SPD格式，可能有其他字段
            event.setLeg_1_contract_cde(getString(jsonMap, "leg_1_contract_cde"));
            event.setLeg_2_contract_cde(getString(jsonMap, "leg_2_contract_cde"));
        }
        
        event.setB_s_tag(getString(jsonMap, "b_s_tag"));
        event.setOcpos_type(getString(jsonMap, "ocpos_type"));
        event.setOrd_prc(getDouble(jsonMap, "ord_prc"));
        event.setOrd_vol(getLong(jsonMap, "ord_vol"));
        event.setRmn_vol(getLong(jsonMap, "rmn_vol"));
        event.setTrd_vol(getLong(jsonMap, "trd_vol"));
        event.setOrd_sts(getString(jsonMap, "ord_sts"));

            // Parse timestamp with milliseconds
            String ordDt = getString(jsonMap, "ord_dt");
            String ordTm = getString(jsonMap, "ord_tm");
            long ordTmMillisec = getLong(jsonMap, "ord_tm_millisec");
            event.setEventTimestamp(parseTimestampWithMillisec(ordDt, ordTm, ordTmMillisec));

            return event;
        } catch (Exception e) {
            System.err.println("解析组合订单失败: " + jsonStr + ", 错误: " + e.getMessage());
            return null;
        }
    }

    public static TradeEvent parseTradeEvent(String jsonStr) throws Exception {
        // Clean up JSON string
        jsonStr = jsonStr.trim();
        if (jsonStr.isEmpty() || jsonStr.equals("{}")) {
            return null;
        }

        try {
            Map<String, Object> jsonMap = objectMapper.readValue(jsonStr, Map.class);

            // Check if this is a valid trade record (must have trd_nbr or ord_nbr)
            if ((!jsonMap.containsKey("trd_nbr") || getString(jsonMap, "trd_nbr").isEmpty()) &&
                (!jsonMap.containsKey("ord_nbr") || getString(jsonMap, "ord_nbr").isEmpty())) {
                return null;
            }

        TradeEvent event = new TradeEvent();
        event.setTrd_dt(getString(jsonMap, "trd_dt"));
        event.setTrd_nbr(getString(jsonMap, "trd_nbr"));
        event.setOrd_nbr(getString(jsonMap, "ord_nbr"));
        event.setContract_cde(getString(jsonMap, "contract_cde"));
        event.setB_s_tag(getString(jsonMap, "b_s_tag"));
        event.setTrd_prc(getDouble(jsonMap, "trd_prc"));
        event.setTrd_vol(getLong(jsonMap, "trd_vol"));
        event.setTrd_type(getString(jsonMap, "trd_type"));
        event.setSettle_memb_unfy_cde(getString(jsonMap, "settle_memb_unfy_cde"));
        event.setSettle_memb_memb_cde(getString(jsonMap, "settle_memb_memb_cde"));
        event.setOcpos_type(getString(jsonMap, "ocpos_type"));

            // Parse timestamp with milliseconds
            String trdDt = getString(jsonMap, "trd_dt");
            String trdTm = getString(jsonMap, "trd_tm");
            long trdTmMillisec = getLong(jsonMap, "trd_tm_millisec");
            event.setEventTimestamp(parseTimestampWithMillisec(trdDt, trdTm, trdTmMillisec));

            return event;
        } catch (Exception e) {
            System.err.println("解析交易事件失败: " + jsonStr + ", 错误: " + e.getMessage());
            return null;
        }
    }

    // ============ 时间戳解析工具方法 ============

    private static String getString(Map<String, Object> map, String key) {
        Object value = map.get(key);
        return value != null ? value.toString() : "";
    }

    private static double getDouble(Map<String, Object> map, String key) {
        Object value = map.get(key);
        if (value instanceof Number) {
            return ((Number) value).doubleValue();
        }
        try {
            return Double.parseDouble(value.toString());
        } catch (Exception e) {
            return 0.0;
        }
    }

    private static long getLong(Map<String, Object> map, String key) {
        Object value = map.get(key);
        if (value instanceof Number) {
            return ((Number) value).longValue();
        }
        try {
            return Long.parseLong(value.toString());
        } catch (Exception e) {
            return 0L;
        }
    }

    private static long parseTimestamp(String date, String time) {
        try {
            if (date == null || date.isEmpty() || time == null || time.isEmpty()) {
                return System.currentTimeMillis();
            }

            // Handle time format: 2025-03-10 and 9:17:33 or 14:27:44
            String normalizedTime = time;
            if (time.length() == 7 && time.charAt(1) == ':') {
                // Format like "9:17:33" -> "09:17:33"
                normalizedTime = "0" + time;
            }

            // Combine date and time
            String dateTimeStr = date + " " + normalizedTime;
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
            LocalDateTime dateTime = LocalDateTime.parse(dateTimeStr, formatter);

            // 统一使用上海时区，确保时间解析的一致性
            return dateTime.atZone(java.time.ZoneId.of("Asia/Shanghai")).toInstant().toEpochMilli();
        } catch (Exception e) {
            System.err.println("时间戳解析失败: " + date + " " + time + ", 错误: " + e.getMessage());
            return System.currentTimeMillis();
        }
    }

    /**
     * 解析带毫秒精度的时间戳
     */
    // 公共时间戳解析方法（供Kafka版本使用） - 修正时区问题
    public static long parseTimestampWithMillisec(String date, String time, long millisec) {
        try {
            if (date == null || date.isEmpty() || time == null || time.isEmpty()) {
                return System.currentTimeMillis();
            }

            // Handle time format: 2025-03-17 and 9:00:36
            String normalizedTime = time;
            if (time.length() == 7 && time.charAt(1) == ':') {
                // Format like "9:00:36" -> "09:00:36"
                normalizedTime = "0" + time;
            }

            // Combine date and time
            String dateTimeStr = date + " " + normalizedTime;
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
            LocalDateTime dateTime = LocalDateTime.parse(dateTimeStr, formatter);

            // 使用统一的时区（上海时区）而非系统默认时区，确保一致性
            // 在生产环境中，建议使用业务所在的时区或UTC
            long baseTimestamp = dateTime.atZone(java.time.ZoneId.of("Asia/Shanghai")).toInstant().toEpochMilli();
            return baseTimestamp + millisec;
        } catch (Exception e) {
            System.err.println("带毫秒的时间戳解析失败: " + date + " " + time + " " + millisec + ", 错误: " + e.getMessage());
            // Fallback to basic timestamp parsing
            return parseTimestamp(date, time);
        }
    }

    /**
     * 全局快照聚合器 - 修复版本
     * 主要修复：
     * 1. 修复定时器判断逻辑
     * 2. 简化状态管理
     * 3. 确保快照正确输出
     */
    public static class GlobalSnapshotAggregator extends ProcessFunction<OrderBookSnapshot, String> {
        private static final Logger logger = LoggerFactory.getLogger(GlobalSnapshotAggregator.class);
        private static final long SNAPSHOT_INTERVAL_MS = 500;

        private transient MapState<Long, Map<String, OrderBookSnapshot>> snapshotBuffer;
        private long lastWatermark = Long.MIN_VALUE;

        @Override
        public void open(Configuration parameters) throws Exception {
            MapStateDescriptor<Long, Map<String, OrderBookSnapshot>> descriptor = 
                new MapStateDescriptor<Long, Map<String, OrderBookSnapshot>>(
                    "global-snapshot-buffer",
                    TypeInformation.of(Long.class),
                    TypeInformation.of(new TypeHint<Map<String, OrderBookSnapshot>>() {})
                );
            snapshotBuffer = getRuntimeContext().getMapState(descriptor);
        }

        @Override
        public void processElement(OrderBookSnapshot snapshot, Context ctx, Collector<String> out) throws Exception {
            long currentWatermark = ctx.timerService().currentWatermark();
            
            logger.debug("接收快照: 合约={}, 时间戳={}, 水位线={}", 
                snapshot.getContract_cde(), snapshot.getTimestamp(), currentWatermark);
            
            // 计算窗口结束时间（向上取整到最近的500ms边界）
            long windowEnd = ((snapshot.getTimestamp() / SNAPSHOT_INTERVAL_MS) + 1) * SNAPSHOT_INTERVAL_MS;

            Map<String, OrderBookSnapshot> buffer = snapshotBuffer.get(windowEnd);
            if (buffer == null) {
                buffer = new HashMap<>();
            }
            buffer.put(snapshot.getContract_cde(), snapshot);
            snapshotBuffer.put(windowEnd, buffer);
            
            logger.debug("缓存快照到窗口: {}, 当前合约数: {}", 
                windowEnd, buffer.size());

            // 注册事件时间定时器（窗口结束时触发）
            ctx.timerService().registerEventTimeTimer(windowEnd);
            
            // Debug信息
            if (currentWatermark != lastWatermark) {
                logger.debug("水位线更新: {} -> {}", 
                    lastWatermark, currentWatermark);
                lastWatermark = currentWatermark;
            }
        }

        @Override
        public void onTimer(long timestamp, OnTimerContext ctx, Collector<String> out) throws Exception {
            long currentWatermark = ctx.timerService().currentWatermark();
            
            logger.debug("定时器触发: 时间戳={}, 水位线={}", 
                timestamp, currentWatermark);
            
            // 输出对应窗口的快照
            Map<String, OrderBookSnapshot> finalSnapshots = snapshotBuffer.get(timestamp);
            
            if (finalSnapshots != null && !finalSnapshots.isEmpty()) {
                logger.info("输出全局快照: 窗口时间={}, 合约数={}", 
                    timestamp, finalSnapshots.size());
                    
                outputGlobalSnapshot(finalSnapshots, timestamp, out);
                snapshotBuffer.remove(timestamp);
            } else {
                logger.debug("窗口 {} 无快照数据", timestamp);
            }
        }
        
        /**
         * 输出全局快照
         */
        private void outputGlobalSnapshot(Map<String, OrderBookSnapshot> finalSnapshots, long windowEnd, 
                                        Collector<String> out) {
            StringBuilder sb = new StringBuilder();
            sb.append(String.format("\n=============== 全局订单簿快照 (事件时间: %s) ===============\n",
                    java.time.LocalDateTime.ofInstant(java.time.Instant.ofEpochMilli(windowEnd), 
                    java.time.ZoneId.systemDefault()).format(java.time.format.DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss.SSS"))));

            List<String> sortedContracts = new ArrayList<>(finalSnapshots.keySet());
            Collections.sort(sortedContracts);

            for (String contract : sortedContracts) {
                OrderBookSnapshot snapshot = finalSnapshots.get(contract);
                sb.append(String.format("--- 合约订单簿: %s ---\n", contract));
                
                Map<Double, Long> sortedAsks = new TreeMap<>(snapshot.getAsks());
                Map<Double, Long> sortedBids = new TreeMap<>(Collections.reverseOrder());
                sortedBids.putAll(snapshot.getBids());

                sb.append(String.format("  卖盘 (%d):\n", sortedAsks.size()));
                sortedAsks.forEach((price, volume) -> sb.append(String.format("    %-10.2f -> %d\n", price, volume)));

                sb.append(String.format("  买盘 (%d):\n", sortedBids.size()));
                sortedBids.forEach((price, volume) -> sb.append(String.format("    %-10.2f -> %d\n", price, volume)));
            }
            sb.append("=====================================================================\n");
            out.collect(sb.toString());
        }
    }

    /**
     * 自定义JSON文件数据源，用于读取JSON数组文件并发出单个JSON对象
     * 支持基于事件时间的流式模拟
     */
    public static class JsonFileSource implements SourceFunction<String> {
        private static final Logger logger = LoggerFactory.getLogger(JsonFileSource.class);
        private final String filePath;
        private volatile boolean isRunning = true;
        private static final long STREAM_DELAY_MS = 800; // 增加流式延迟以跨越多个0.5s窗口

        public JsonFileSource(String filePath) {
            this.filePath = filePath;
        }

        @Override
        public void run(SourceContext<String> ctx) throws Exception {
            try {
                logger.info("开始读取文件: {}", filePath);
                
                // Read the entire file content
                StringBuilder content = new StringBuilder();
                try (java.io.BufferedReader reader = new java.io.BufferedReader(new java.io.FileReader(filePath))) {
                    String line;
                    while ((line = reader.readLine()) != null && isRunning) {
                        content.append(line).append("\n");
                    }
                }

                // Parse JSON array and emit individual objects
                String jsonContent = content.toString().trim();
                if (jsonContent.startsWith("[") && jsonContent.endsWith("]")) {
                    // Remove array brackets
                    jsonContent = jsonContent.substring(1, jsonContent.length() - 1);

                    // Split by objects (simple approach)
                    List<String> jsonObjects = extractJsonObjectsFromArray(jsonContent);
                    
                    // 解析并按事件时间排序数据
                    List<TimestampedRecord> timestampedRecords = new ArrayList<>();
                    for (String jsonObject : jsonObjects) {
                        if (!jsonObject.trim().isEmpty()) {
                            long eventTime = extractEventTime(jsonObject);
                            timestampedRecords.add(new TimestampedRecord(eventTime, jsonObject));
                        }
                    }
                    
                    // 按事件时间排序
                    timestampedRecords.sort(Comparator.comparingLong(TimestampedRecord::getEventTime));
                    
                    logger.info("文件 {}: 共读取 {} 条记录，开始流式发送", 
                        filePath, timestampedRecords.size());

                    // 模拟流式发送数据 - 确保跨越更多的0.5秒窗口
                    long startTime = System.currentTimeMillis();
                    int recordCount = timestampedRecords.size();
                    int batchSize = Math.max(1, recordCount / 10); // 将数据分为10批，每批间隔0.6秒
                    
                    for (int i = 0; i < timestampedRecords.size(); i++) {
                        if (!isRunning) break;
                        
                        TimestampedRecord record = timestampedRecords.get(i);
                        
                        synchronized (ctx.getCheckpointLock()) {
                            ctx.collect(record.getJsonData());
                        }
                        
                        // 每处理一定数量的记录后，等待以跨越0.5秒窗口边界
                        if ((i + 1) % batchSize == 0 || i == timestampedRecords.size() - 1) {
                            logger.info("文件 {}: 已发送 {}/{} 条记录", 
                                filePath, i + 1, recordCount);
                            Thread.sleep(600); // 等待0.6秒，确保跨越0.5秒窗口边界
                        } else {
                            Thread.sleep(STREAM_DELAY_MS / 4); // 减少批内记录间的延迟
                        }
                    }
                    
                    long totalTime = System.currentTimeMillis() - startTime;
                    logger.info("文件 {}: 数据发送完成，耗时 {} ms", 
                        filePath, totalTime);
                }
            } catch (Exception e) {
                System.err.println("读取JSON文件出错: " + filePath + ", 错误: " + e.getMessage());
                e.printStackTrace();
            }
        }

        /**
         * 从JSON记录中提取事件时间
         */
        private long extractEventTime(String jsonStr) {
            try {
                Map<String, Object> jsonMap = objectMapper.readValue(jsonStr, Map.class);
                
                // 尝试解析订单时间
                if (jsonMap.containsKey("ord_dt") && jsonMap.containsKey("ord_tm")) {
                    String ordDt = getString(jsonMap, "ord_dt");
                    String ordTm = getString(jsonMap, "ord_tm");
                    long ordTmMillisec = getLong(jsonMap, "ord_tm_millisec");
                    return parseTimestampWithMillisec(ordDt, ordTm, ordTmMillisec);
                }
                
                // 尝试解析交易时间
                if (jsonMap.containsKey("trd_dt") && jsonMap.containsKey("trd_tm")) {
                    String trdDt = getString(jsonMap, "trd_dt");
                    String trdTm = getString(jsonMap, "trd_tm");
                    long trdTmMillisec = getLong(jsonMap, "trd_tm_millisec");
                    return parseTimestampWithMillisec(trdDt, trdTm, trdTmMillisec);
                }
                
                return System.currentTimeMillis();
            } catch (Exception e) {
                return System.currentTimeMillis();
            }
        }

        @Override
        public void cancel() {
            isRunning = false;
        }

        /**
         * 带时间戳的记录类
         */
        private static class TimestampedRecord {
            private final long eventTime;
            private final String jsonData;
            
            public TimestampedRecord(long eventTime, String jsonData) {
                this.eventTime = eventTime;
                this.jsonData = jsonData;
            }
            
            public long getEventTime() {
                return eventTime;
            }
            
            public String getJsonData() {
                return jsonData;
            }
        }

        private List<String> extractJsonObjectsFromArray(String content) {
            List<String> objects = new ArrayList<>();
            int braceCount = 0;
            int start = -1;
            boolean inString = false;
            boolean escaped = false;

            for (int i = 0; i < content.length(); i++) {
                char c = content.charAt(i);

                if (escaped) {
                    escaped = false;
                    continue;
                }

                if (c == '\\' && inString) {
                    escaped = true;
                    continue;
                }

                if (c == '"') {
                    inString = !inString;
                    continue;
                }

                if (!inString) {
                    if (c == '{') {
                        if (braceCount == 0) {
                            start = i;
                        }
                        braceCount++;
                    } else if (c == '}') {
                        braceCount--;
                        if (braceCount == 0 && start != -1) {
                            objects.add(content.substring(start, i + 1));
                            start = -1;
                        }
                    }
                }
            }

            return objects;
        }
    }
}
