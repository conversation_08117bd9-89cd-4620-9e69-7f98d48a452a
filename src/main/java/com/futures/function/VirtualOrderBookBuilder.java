package com.futures.function;

import com.futures.pojo.BBO_Update;
import com.futures.pojo.CombinationOrderEvent;
import com.futures.pojo.VirtualOrderBook;
import org.apache.flink.api.common.state.BroadcastState;
import org.apache.flink.api.common.state.MapState;
import org.apache.flink.api.common.state.MapStateDescriptor;
import org.apache.flink.api.common.state.ReadOnlyBroadcastState;
import org.apache.flink.configuration.Configuration;
import org.apache.flink.streaming.api.functions.co.KeyedBroadcastProcessFunction;
import org.apache.flink.util.Collector;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Collections;
import java.util.Map;
import java.util.TreeMap;

/**
 * 虚拟层订单簿构建器
 * 使用KeyedBroadcastProcessFunction处理组合订单和BBO广播状态
 * 
 * 核心逻辑：
 * 1. 接收BBO广播流，更新广播状态
 * 2. 接收组合订单流，查询广播状态中的BBO
 * 3. 根据BBO计算组合订单的虚拟挂单价格
 * 4. 构建虚拟层订单簿
 */
public class VirtualOrderBookBuilder extends KeyedBroadcastProcessFunction<String, CombinationOrderEvent, BBO_Update, VirtualOrderBook> {
    private static final Logger logger = LoggerFactory.getLogger(VirtualOrderBookBuilder.class);
    
    // 广播状态描述符 - 必须与广播端保持一致
    public static final MapStateDescriptor<String, BBO_Update> BBO_STATE_DESCRIPTOR = 
        new MapStateDescriptor<>("bbo-broadcast-state", String.class, BBO_Update.class);
    
    // Keyed State: 存储当前组合合约的活跃委托
    private transient MapState<String, CombinationOrderEvent> activeComboOrdersState;
    
    @Override
    public void open(Configuration parameters) throws Exception {
        super.open(parameters);
        
        // 初始化Keyed State
        MapStateDescriptor<String, CombinationOrderEvent> ordersDescriptor = 
            new MapStateDescriptor<>("active-combo-orders", String.class, CombinationOrderEvent.class);
        activeComboOrdersState = getRuntimeContext().getMapState(ordersDescriptor);
        
        logger.info("VirtualOrderBookBuilder 初始化完成");
    }
    
    /**
     * 处理BBO广播流的更新
     */
    @Override
    public void processBroadcastElement(BBO_Update bbo, Context ctx, Collector<VirtualOrderBook> out) throws Exception {
        logger.debug("[虚拟层-广播] 接收BBO更新: {}", bbo);
        
        // 将最新的BBO更新到广播状态中
        BroadcastState<String, BBO_Update> bboState = ctx.getBroadcastState(BBO_STATE_DESCRIPTOR);
        bboState.put(bbo.getInstrumentId(), bbo);

        System.out.println("*** [虚拟层-广播] BBO状态已更新: 合约=" + bbo.getInstrumentId() + ", BBO=" + bbo);
        logger.debug("[虚拟层-广播] BBO状态已更新: 合约={}, BBO={}", bbo.getInstrumentId(), bbo);
        
        // 注意：这里不直接触发计算，因为一次BBO更新可能影响多个组合合约
        // 计算的触发点在processElement中，或者可以通过定时器周期性触发
    }
    
    /**
     * 处理组合委托流的事件
     */
    @Override
    public void processElement(CombinationOrderEvent comboOrder, ReadOnlyContext ctx, Collector<VirtualOrderBook> out) throws Exception {
        String comboContractKey = ctx.getCurrentKey(); // 通常是腿1合约代码
        String orderNumber = comboOrder.getOrd_nbr();
        String orderStatusStr = comboOrder.getOrd_sts();
        
        logger.debug("[虚拟层] 处理组合订单: 合约键={}, 订单号={}, 状态={}, 剩余量={}",
                comboContractKey, orderNumber, orderStatusStr, comboOrder.getRmn_vol());
        
        // 验证订单状态
        if (orderStatusStr == null || orderStatusStr.isEmpty() || "nan".equalsIgnoreCase(orderStatusStr)) {
            logger.warn("[虚拟层] 组合订单状态无效，跳过处理: 订单号={}", orderNumber);
            return;
        }
        
        int orderStatus;
        try {
            orderStatus = (int) Double.parseDouble(orderStatusStr);
        } catch (NumberFormatException e) {
            logger.warn("[虚拟层] 组合订单状态解析失败，跳过处理: 订单号={}, 状态={}", orderNumber, orderStatusStr);
            return;
        }
        
        // 更新组合订单状态
        updateComboOrderState(comboOrder, orderStatus);
        
        // 从广播状态中获取依赖的腿的BBO
        ReadOnlyBroadcastState<String, BBO_Update> bboState = ctx.getBroadcastState(BBO_STATE_DESCRIPTOR);
        BBO_Update leg1_bbo = bboState.get(comboOrder.getLeg_1_contract_cde());
        BBO_Update leg2_bbo = bboState.get(comboOrder.getLeg_2_contract_cde());
        
        if (leg1_bbo == null || leg2_bbo == null) {
            logger.debug("[虚拟层] BBO数据不完整，暂时跳过虚拟挂单计算: 订单号={}, leg1_bbo={}, leg2_bbo={}",
                    orderNumber, leg1_bbo != null, leg2_bbo != null);
            // 仍然输出空的虚拟订单簿，表示当前状态
            VirtualOrderBook emptyBook = new VirtualOrderBook(comboContractKey);
            emptyBook.setSourceComboContract(comboOrder.getContract_cde());
            out.collect(emptyBook);
            return;
        }
        
        // 构建虚拟订单簿
        VirtualOrderBook virtualBook = buildVirtualOrderBook(comboContractKey, comboOrder.getContract_cde(), leg1_bbo, leg2_bbo);
        out.collect(virtualBook);

        System.out.println("*** [虚拟层] 虚拟订单簿构建完成: 合约键=" + comboContractKey +
                ", 虚拟买盘" + virtualBook.getVirtualBidDepth() + "档, 虚拟卖盘" + virtualBook.getVirtualAskDepth() + "档");
        logger.debug("[虚拟层] 虚拟订单簿构建完成: 合约键={}, 虚拟买盘{}档, 虚拟卖盘{}档",
                comboContractKey, virtualBook.getVirtualBidDepth(), virtualBook.getVirtualAskDepth());
    }
    
    /**
     * 更新组合订单状态
     */
    private void updateComboOrderState(CombinationOrderEvent comboOrder, int orderStatus) throws Exception {
        String orderNumber = comboOrder.getOrd_nbr();
        
        switch (orderStatus) {
            case 0: // 全部成交
            case 5: // 已撤销
                // 从活跃订单中移除
                activeComboOrdersState.remove(orderNumber);
                logger.debug("[虚拟层] 移除组合订单: 订单号={}, 状态={}", orderNumber, orderStatus);
                break;
                
            case 1: // 部分成交在队列中
            case 3: // 未成交在队列中
                // 更新或添加到活跃订单中
                if (comboOrder.getRmn_vol() > 0) {
                    activeComboOrdersState.put(orderNumber, comboOrder);
                    logger.debug("[虚拟层] 更新活跃组合订单: 订单号={}, 剩余量={}", orderNumber, comboOrder.getRmn_vol());
                } else {
                    // 剩余量为0，移除订单
                    activeComboOrdersState.remove(orderNumber);
                    logger.debug("[虚拟层] 剩余量为0，移除组合订单: 订单号={}", orderNumber);
                }
                break;
                
            default:
                logger.warn("[虚拟层] 未知组合订单状态: 订单号={}, 状态={}", orderNumber, orderStatus);
                break;
        }
    }
    
    /**
     * 构建虚拟订单簿
     */
    private VirtualOrderBook buildVirtualOrderBook(String instrumentId, String sourceComboContract, 
                                                  BBO_Update leg1_bbo, BBO_Update leg2_bbo) throws Exception {
        VirtualOrderBook virtualBook = new VirtualOrderBook(instrumentId);
        virtualBook.setSourceComboContract(sourceComboContract);
        
        Map<Double, Long> virtualBids = new TreeMap<>(Collections.reverseOrder());
        Map<Double, Long> virtualAsks = new TreeMap<>();
        
        int virtualBidOrderCount = 0;
        int virtualAskOrderCount = 0;
        
        // 遍历当前合约键下的所有活跃组合委托
        for (CombinationOrderEvent order : activeComboOrdersState.values()) {
            if (order.getRmn_vol() <= 0) {
                continue; // 跳过无剩余量的订单
            }
            
            // 核心定价逻辑：根据BBO和组合订单价差计算虚拟挂单价格
            double virtualPrice = calculateVirtualPrice(order, leg1_bbo, leg2_bbo);
            if (virtualPrice <= 0) {
                logger.debug("[虚拟层] 无法计算有效虚拟价格，跳过订单: 订单号={}", order.getOrd_nbr());
                continue;
            }
            
            long volume = order.getRmn_vol();
            String buySellTag = order.getB_s_tag();
            
            if ("B".equalsIgnoreCase(buySellTag)) {
                // 虚拟买单
                virtualBids.merge(virtualPrice, volume, Long::sum);
                virtualBidOrderCount++;
            } else if ("S".equalsIgnoreCase(buySellTag)) {
                // 虚拟卖单
                virtualAsks.merge(virtualPrice, volume, Long::sum);
                virtualAskOrderCount++;
            } else {
                logger.warn("[虚拟层] 未知买卖方向: 订单号={}, 方向={}", order.getOrd_nbr(), buySellTag);
            }
        }
        
        virtualBook.setVirtualBids(virtualBids);
        virtualBook.setVirtualAsks(virtualAsks);
        virtualBook.setTotalVirtualBidOrders(virtualBidOrderCount);
        virtualBook.setTotalVirtualAskOrders(virtualAskOrderCount);
        virtualBook.setTimestamp(System.currentTimeMillis());
        
        logger.debug("[虚拟层] 构建虚拟订单簿完成: 合约={}, 虚拟买单{}个({}档), 虚拟卖单{}个({}档)",
                instrumentId, virtualBidOrderCount, virtualBids.size(), virtualAskOrderCount, virtualAsks.size());
        
        return virtualBook;
    }
    
    /**
     * 计算虚拟挂单价格
     * 这里使用简化的定价逻辑：基于价差和BBO的中间价
     * 实际业务中可能需要更复杂的定价模型
     */
    private double calculateVirtualPrice(CombinationOrderEvent order, BBO_Update leg1_bbo, BBO_Update leg2_bbo) {
        if (!leg1_bbo.isValid() || !leg2_bbo.isValid()) {
            return 0.0;
        }
        
        // 简化定价逻辑：使用价差 + 腿1的中间价
        double leg1MidPrice = leg1_bbo.getMidPrice();
        double spreadPrice = order.getOrd_prc(); // 组合订单的价差
        
        // 根据买卖方向调整价格
        if ("B".equalsIgnoreCase(order.getB_s_tag())) {
            // 买单：使用稍低的价格
            return leg1MidPrice + spreadPrice - 0.5; // 减少0.5作为买单的保守定价
        } else {
            // 卖单：使用稍高的价格
            return leg1MidPrice + spreadPrice + 0.5; // 增加0.5作为卖单的保守定价
        }
    }
}
