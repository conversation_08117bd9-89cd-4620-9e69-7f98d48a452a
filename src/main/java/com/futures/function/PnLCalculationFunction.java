package com.futures.function;

import com.futures.pojo.PnLResult;
import com.futures.pojo.Position;
import com.futures.pojo.TradeEvent;
import org.apache.flink.api.common.state.*;
import org.apache.flink.api.common.typeinfo.TypeHint;
import org.apache.flink.api.common.typeinfo.TypeInformation;
import org.apache.flink.configuration.Configuration;
import org.apache.flink.streaming.api.functions.KeyedProcessFunction;
import org.apache.flink.util.Collector;
import org.apache.flink.util.OutputTag;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;

/**
 * 期货PnL计算算子 - 改进版本
 * 按结算会员计算每日盈亏，支持持仓跟踪和每日结算
 * 
 * 主要改进：
 * 1. 实现先进先出（FIFO）平仓原则
 * 2. 增加异常交易处理的侧输出流
 * 3. 支持跨日结算和持仓盈亏计算
 * 4. 增强风险控制和数据完整性验证
 */
public class PnLCalculationFunction extends KeyedProcessFunction<String, TradeEvent, PnLResult> {
    private static final Logger logger = LoggerFactory.getLogger(PnLCalculationFunction.class);

    // 异常交易输出标签 - 用于处理无持仓的平仓交易
    public static final OutputTag<TradeEvent> ANOMALY_TRADE_OUTPUT_TAG = 
        new OutputTag<TradeEvent>("anomaly-trades") {};
        
    // 持仓盈亏输出标签 - 用于输出每日结算的持仓盈亏
    public static final OutputTag<PnLResult> MTM_PNL_OUTPUT_TAG = 
        new OutputTag<PnLResult>("mark-to-market-pnl") {};

    private transient ValueState<Double> totalPnL;
    // 按合约管理持仓：合约代码 -> 持仓信息列表（按开仓时间排序，实现FIFO）
    private transient MapState<String, LinkedList<Position>> openPositionsByContract;
    // 已处理交易编号集合，防重复处理
    private transient ValueState<Set<String>> processedTradeNumbers;
    // 每日结算价格缓存：合约代码 -> 结算价格
    private transient MapState<String, Double> dailySettlementPrices;
    // 当前交易日期状态，用于跨日结算
    private transient ValueState<String> currentTradingDate;
    // 当日已实现盈亏，用于日终结算
    private transient ValueState<Double> dailyRealizedPnL;

    @Override
    public void open(Configuration parameters) throws Exception {
        totalPnL = getRuntimeContext().getState(
                new ValueStateDescriptor<>("total-pnl", Double.class, 0.0));

        openPositionsByContract = getRuntimeContext().getMapState(
                new MapStateDescriptor<>("open-positions-by-contract",
                                         TypeInformation.of(String.class),
                                         TypeInformation.of(new TypeHint<LinkedList<Position>>() {})));

        processedTradeNumbers = getRuntimeContext().getState(
                new ValueStateDescriptor<>("processed-trades", 
                                         (Class<Set<String>>) (Class<?>) Set.class, 
                                         new HashSet<>()));

        dailySettlementPrices = getRuntimeContext().getMapState(
                new MapStateDescriptor<>("daily-settlement-prices",
                                         TypeInformation.of(String.class),
                                         TypeInformation.of(Double.class)));
                                         
        currentTradingDate = getRuntimeContext().getState(
                new ValueStateDescriptor<>("current-trading-date", String.class, ""));
                
        dailyRealizedPnL = getRuntimeContext().getState(
                new ValueStateDescriptor<>("daily-realized-pnl", Double.class, 0.0));
    }

        @Override
    public void processElement(TradeEvent trade, Context ctx, Collector<PnLResult> out) throws Exception {
        String tradeNumber = trade.getTrd_nbr();
        Set<String> processedTrades = processedTradeNumbers.value();
        
        // 注意：每笔成交都有买卖双方两条记录，同一个trd_nbr对应买方和卖方
        // 我们需要区分买卖方，避免重复处理同一方的交易
        String uniqueTradeKey = tradeNumber + "_" + trade.getSettle_memb_memb_cde() + "_" + trade.getB_s_tag();
        
        // 防重复处理同一笔交易的同一方
        if (processedTrades.contains(uniqueTradeKey)) {
            logger.debug("跳过已处理的交易: 会员={}, 交易号={}, 方向={}", 
                ctx.getCurrentKey(), tradeNumber, trade.getB_s_tag());
            return;
        }
        processedTrades.add(uniqueTradeKey);
        processedTradeNumbers.update(processedTrades);

        // 检查是否需要进行跨日结算
        String tradeDate = trade.getTrd_dt();
        String currentDate = currentTradingDate.value();
        if (!tradeDate.equals(currentDate)) {
            // 执行日终结算
            performEndOfDaySettlement(currentDate, ctx, out);
            // 更新当前交易日期
            currentTradingDate.update(tradeDate);
            // 重置当日已实现盈亏
            dailyRealizedPnL.update(0.0);
        }

        String ocposType = trade.getOcpos_type();
        String contractCode = trade.getContract_cde();
        String tradeType = trade.getTrd_type();
        
        logger.info("处理交易: 结算会员={}, 合约={}, 交易号={}, 开平={}, 买卖={}, 价格={}, 数量={}, 交易类型={}", 
            ctx.getCurrentKey(), contractCode, tradeNumber, ocposType, trade.getB_s_tag(), trade.getTrd_prc(), trade.getTrd_vol(), tradeType);

        // 所有交易都有买卖双方记录，组合委托(trd_type="2")只是标识交易类型，处理逻辑相同
        // 重点关注：开仓平仓类型(ocpos_type) + 买卖方向(b_s_tag) + 会员信息 + 成交价格数量
        if ("2".equals(tradeType)) {
            logger.info("组合委托成交，注意多轮成交情况: 交易号={}, 合约={}, 会员={}", 
                tradeNumber, contractCode, ctx.getCurrentKey());
        }

        if ("0".equals(ocposType)) {
            // 开仓交易
            processOpeningTrade(trade);
        } else if ("1".equals(ocposType) || "3".equals(ocposType) || "4".equals(ocposType)) {
            // 平仓交易
            double pnl = processClosingTrade(trade, ctx);
            if (pnl != 0) {
                double currentPnL = totalPnL.value();
                double newTotalPnL = currentPnL + pnl;
                totalPnL.update(newTotalPnL);
                
                // 更新当日已实现盈亏
                double currentDailyPnL = dailyRealizedPnL.value();
                dailyRealizedPnL.update(currentDailyPnL + pnl);

                // 输出盈亏结果
                PnLResult result = new PnLResult(ctx.getCurrentKey(), newTotalPnL, trade.getTrd_dt());
                result.setTimestamp(System.currentTimeMillis());
                out.collect(result);
                
                logger.info("结算会员={} 平仓盈亏={}, 累计盈亏={}, 当日已实现盈亏={}", 
                    ctx.getCurrentKey(), pnl, newTotalPnL, currentDailyPnL + pnl);
            }
        }
    }

    /**
     * 处理开仓交易 - 按时间顺序添加到持仓列表（FIFO队列）
     */
    private void processOpeningTrade(TradeEvent trade) throws Exception {
        String contractCode = trade.getContract_cde();
        Position newPosition = new Position(contractCode, trade.getB_s_tag(), trade.getTrd_prc(), trade.getTrd_vol());
        // 设置开仓时间戳，用于FIFO排序
        newPosition.setOpenTimestamp(trade.getEventTimestamp());
        
        LinkedList<Position> positions = openPositionsByContract.get(contractCode);
        if (positions == null) {
            positions = new LinkedList<>();
        }
        // 添加到队列末尾，保证FIFO顺序
        positions.addLast(newPosition);
        openPositionsByContract.put(contractCode, positions);
        
        logger.info("开仓(FIFO): 合约={}, 方向={}, 价格={}, 数量={}, 时间戳={}", 
            contractCode, trade.getB_s_tag(), trade.getTrd_prc(), trade.getTrd_vol(), newPosition.getOpenTimestamp());
    }

    /**
     * 处理平仓交易并计算盈亏 - 实现先进先出（FIFO）平仓原则
     */
    private double processClosingTrade(TradeEvent closingTrade, Context ctx) throws Exception {
        String contractCode = closingTrade.getContract_cde();
        long remainingVolumeToClose = closingTrade.getTrd_vol();
        double totalPnl = 0.0;

        LinkedList<Position> positions = openPositionsByContract.get(contractCode);
        if (positions == null || positions.isEmpty()) {
            logger.warn("发现无持仓的平仓交易 - 发送到异常流: 交易号={}, 合约={}, 方向={}, 数量={}", 
                closingTrade.getTrd_nbr(), contractCode, closingTrade.getB_s_tag(), remainingVolumeToClose);
            
            // 将异常交易发送到侧输出流进行专门处理
            ctx.output(ANOMALY_TRADE_OUTPUT_TAG, closingTrade);
            return 0.0;
        }

        // 使用FIFO原则：从队列头部开始匹配最早的持仓
        Iterator<Position> iterator = positions.iterator();
        while (iterator.hasNext() && remainingVolumeToClose > 0) {
            Position openPosition = iterator.next();

            // 检查是否为相反方向的持仓
            boolean isOpposite = ("B".equals(closingTrade.getB_s_tag()) && "S".equals(openPosition.getB_s_tag())) ||
                                 ("S".equals(closingTrade.getB_s_tag()) && "B".equals(openPosition.getB_s_tag()));

            if (isOpposite) {
                long matchedVolume = Math.min(remainingVolumeToClose, openPosition.getVolume());

                double pnl;
                if ("B".equals(closingTrade.getB_s_tag())) { // 买入平仓（平掉空头寸）
                    pnl = (openPosition.getOpen_price() - closingTrade.getTrd_prc()) * matchedVolume;
                } else { // 卖出平仓（平掉多头寸）
                    pnl = (closingTrade.getTrd_prc() - openPosition.getOpen_price()) * matchedVolume;
                }
                
                logger.info("FIFO匹配持仓: 开仓价={}, 时间戳={}, 平仓价={}, 数量={}, 盈亏={}", 
                    openPosition.getOpen_price(), openPosition.getOpenTimestamp(), closingTrade.getTrd_prc(), matchedVolume, pnl);
                
                totalPnl += pnl;
                remainingVolumeToClose -= matchedVolume;
                openPosition.setVolume(openPosition.getVolume() - matchedVolume);

                // 如果持仓已完全平掉，则从队列中移除
                if (openPosition.getVolume() == 0) {
                    iterator.remove();
                }
            }
        }

        // 如果平仓量仍有剩余，将超量部分发送到异常流
        if (remainingVolumeToClose > 0) {
            logger.warn("平仓量超过持仓量，发送超量交易到异常流: 合约={}, 超量={}", 
                contractCode, remainingVolumeToClose);
                
            // 创建一个代表超量部分的交易事件发送到异常流
            TradeEvent excessTrade = copyTradeEvent(closingTrade);
            excessTrade.setTrd_vol(remainingVolumeToClose);
            ctx.output(ANOMALY_TRADE_OUTPUT_TAG, excessTrade);
        }

        // 更新状态：如果列表为空，则从MapState中移除该合约条目
        if (positions.isEmpty()) {
            openPositionsByContract.remove(contractCode);
        } else {
            openPositionsByContract.put(contractCode, positions);
        }

        return totalPnl;
    }

    /**
     * 执行日终结算 - 计算持仓盈亏（Mark-to-Market PnL）
     * 
     * 触发时机：当检测到新的交易日期时，立即对前一日进行结算
     * 这样设计的原因：
     * 1. 实时流处理中无法预知当日交易何时结束
     * 2. 识别到下一天数据后立即触发前一日结算，确保及时性
     * 3. 适用于T+1结算制度，当日持仓需要按结算价重新估值
     */
    private void performEndOfDaySettlement(String settlementDate, Context ctx, Collector<PnLResult> out) throws Exception {
        if (settlementDate == null || settlementDate.isEmpty()) {
            logger.info("首个交易日，无需结算");
            return; // 第一个交易日，无需结算
        }
        
        logger.info("检测到日期变化，立即执行 {} 日终结算，会员: {}", 
            settlementDate, ctx.getCurrentKey());
            
        double totalUnrealizedPnL = 0.0;
        int totalOpenPositions = 0;
        
        // 遍历所有合约的持仓，计算持仓盈亏
        for (Map.Entry<String, LinkedList<Position>> entry : openPositionsByContract.entries()) {
            String contractCode = entry.getKey();
            LinkedList<Position> positions = entry.getValue();
            
            if (positions.isEmpty()) continue;
            
            // 获取合约的结算价
            Double settlementPrice = dailySettlementPrices.get(contractCode);
            if (settlementPrice == null) {
                logger.warn("合约 {} 缺少结算价，跳过持仓盈亏计算", contractCode);
                continue;
            }
            
            // 计算该合约所有持仓的持仓盈亏
            for (Position position : positions) {
                double unrealizedPnL;
                if ("B".equals(position.getB_s_tag())) { // 多头持仓
                    unrealizedPnL = (settlementPrice - position.getOpen_price()) * position.getVolume();
                } else { // 空头持仓
                    unrealizedPnL = (position.getOpen_price() - settlementPrice) * position.getVolume();
                }
                
                totalUnrealizedPnL += unrealizedPnL;
                totalOpenPositions += position.getVolume();
                
                logger.info("持仓盈亏: 合约={}, 方向={}, 开仓价={}, 结算价={}, 数量={}, 盈亏={}",
                    contractCode, position.getB_s_tag(), position.getOpen_price(), settlementPrice, position.getVolume(), unrealizedPnL);
            }
        }
        
        // 输出持仓盈亏到侧输出流（这会触发每日会员盈亏汇总）
        if (totalOpenPositions > 0 || dailyRealizedPnL.value() != 0.0) {
            PnLResult mtmResult = new PnLResult(ctx.getCurrentKey(), totalUnrealizedPnL, settlementDate);
            mtmResult.setTimestamp(System.currentTimeMillis());
            ctx.output(MTM_PNL_OUTPUT_TAG, mtmResult);
            
            logger.info("{} 日终结算完成 - 会员: {}, 当日已实现盈亏: {}, 持仓盈亏: {}, 持仓数量: {}",
                settlementDate, ctx.getCurrentKey(), dailyRealizedPnL.value(), totalUnrealizedPnL, totalOpenPositions);
        } else {
            logger.info("{} 会员 {} 无持仓且无已实现盈亏，跳过结算输出",
                settlementDate, ctx.getCurrentKey());
        }
    }
    
    /**
     * 复制交易事件对象（用于异常处理）
     */
    private TradeEvent copyTradeEvent(TradeEvent original) {
        TradeEvent copy = new TradeEvent();
        copy.setTrd_dt(original.getTrd_dt());
        copy.setTrd_nbr(original.getTrd_nbr() + "_EXCESS");
        copy.setOrd_nbr(original.getOrd_nbr());
        copy.setContract_cde(original.getContract_cde());
        copy.setB_s_tag(original.getB_s_tag());
        copy.setTrd_prc(original.getTrd_prc());
        copy.setTrd_vol(original.getTrd_vol()); // 将在调用方设置为超量部分
        copy.setTrd_type(original.getTrd_type());
        copy.setSettle_memb_unfy_cde(original.getSettle_memb_unfy_cde());
        copy.setSettle_memb_memb_cde(original.getSettle_memb_memb_cde());
        copy.setOcpos_type(original.getOcpos_type());
        copy.setEventTimestamp(original.getEventTimestamp());
        return copy;
    }
    
    /**
     * 设置结算价格（可以通过外部调用设置）
     */
    public void setSettlementPrice(String contractCode, double settlementPrice) throws Exception {
        dailySettlementPrices.put(contractCode, settlementPrice);
        logger.info("设置结算价: 合约={}, 价格={}", contractCode, settlementPrice);
    }
    
    /**
     * 调试方法：打印当前持仓情况
     */
    private void printCurrentPositions() throws Exception {
        logger.debug("当前持仓情况:");
        for (Map.Entry<String, LinkedList<Position>> entry : openPositionsByContract.entries()) {
            String contract = entry.getKey();
            LinkedList<Position> positions = entry.getValue();
            logger.debug("  合约 {}: {}个持仓", contract, positions.size());
            for (Position pos : positions) {
                logger.debug("    方向={}, 价格={}, 数量={}, 开仓时间={}", 
                    pos.getB_s_tag(), pos.getOpen_price(), pos.getVolume(), pos.getOpenTimestamp());
            }
        }
    }

}
