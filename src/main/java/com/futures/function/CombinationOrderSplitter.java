package com.futures.function;

import com.futures.pojo.CombinationOrderEvent;
import org.apache.flink.api.common.state.ValueState;
import org.apache.flink.api.common.state.ValueStateDescriptor;
import org.apache.flink.api.common.typeinfo.Types;
import org.apache.flink.configuration.Configuration;
import org.apache.flink.streaming.api.functions.ProcessFunction;
import org.apache.flink.util.Collector;
import org.apache.flink.util.OutputTag;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.Serializable;

/**
 * 组合订单分发器 - 将组合订单分发到两条腿的处理流中
 * 解决原有架构中只处理第一条腿的问题
 */
public class CombinationOrderSplitter extends ProcessFunction<CombinationOrderEvent, Object> {
    
    private static final Logger logger = LoggerFactory.getLogger(CombinationOrderSplitter.class);
    
    // 第一条腿输出标签
    public static final OutputTag<CombinationOrderLegInfo> LEG1_OUTPUT_TAG = 
        new OutputTag<CombinationOrderLegInfo>("leg1-output"){};
    
    // 第二条腿输出标签  
    public static final OutputTag<CombinationOrderLegInfo> LEG2_OUTPUT_TAG = 
        new OutputTag<CombinationOrderLegInfo>("leg2-output"){};
    
    // 快照时间间隔，单位：毫秒
    private static final long SNAPSHOT_INTERVAL_MS = 500L;
    
    // 定时器注册状态
    private transient ValueState<Boolean> timerRegistered;
    
    @Override
    public void open(Configuration parameters) throws Exception {
        super.open(parameters);
        // 初始化定时器注册状态
        ValueStateDescriptor<Boolean> descriptor = new ValueStateDescriptor<>(
            "timerRegistered", // 状态名称
            Types.BOOLEAN, // 状态类型
            false // 默认值
        );
        timerRegistered = getRuntimeContext().getState(descriptor);
    }
    
    @Override
    public void processElement(CombinationOrderEvent combOrder, Context ctx, Collector<Object> out) throws Exception {

        // 解析组合合约代码，提取腿信息
        String combinationContract = combOrder.getContract_cde();
        String leg1Contract = combOrder.getLeg_1_contract_cde();
        String leg2Contract = combOrder.getLeg_2_contract_cde();
        
        // 从数据中可以看到多轮成交的特征：同一订单号有多条记录，trd_vol递增，rmn_vol递减
        long tradedVolume = combOrder.getTrd_vol(); // 累计成交量
        long remainingVolume = combOrder.getRmn_vol(); // 剩余量

        // 调试日志 - 重点显示多轮成交信息
        logger.info("处理组合订单: 订单号={}, 组合合约={}, 腿1={}, 腿2={}, 状态={}, 累计成交量={}, 剩余量={}",
            combOrder.getOrd_nbr(), combinationContract, leg1Contract, leg2Contract,
            combOrder.getOrd_sts(), tradedVolume, remainingVolume);

        // 为第一条腿创建信息
        CombinationOrderLegInfo leg1Info = new CombinationOrderLegInfo(
            combOrder.getOrd_nbr(),
            leg1Contract, // 当前腿合约
            leg2Contract, // 对手腿合约
            combOrder.getB_s_tag(),
            combOrder.getOrd_prc(),
            remainingVolume, // 使用剩余量作为虚拟挂单量
            combOrder.getOrd_sts(),
            combOrder.getEventTimestamp(),
            1, // 腿序号
            tradedVolume // 添加累计成交量信息
        );

        // 为第二条腿创建信息（买卖方向相反）
        String oppositeBuySellTag = "B".equals(combOrder.getB_s_tag()) ? "S" : "B";
        CombinationOrderLegInfo leg2Info = new CombinationOrderLegInfo(
            combOrder.getOrd_nbr(),
            leg2Contract, // 当前腿合约
            leg1Contract, // 对手腿合约
            oppositeBuySellTag, // 相反方向
            -combOrder.getOrd_prc(), // 价差取负值
            remainingVolume, // 使用剩余量作为虚拟挂单量
            combOrder.getOrd_sts(),
            combOrder.getEventTimestamp(),
            2, // 腿序号
            tradedVolume // 添加累计成交量信息
        );

        // 分发到对应的侧输出流
        ctx.output(LEG1_OUTPUT_TAG, leg1Info);
        ctx.output(LEG2_OUTPUT_TAG, leg2Info);
        
        // 多轮成交监控日志
        if (tradedVolume > 0) {
            logger.info("组合订单多轮成交监控: 订单号={}, 当前轮次成交量={}, 累计成交量={}, 剩余量={}, 两腿已拆分处理",
                combOrder.getOrd_nbr(), 
                (tradedVolume > 0 && remainingVolume >= 0) ? "部分成交" : "状态变更", 
                tradedVolume, remainingVolume);
        }

        // 注册定时器生成快照
        if (!Boolean.TRUE.equals(timerRegistered.value())) {
            long currentTime = ctx.timestamp();
            if (currentTime <= 0) {
                currentTime = ctx.timerService().currentProcessingTime();
            }
            // 修改1：使用更精确的定时器注册，确保每个500ms窗口都有定时器
            long nextSnapshotTime = ((currentTime / SNAPSHOT_INTERVAL_MS) + 1) * SNAPSHOT_INTERVAL_MS;
            ctx.timerService().registerEventTimeTimer(nextSnapshotTime);
            
            // 修改2：还要注册中间的定时器，确保短生命周期订单被捕获
            if (currentTime % SNAPSHOT_INTERVAL_MS > 0) {
                // 如果当前事件不在边界上，还要注册当前窗口的定时器
                long currentWindowEnd = (currentTime / SNAPSHOT_INTERVAL_MS + 1) * SNAPSHOT_INTERVAL_MS;
                ctx.timerService().registerEventTimeTimer(currentWindowEnd);
            }
            timerRegistered.update(true);
        }

        // 第五步：检查是否需要继续注册定时器
        long currentWatermark = ctx.timerService().currentWatermark();
        boolean isDataStreamEnded = currentWatermark >= Long.MAX_VALUE;

        if (isDataStreamEnded) {
            timerRegistered.update(false);
            logger.debug("合约 {} 数据流结束，停止定时器", combinationContract);
        } else {
            long nextTimerTime = ctx.timestamp() + SNAPSHOT_INTERVAL_MS;
            ctx.timerService().registerEventTimeTimer(nextTimerTime);
            // 修改3：重置定时器注册标志，允许新事件重新触发定时器注册
            timerRegistered.update(false);
            logger.debug("合约 {} 注册下次定时器: {}", combinationContract, nextTimerTime);
        }
    }
    
    /**
     * 组合订单腿信息 - 包含处理单条腿所需的所有信息
     * 支持多轮成交追踪和虚拟层挂单管理
     */
    public static class CombinationOrderLegInfo implements Serializable {
        private static final long serialVersionUID = 1L;
        
        private String ordNbr;
        private String currentLegContract; // 当前腿合约代码
        private String oppositeLegContract; // 对手腿合约代码
        private String buySellTag;
        private double spreadPrice;
        private long remainingVol;
        private String orderStatus;
        private long eventTimestamp;
        private int legNumber; // 腿序号 1或2
        private long cumulativeTradedVol; // 累计成交量，用于多轮成交追踪
        
        public CombinationOrderLegInfo() {}
        
        public CombinationOrderLegInfo(String ordNbr, String currentLegContract, String oppositeLegContract,
                                     String buySellTag, double spreadPrice, long remainingVol, 
                                     String orderStatus, long eventTimestamp, int legNumber) {
            this(ordNbr, currentLegContract, oppositeLegContract, buySellTag, spreadPrice, 
                 remainingVol, orderStatus, eventTimestamp, legNumber, 0L);
        }
        
        public CombinationOrderLegInfo(String ordNbr, String currentLegContract, String oppositeLegContract,
                                     String buySellTag, double spreadPrice, long remainingVol, 
                                     String orderStatus, long eventTimestamp, int legNumber, long cumulativeTradedVol) {
            this.ordNbr = ordNbr;
            this.currentLegContract = currentLegContract;
            this.oppositeLegContract = oppositeLegContract;
            this.buySellTag = buySellTag;
            this.spreadPrice = spreadPrice;
            this.remainingVol = remainingVol;
            this.orderStatus = orderStatus;
            this.eventTimestamp = eventTimestamp;
            this.legNumber = legNumber;
            this.cumulativeTradedVol = cumulativeTradedVol;
        }
        
        // Getters and setters
        public String getOrdNbr() { return ordNbr; }
        public void setOrdNbr(String ordNbr) { this.ordNbr = ordNbr; }
        
        public String getCurrentLegContract() { return currentLegContract; }
        public void setCurrentLegContract(String currentLegContract) { this.currentLegContract = currentLegContract; }
        
        public String getOppositeLegContract() { return oppositeLegContract; }
        public void setOppositeLegContract(String oppositeLegContract) { this.oppositeLegContract = oppositeLegContract; }
        
        public String getBuySellTag() { return buySellTag; }
        public void setBuySellTag(String buySellTag) { this.buySellTag = buySellTag; }
        
        public double getSpreadPrice() { return spreadPrice; }
        public void setSpreadPrice(double spreadPrice) { this.spreadPrice = spreadPrice; }
        
        public long getRemainingVol() { return remainingVol; }
        public void setRemainingVol(long remainingVol) { this.remainingVol = remainingVol; }
        
        public String getOrderStatus() { return orderStatus; }
        public void setOrderStatus(String orderStatus) { this.orderStatus = orderStatus; }
        
        public long getEventTimestamp() { return eventTimestamp; }
        public void setEventTimestamp(long eventTimestamp) { this.eventTimestamp = eventTimestamp; }
        
        public int getLegNumber() { return legNumber; }
        public void setLegNumber(int legNumber) { this.legNumber = legNumber; }
        
        public long getCumulativeTradedVol() { return cumulativeTradedVol; }
        public void setCumulativeTradedVol(long cumulativeTradedVol) { this.cumulativeTradedVol = cumulativeTradedVol; }
        
        /**
         * 获取成交进度百分比
         */
        public double getTradedPercentage() {
            long totalVol = remainingVol + cumulativeTradedVol;
            return totalVol > 0 ? (double) cumulativeTradedVol / totalVol : 0.0;
        }
    }
}
