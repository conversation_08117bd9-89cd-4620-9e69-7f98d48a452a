package com.futures.function;

import com.futures.pojo.BaseOrderBook;
import com.futures.pojo.BBO_Update;
import com.futures.pojo.SingleLegOrderEvent;
import org.apache.flink.api.common.state.MapState;
import org.apache.flink.api.common.state.MapStateDescriptor;
import org.apache.flink.configuration.Configuration;
import org.apache.flink.streaming.api.functions.ProcessFunction;
import org.apache.flink.util.Collector;
import org.apache.flink.util.OutputTag;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Collections;
import java.util.Map;
import java.util.TreeMap;

/**
 * 基础层订单簿构建器
 * 处理单腿订单，生成基础层订单簿和BBO广播流
 * 
 * 订单状态说明：
 * 0 - 全部成交（从订单簿中移除）
 * 1 - 部分成交在队列中（保持在订单簿中）
 * 3 - 未成交在队列中（保持在订单簿中）
 * 5 - 已撤销（从订单簿中移除）
 */
public class BaseOrderBookBuilder extends ProcessFunction<SingleLegOrderEvent, BaseOrderBook> {
    private static final Logger logger = LoggerFactory.getLogger(BaseOrderBookBuilder.class);
    
    // BBO输出标签
    public static final OutputTag<BBO_Update> BBO_OUTPUT_TAG = new OutputTag<BBO_Update>("bbo-updates"){};
    
    // 存储活跃订单的状态
    private transient MapState<String, SingleLegOrderEvent> activeOrdersState;
    
    @Override
    public void open(Configuration parameters) throws Exception {
        super.open(parameters);
        
        // 初始化状态
        MapStateDescriptor<String, SingleLegOrderEvent> ordersDescriptor = 
            new MapStateDescriptor<>("base-active-orders", String.class, SingleLegOrderEvent.class);
        activeOrdersState = getRuntimeContext().getMapState(ordersDescriptor);
        
        logger.info("BaseOrderBookBuilder 初始化完成");
    }
    
    @Override
    public void processElement(SingleLegOrderEvent order, Context ctx, Collector<BaseOrderBook> out) throws Exception {
        String contractCode = order.getContract_cde();
        String orderNumber = order.getOrd_nbr();
        String orderStatusStr = order.getOrd_sts();
        
        logger.debug("[基础层] 处理单腿订单: 合约={}, 订单号={}, 状态={}, 剩余量={}",
                contractCode, orderNumber, orderStatusStr, order.getRmn_vol());
        
        // 验证订单状态
        if (orderStatusStr == null || orderStatusStr.isEmpty() || "nan".equalsIgnoreCase(orderStatusStr)) {
            logger.warn("[基础层] 订单状态无效，跳过处理: 订单号={}", orderNumber);
            return;
        }
        
        int orderStatus;
        try {
            orderStatus = (int) Double.parseDouble(orderStatusStr);
        } catch (NumberFormatException e) {
            logger.warn("[基础层] 订单状态解析失败，跳过处理: 订单号={}, 状态={}", orderNumber, orderStatusStr);
            return;
        }
        
        // 更新订单状态
        updateOrderState(order, orderStatus);
        
        // 构建基础层订单簿
        BaseOrderBook baseOrderBook = buildBaseOrderBook(contractCode);
        
        // 输出基础层订单簿
        out.collect(baseOrderBook);

        // 生成并输出BBO更新
        BBO_Update bboUpdate = baseOrderBook.generateBBO();
        ctx.output(BBO_OUTPUT_TAG, bboUpdate);

        System.out.println("*** [基础层] 订单簿更新完成: 合约=" + contractCode +
                ", 买盘" + baseOrderBook.getBidDepth() + "档, 卖盘" + baseOrderBook.getAskDepth() + "档" +
                ", BBO=" + bboUpdate);
        logger.debug("[基础层] 订单簿更新完成: 合约={}, 买盘{}档, 卖盘{}档, BBO={}",
                contractCode, baseOrderBook.getBidDepth(), baseOrderBook.getAskDepth(), bboUpdate);
    }
    
    /**
     * 更新订单状态
     */
    private void updateOrderState(SingleLegOrderEvent order, int orderStatus) throws Exception {
        String orderNumber = order.getOrd_nbr();
        
        switch (orderStatus) {
            case 0: // 全部成交
            case 5: // 已撤销
                // 从活跃订单中移除
                activeOrdersState.remove(orderNumber);
                logger.debug("[基础层] 移除订单: 订单号={}, 状态={}", orderNumber, orderStatus);
                break;
                
            case 1: // 部分成交在队列中
            case 3: // 未成交在队列中
                // 更新或添加到活跃订单中
                if (order.getRmn_vol() > 0) {
                    activeOrdersState.put(orderNumber, order);
                    logger.debug("[基础层] 更新活跃订单: 订单号={}, 剩余量={}", orderNumber, order.getRmn_vol());
                } else {
                    // 剩余量为0，移除订单
                    activeOrdersState.remove(orderNumber);
                    logger.debug("[基础层] 剩余量为0，移除订单: 订单号={}", orderNumber);
                }
                break;
                
            default:
                logger.warn("[基础层] 未知订单状态: 订单号={}, 状态={}", orderNumber, orderStatus);
                break;
        }
    }
    
    /**
     * 构建基础层订单簿
     */
    private BaseOrderBook buildBaseOrderBook(String contractCode) throws Exception {
        BaseOrderBook orderBook = new BaseOrderBook(contractCode);
        
        Map<Double, Long> bids = new TreeMap<>(Collections.reverseOrder()); // 买盘降序
        Map<Double, Long> asks = new TreeMap<>(); // 卖盘升序
        
        int bidOrderCount = 0;
        int askOrderCount = 0;
        
        // 遍历所有活跃订单
        for (SingleLegOrderEvent order : activeOrdersState.values()) {
            if (order.getRmn_vol() <= 0) {
                continue; // 跳过无剩余量的订单
            }
            
            double price = order.getOrd_prc();
            long volume = order.getRmn_vol();
            String buySellTag = order.getB_s_tag();
            
            if ("B".equalsIgnoreCase(buySellTag)) {
                // 买单
                bids.merge(price, volume, Long::sum);
                bidOrderCount++;
            } else if ("S".equalsIgnoreCase(buySellTag)) {
                // 卖单
                asks.merge(price, volume, Long::sum);
                askOrderCount++;
            } else {
                logger.warn("[基础层] 未知买卖方向: 订单号={}, 方向={}", order.getOrd_nbr(), buySellTag);
            }
        }
        
        orderBook.setBids(bids);
        orderBook.setAsks(asks);
        orderBook.setTotalBidOrders(bidOrderCount);
        orderBook.setTotalAskOrders(askOrderCount);
        orderBook.setTimestamp(System.currentTimeMillis());
        
        logger.debug("[基础层] 构建订单簿完成: 合约={}, 买单{}个({}档), 卖单{}个({}档)",
                contractCode, bidOrderCount, bids.size(), askOrderCount, asks.size());
        
        return orderBook;
    }
}
