package com.futures.function;

import com.futures.pojo.OrderBookSnapshot;
import com.futures.pojo.SingleLegOrderEvent;
import com.futures.pojo.CombinationOrderEvent;
import com.futures.function.CombinationOrderSplitter.CombinationOrderLegInfo;
import org.apache.flink.api.common.state.*;
import org.apache.flink.api.common.time.Time;
import org.apache.flink.configuration.Configuration;
import org.apache.flink.streaming.api.functions.KeyedProcessFunction;
import org.apache.flink.util.Collector;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import java.util.*;

/**
 * 组合订单状态追踪类
 * 用于管理组合订单的多轮成交状态和虚拟挂单逻辑
 */
class CombinationOrderState implements java.io.Serializable {
    private static final long serialVersionUID = 1L;
    
    private String ordNbr;
    private String leg1Contract;
    private String leg2Contract;
    private String buySellTag;
    private double orderPrice;
    private long originalVol; // 原始委托量
    private long currentRemainingVol; // 当前剩余量
    private long totalTradedVol; // 累计成交量
    private String currentStatus; // 当前状态
    private int roundCount; // 成交轮次计数
    private long lastUpdateTimestamp; // 最后更新时间
    
    // 虚拟挂单配置
    private boolean enableVirtualOrders = true; // 是否启用虚拟挂单
    private double virtualOrderSpread = 0.5; // 虚拟挂单价差调整
    
    public CombinationOrderState(String ordNbr, String leg1Contract, String leg2Contract,
                               String buySellTag, double orderPrice, long originalVol) {
        this.ordNbr = ordNbr;
        this.leg1Contract = leg1Contract;
        this.leg2Contract = leg2Contract;
        this.buySellTag = buySellTag;
        this.orderPrice = orderPrice;
        this.originalVol = originalVol;
        this.currentRemainingVol = originalVol;
        this.totalTradedVol = 0;
        this.currentStatus = "3"; // 初始状态为未成交在队列中
        this.roundCount = 0;
        this.lastUpdateTimestamp = System.currentTimeMillis();
    }
    
    /**
     * 更新组合订单状态
     * @param newStatus 新状态
     * @param tradedVol 本轮成交量
     * @param remainingVol 剩余量
     */
    public void updateState(String newStatus, long tradedVol, long remainingVol) {
        if (tradedVol > 0) {
            this.roundCount++;
            this.totalTradedVol += tradedVol;
        }
        this.currentStatus = newStatus;
        this.currentRemainingVol = remainingVol;
        this.lastUpdateTimestamp = System.currentTimeMillis();
    }
    
    /**
     * 判断是否应该生成虚拟挂单
     * 当订单仍在队列中且剩余量大于0时，生成虚拟挂单
     */
    public boolean shouldGenerateVirtualOrders() {
        return enableVirtualOrders && 
               ("1".equals(currentStatus) || "3".equals(currentStatus)) && 
               currentRemainingVol > 0;
    }
    
    /**
     * 获取完成百分比
     */
    public double getCompletionRate() {
        if (originalVol == 0) return 0.0;
        return (double) totalTradedVol / originalVol;
    }
    
    // Getters and setters
    public String getOrdNbr() { return ordNbr; }
    public String getLeg1Contract() { return leg1Contract; }
    public String getLeg2Contract() { return leg2Contract; }
    public String getBuySellTag() { return buySellTag; }
    public double getOrderPrice() { return orderPrice; }
    public long getOriginalVol() { return originalVol; }
    public long getCurrentRemainingVol() { return currentRemainingVol; }
    public long getTotalTradedVol() { return totalTradedVol; }
    public String getCurrentStatus() { return currentStatus; }
    public int getRoundCount() { return roundCount; }
    public long getLastUpdateTimestamp() { return lastUpdateTimestamp; }
    public double getVirtualOrderSpread() { return virtualOrderSpread; }
    public void setVirtualOrderSpread(double virtualOrderSpread) { this.virtualOrderSpread = virtualOrderSpread; }
}

/**
 * 订单簿重建函数 - 支持基础层和虚拟层的分层订单簿重建
 * 基础层：处理单腿订单的真实挂单
 * 虚拟层：处理组合订单的虚拟挂单，支持多轮成交和分阶段处理
 */
public class OrderBookReconstructionFunction extends KeyedProcessFunction<String, Object, OrderBookSnapshot> {
    private static final Logger logger = LoggerFactory.getLogger(OrderBookReconstructionFunction.class);

    // 基础层状态管理 - 单腿订单
    private transient MapState<String, SingleLegOrderEvent> baseLayerOrders;

    // 虚拟层状态管理 - 组合订单腿
    private transient MapState<String, CombinationOrderLegInfo> virtualLayerOrders;

    // 组合订单多轮成交状态追踪
    private transient MapState<String, CombinationOrderState> combOrderStates;

    // 价格缓存用于虚拟层计算
    private transient ValueState<Double> bestBidPrice;
    private transient ValueState<Double> bestAskPrice;
    private transient ValueState<Boolean> timerRegistered;

    // 状态版本控制 - 记录每个订单的状态变更时间戳
    private transient MapState<String, Long> orderStateTimestamps;

    // 最后状态更新时间 - 用于快照时序控制
    private transient ValueState<Long> lastStateUpdateTime;

    private static final long SNAPSHOT_INTERVAL_MS = 500; // 0.5秒生成一次全量订单簿

    /**
     * 格式化时间戳为可读格式
     */
    private String formatTimestamp(long timestamp) {
        return java.time.LocalDateTime.ofInstant(
            java.time.Instant.ofEpochMilli(timestamp),
            java.time.ZoneId.of("Asia/Shanghai")
        ).format(java.time.format.DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss.SSS"));
    }

    @Override
    public void open(Configuration parameters) throws Exception {
        StateTtlConfig ttlConfig = StateTtlConfig
                .newBuilder(Time.hours(24))
                .setUpdateType(StateTtlConfig.UpdateType.OnCreateAndWrite)
                .setStateVisibility(StateTtlConfig.StateVisibility.NeverReturnExpired)
                .build();

        // 初始化基础层状态 - 单腿订单
        MapStateDescriptor<String, SingleLegOrderEvent> baseLayerDescriptor =
                new MapStateDescriptor<>("base-layer-orders", String.class, SingleLegOrderEvent.class);
        baseLayerDescriptor.enableTimeToLive(ttlConfig);
        baseLayerOrders = getRuntimeContext().getMapState(baseLayerDescriptor);

        // 初始化虚拟层状态 - 组合订单腿
        MapStateDescriptor<String, CombinationOrderLegInfo> virtualLayerDescriptor =
                new MapStateDescriptor<>("virtual-layer-orders", String.class, CombinationOrderLegInfo.class);
        virtualLayerDescriptor.enableTimeToLive(ttlConfig);
        virtualLayerOrders = getRuntimeContext().getMapState(virtualLayerDescriptor);
        
        // 初始化组合订单状态追踪
        MapStateDescriptor<String, CombinationOrderState> combStateDescriptor =
                new MapStateDescriptor<>("comb-order-states", String.class, CombinationOrderState.class);
        combStateDescriptor.enableTimeToLive(ttlConfig);
        combOrderStates = getRuntimeContext().getMapState(combStateDescriptor);

        // 初始化价格缓存状态
        ValueStateDescriptor<Double> bestBidDescriptor =
                new ValueStateDescriptor<>("best-bid-price", Double.class);
        bestBidDescriptor.enableTimeToLive(ttlConfig);
        bestBidPrice = getRuntimeContext().getState(bestBidDescriptor);

        ValueStateDescriptor<Double> bestAskDescriptor =
                new ValueStateDescriptor<>("best-ask-price", Double.class);
        bestAskDescriptor.enableTimeToLive(ttlConfig);
        bestAskPrice = getRuntimeContext().getState(bestAskDescriptor);
        
        ValueStateDescriptor<Boolean> timerDescriptor =
                new ValueStateDescriptor<>("timer-registered", Boolean.class, false);
        timerRegistered = getRuntimeContext().getState(timerDescriptor);

        // 初始化状态版本控制
        MapStateDescriptor<String, Long> timestampDescriptor =
                new MapStateDescriptor<>("order-state-timestamps", String.class, Long.class);
        orderStateTimestamps = getRuntimeContext().getMapState(timestampDescriptor);

        ValueStateDescriptor<Long> lastUpdateDescriptor =
                new ValueStateDescriptor<>("last-state-update-time", Long.class, 0L);
        lastStateUpdateTime = getRuntimeContext().getState(lastUpdateDescriptor);
    }

    @Override
    public void processElement(Object value, Context ctx, Collector<OrderBookSnapshot> out) throws Exception {
        logger.info("接收到事件: 类型={}", value.getClass().getSimpleName());
        
        // 强制控制台输出，确保能看到日志
        System.out.println("*** 订单簿处理: 接收到事件类型 = " + value.getClass().getSimpleName());
        
        try {
            if (value instanceof SingleLegOrderEvent) {
                logger.info("处理基础层单腿订单事件");
                SingleLegOrderEvent order = (SingleLegOrderEvent) value;
                System.out.println("*** 单腿订单: ord_nbr=" + order.getOrd_nbr() + ", contract=" + order.getContract_cde() + ", status=" + order.getOrd_sts());
                processBaseLayerOrder(order, ctx);
            } else if (value instanceof CombinationOrderEvent) {
                logger.info("处理组合订单事件，将在内部拆分为虚拟层订单");
                processCombinationOrder((CombinationOrderEvent) value);
            } else if (value instanceof CombinationOrderLegInfo) {
                logger.info("处理虚拟层组合订单腿事件");
                processVirtualLayerOrder((CombinationOrderLegInfo) value);
            }

            // 注册定时器生成快照 - 修复短生命周期订单丢失问题
            // 移除timerRegistered限制，确保每个事件都能注册足够的定时器窗口
            long currentTime = ctx.timestamp();
            if (currentTime <= 0) {
                currentTime = ctx.timerService().currentProcessingTime();
            }

            // 为可能的订单生命周期注册多个定时器窗口，确保覆盖短生命周期订单
            long startWindow = ((currentTime / SNAPSHOT_INTERVAL_MS) + 1) * SNAPSHOT_INTERVAL_MS;

            // 注册未来12个窗口（6秒），确保覆盖短生命周期订单的完整生命周期
            for (int i = 0; i < 12; i++) {
                long timerTime = startWindow + (i * SNAPSHOT_INTERVAL_MS);
                ctx.timerService().registerEventTimeTimer(timerTime);
            }


            System.out.println("*** 定时器注册: currentTime=" + currentTime +
                ", 注册窗口范围: " + startWindow + " 到 " + (startWindow + 11 * SNAPSHOT_INTERVAL_MS) +
                " (共12个窗口，覆盖6秒)");
            logger.debug("注册定时器: 当前事件时间={}, 注册窗口范围={} 到 {} (共12个窗口)",
                currentTime, startWindow, startWindow + 11 * SNAPSHOT_INTERVAL_MS);
        } catch (Exception e) {
            logger.error("订单簿重建处理元素失败: {}", value.toString(), e);
            System.out.println("*** 处理失败: " + e.getMessage());
        }
    }

    /**
     * 处理组合订单事件 - 在内部进行拆分处理
     * 确保组合订单的两条腿在同一个处理实例中协调处理
     */
    private void processCombinationOrder(CombinationOrderEvent combOrder) throws Exception {
        logger.info("[组合订单] 处理组合订单: 订单号={}, 组合合约={}, 腿1={}, 腿2={}, 状态={}", 
            combOrder.getOrd_nbr(), combOrder.getContract_cde(), 
            combOrder.getLeg_1_contract_cde(), combOrder.getLeg_2_contract_cde(), combOrder.getOrd_sts());

        // 使用CombinationOrderSplitter的逻辑，但在内部执行
        String combinationContract = combOrder.getContract_cde();
        String leg1Contract = combOrder.getLeg_1_contract_cde();
        String leg2Contract = combOrder.getLeg_2_contract_cde();
        
        long tradedVolume = combOrder.getTrd_vol();
        long remainingVolume = combOrder.getRmn_vol();

        // 创建第一条腿信息
        CombinationOrderSplitter.CombinationOrderLegInfo leg1Info = 
            new CombinationOrderSplitter.CombinationOrderLegInfo(
                combOrder.getOrd_nbr(),
                leg1Contract, // 当前腿合约
                leg2Contract, // 对手腿合约
                combOrder.getB_s_tag(),
                combOrder.getOrd_prc(),
                remainingVolume,
                combOrder.getOrd_sts(),
                combOrder.getEventTimestamp(),
                1, // 腿序号
                tradedVolume
            );

        // 创建第二条腿信息（买卖方向相反）
        String oppositeBuySellTag = "B".equals(combOrder.getB_s_tag()) ? "S" : "B";
        CombinationOrderSplitter.CombinationOrderLegInfo leg2Info = 
            new CombinationOrderSplitter.CombinationOrderLegInfo(
                combOrder.getOrd_nbr(),
                leg2Contract, // 当前腿合约
                leg1Contract, // 对手腿合约
                oppositeBuySellTag, // 相反方向
                -combOrder.getOrd_prc(), // 价差取负值
                remainingVolume,
                combOrder.getOrd_sts(),
                combOrder.getEventTimestamp(),
                2, // 腿序号
                tradedVolume
            );

        // 处理两条腿的虚拟层订单
        processVirtualLayerOrder(leg1Info);
        processVirtualLayerOrder(leg2Info);

        logger.info("[组合订单] 组合订单拆分完成: 订单号={}, 两条腿已在虚拟层处理", combOrder.getOrd_nbr());
    }

    /**
     * 处理基础层单腿订单事件
     * 基础层只处理真实的单腿委托，不包含组合订单的虚拟挂单
     * 订单状态说明：
     * 0 - 全部成交（从订单簿中移除）
     * 1 - 部分成交在队列中（保持在订单簿中）
     * 3 - 未成交在队列中（保持在订单簿中）
     * 5 - 已撤销（从订单簿中移除）
     * 典型状态变迁：3->1->0, 3->5, 3->0
     */
    private void processBaseLayerOrder(SingleLegOrderEvent order, Context ctx) throws Exception {
        String orderStatusStr = order.getOrd_sts();
        String orderNumber = order.getOrd_nbr();

        

        logger.debug("[基础层] 处理单腿订单: 订单号={}, 合约={}, 状态={}, 剩余量={}",
            orderNumber, order.getContract_cde(), orderStatusStr, order.getRmn_vol());

        
        if (orderStatusStr == null || orderStatusStr.isEmpty() || "nan".equalsIgnoreCase(orderStatusStr)) {
            logger.debug("[基础层] 订单状态无效，跳过处理");
            return;
        }

        int orderStatus = (int) Double.parseDouble(orderStatusStr);

        // 基础层订单状态管理 - 严格按照真实委托状态处理
        switch (orderStatus) {
            case 0: // 全部成交
                baseLayerOrders.remove(orderNumber);

                logger.info("[基础层] 单腿订单全部成交，移除: 订单号={}, 合约={}",
                    orderNumber, order.getContract_cde());

                break;
                
            case 1: // 部分成交在队列中
                if (order.getRmn_vol() > 0) {
                    baseLayerOrders.put(orderNumber, order);

                    logger.info("[基础层] 单腿订单部分成交，更新: 订单号={}, 合约={}, 剩余量={}",
                        orderNumber, order.getContract_cde(), order.getRmn_vol());

                } else {
                    baseLayerOrders.remove(orderNumber);
                    logger.warn("[基础层] 订单状态1但剩余量为0，移除: {}", orderNumber);
                }
                break;
                
            case 3: // 未成交在队列中
                if (order.getRmn_vol() > 0) {
                    // 记录状态变更时间戳
                    long currentEventTime = ctx.timestamp();
                    if (currentEventTime <= 0) {
                        currentEventTime = ctx.timerService().currentProcessingTime();
                    }

                    baseLayerOrders.put(orderNumber, order);
                    orderStateTimestamps.put(orderNumber, currentEventTime);
                    lastStateUpdateTime.update(currentEventTime);


                    logger.debug("[基础层] 单腿订单未成交在队列: 订单号={}, 合约={}, 剩余量={}",
                        orderNumber, order.getContract_cde(), order.getRmn_vol());

                } else {
                    baseLayerOrders.remove(orderNumber);
                    orderStateTimestamps.remove(orderNumber);
                    logger.warn("[基础层] 订单状态3但剩余量为0，移除: {}", orderNumber);
                }
                break;
                
            case 5: // 已撤销
                // 记录撤销时间戳
                long cancelEventTime = ctx.timestamp();
                if (cancelEventTime <= 0) {
                    cancelEventTime = ctx.timerService().currentProcessingTime();
                }

                baseLayerOrders.remove(orderNumber);
                orderStateTimestamps.put(orderNumber + "_CANCELLED", cancelEventTime); // 记录撤销时间
                // *** 修复关键问题：保留创建时间戳，不要删除，这对时序验证至关重要 ***
                // orderStateTimestamps.remove(orderNumber); // 注释掉错误的删除操作
                lastStateUpdateTime.update(cancelEventTime);
                break;
                
            default:
                baseLayerOrders.remove(orderNumber);
                logger.warn("[基础层] 未知订单状态 {}，移除: 订单号={}", orderStatus, orderNumber);
                break;
        }
    }

    /**
     * 处理虚拟层组合订单腿事件
     * 虚拟层处理组合订单的虚拟挂单，支持多轮成交和分阶段处理
     * 组合订单特点：
     * - 可能有多轮成交（从数据可看到同一订单号有多条记录，成交量逐步增加）
     * - 两腿都需要考虑，每腿独立处理
     * - 虚拟挂单不与基础层直接撮合，但影响整体订单簿流动性展示
     */
    private void processVirtualLayerOrder(CombinationOrderLegInfo legInfo) throws Exception {
        String orderStatus = legInfo.getOrderStatus();
        String orderNumber = legInfo.getOrdNbr();
        String legKey = orderNumber + "_leg" + legInfo.getLegNumber();
        
        logger.info("[虚拟层] 处理组合订单腿: 订单号={}, 腿号={}, 合约={}, 状态={}, 剩余量={}", 
            orderNumber, legInfo.getLegNumber(), legInfo.getCurrentLegContract(), orderStatus, legInfo.getRemainingVol());

        // 更新或创建组合订单状态跟踪
        CombinationOrderState combState = combOrderStates.get(orderNumber);
        if (combState == null) {
            combState = new CombinationOrderState(
                orderNumber,
                legInfo.getCurrentLegContract(),
                legInfo.getOppositeLegContract(),
                legInfo.getBuySellTag(),
                legInfo.getSpreadPrice(),
                legInfo.getRemainingVol() // 使用当前剩余量作为初始量的估算
            );
            logger.info("[虚拟层] 创建新的组合订单状态追踪: 订单号={}, 初始量={}", 
                orderNumber, legInfo.getRemainingVol());
        }
        
        // 计算本轮成交量（如果有的话）
        long previousRemaining = combState.getCurrentRemainingVol();
        long currentRemaining = legInfo.getRemainingVol();
        long tradedThisRound = Math.max(0, previousRemaining - currentRemaining);
        
        // 更新组合订单状态
        combState.updateState(orderStatus, tradedThisRound, currentRemaining);
        combOrderStates.put(orderNumber, combState);
        
        if (tradedThisRound > 0) {
            logger.info("[虚拟层] 组合订单第{}轮成交: 订单号={}, 本轮成交量={}, 累计成交量={}, 完成度={:.1f}%", 
                combState.getRoundCount(), orderNumber, tradedThisRound, combState.getTotalTradedVol(),
                combState.getCompletionRate() * 100);
        }

        // 虚拟层订单管理 - 支持多轮成交的虚拟挂单
        switch (orderStatus) {
            case "0": // 全部成交 - 移除虚拟订单
                virtualLayerOrders.remove(legKey);
                logger.info("[虚拟层] 组合订单腿全部成交，移除虚拟挂单: 订单号={}, 腿={}, 合约={}", 
                    orderNumber, legInfo.getLegNumber(), legInfo.getCurrentLegContract());
                break;
                
            case "1": // 部分成交在队列中 - 更新虚拟挂单
                if (legInfo.getRemainingVol() > 0) {
                    virtualLayerOrders.put(legKey, legInfo);
                    logger.info("[虚拟层] 组合订单腿部分成交，更新虚拟挂单: 订单号={}, 腿={}, 合约={}, 剩余量={}", 
                        orderNumber, legInfo.getLegNumber(), legInfo.getCurrentLegContract(), legInfo.getRemainingVol());
                } else {
                    virtualLayerOrders.remove(legKey);
                    logger.warn("[虚拟层] 组合订单腿状态1但剩余量为0，移除: {}", legKey);
                }
                break;
                
            case "3": // 未成交在队列中 - 保持虚拟挂单
                if (legInfo.getRemainingVol() > 0) {
                    virtualLayerOrders.put(legKey, legInfo);
                    logger.debug("[虚拟层] 组合订单腿未成交，保持虚拟挂单: 订单号={}, 腿={}, 合约={}, 剩余量={}", 
                        orderNumber, legInfo.getLegNumber(), legInfo.getCurrentLegContract(), legInfo.getRemainingVol());
                } else {
                    virtualLayerOrders.remove(legKey);
                    logger.warn("[虚拟层] 组合订单腿状态3但剩余量为0，移除: {}", legKey);
                }
                break;
                
            case "5": // 已撤销 - 移除虚拟挂单
                virtualLayerOrders.remove(legKey);
                logger.info("[虚拟层] 组合订单腿已撤销，移除虚拟挂单: 订单号={}, 腿={}, 合约={}", 
                    orderNumber, legInfo.getLegNumber(), legInfo.getCurrentLegContract());
                break;
                
            default:
                virtualLayerOrders.remove(legKey);
                logger.warn("[虚拟层] 组合订单腿未知状态 {}，移除: 订单号={}, 腿={}", 
                    orderStatus, orderNumber, legInfo.getLegNumber());
                break;
        }
    }

    @Override
    public void onTimer(long timestamp, OnTimerContext ctx, Collector<OrderBookSnapshot> out) throws Exception {
        String contractCode = ctx.getCurrentKey();
        OrderBookSnapshot snapshot = new OrderBookSnapshot(contractCode);
        snapshot.setTimestamp(timestamp);

        System.out.println("*** onTimer触发: 合约=" + contractCode + ", 时间戳=" + timestamp);
        logger.debug("合约 {} 生成分层订单簿快照，时间戳={}", contractCode, timestamp);

        // 第一步：构建基础层订单簿 (Base Layer) - 真实单腿订单
        Map<Double, Long> baseLayerBids = new TreeMap<>(Collections.reverseOrder()); // 买盘降序
        Map<Double, Long> baseLayerAsks = new TreeMap<>(); // 卖盘升序

        int baseLayerOrderCount = 0;
        boolean hasTargetOrder = false; // 检查是否包含目标订单



        // *** 增强状态输出日志，提供更详细的调试信息 ***
        if (hasTargetOrder) {
            System.out.printf("*** [onTimer状态-修复] 合约=%s, 时间戳=%d (%s), 总订单=%d, 包含目标订单=true, 买盘=%d档, 卖盘=%d档\n",
                contractCode, timestamp, formatTimestamp(timestamp), baseLayerOrderCount, baseLayerBids.size(), baseLayerAsks.size());
            logger.info("[基础层-快照-特别关注] 合约 {} 基础层包含目标订单10000000: 总订单{}个, 买盘{}档, 卖盘{}档",
                contractCode, baseLayerOrderCount, baseLayerBids.size(), baseLayerAsks.size());
        } else {
            System.out.printf("*** [onTimer状态-修复] 合约=%s, 时间戳=%d (%s), 总订单=%d, 包含目标订单=false\n",
                contractCode, timestamp, formatTimestamp(timestamp), baseLayerOrderCount);

            // *** 增强调试：检查是否有其他活跃订单 ***
            if (baseLayerOrderCount > 0) {
                System.out.printf("*** [其他订单检查] 合约=%s有%d个其他活跃订单, 买盘=%d档, 卖盘=%d档\n",
                    contractCode, baseLayerOrderCount, baseLayerBids.size(), baseLayerAsks.size());
            }

            logger.debug("[基础层] 合约 {} 基础层处理: {}个单腿订单, 买盘{}档, 卖盘{}档",
                contractCode, baseLayerOrderCount, baseLayerBids.size(), baseLayerAsks.size());
        }

        // 更新最优买卖价缓存，为虚拟层计算做准备
        updateBestPrices(baseLayerBids, baseLayerAsks);

        // 第二步：构建虚拟层订单簿 (Virtual Layer) - 组合订单虚拟挂单
        Map<Double, Long> virtualLayerBids = new TreeMap<>(Collections.reverseOrder());
        Map<Double, Long> virtualLayerAsks = new TreeMap<>();

        int virtualOrderCount = 0;
        int activeVirtualOrders = 0;
        
        for (CombinationOrderLegInfo legInfo : virtualLayerOrders.values()) {
            if (legInfo.getRemainingVol() > 0) {
                virtualOrderCount++;
                
                // 检查是否应该生成虚拟挂单
                CombinationOrderState combState = combOrderStates.get(legInfo.getOrdNbr());
                if (combState != null && combState.shouldGenerateVirtualOrders()) {
                    addSmartVirtualOrdersToLayer(virtualLayerBids, virtualLayerAsks, legInfo, combState);
                    activeVirtualOrders++;
                }
            }
        }
        
        logger.debug("[虚拟层] 合约 {} 虚拟层处理: {}个组合订单腿, {}个活跃虚拟挂单, 买盘{}档, 卖盘{}档", 
            contractCode, virtualOrderCount, activeVirtualOrders, virtualLayerBids.size(), virtualLayerAsks.size());

        // 第三步：合并基础层和虚拟层，生成最终的分层订单簿快照
        Map<Double, Long> finalBids = new TreeMap<>(Collections.reverseOrder());
        Map<Double, Long> finalAsks = new TreeMap<>();
        
        // 合并买盘：基础层 + 虚拟层
        finalBids.putAll(baseLayerBids);
        for (Map.Entry<Double, Long> entry : virtualLayerBids.entrySet()) {
            finalBids.merge(entry.getKey(), entry.getValue(), Long::sum);
        }
        
        // 合并卖盘：基础层 + 虚拟层
        finalAsks.putAll(baseLayerAsks);
        for (Map.Entry<Double, Long> entry : virtualLayerAsks.entrySet()) {
            finalAsks.merge(entry.getKey(), entry.getValue(), Long::sum);
        }
        
        // 第四步：输出分层订单簿快照 (强制输出所有快照，包括空的)
        snapshot.setBids(finalBids);
        snapshot.setAsks(finalAsks);

        // *** 状态一致性检查 ***
        performStateConsistencyCheck(contractCode, timestamp);

        System.out.println("*** 输出快照-修复: 合约=" + contractCode + ", 有数据=" + (!finalBids.isEmpty() || !finalAsks.isEmpty()) +
            ", 买盘=" + finalBids.size() + "档, 卖盘=" + finalAsks.size() + "档, 时间=" + formatTimestamp(timestamp));

        // 强制输出快照，包括空的订单簿
        if (!finalBids.isEmpty() || !finalAsks.isEmpty()) {
            logger.info("[分层订单簿] 合约 {} 输出快照: 基础层({}/{}档) + 虚拟层({}/{}档) = 最终({}/{}档)",
                contractCode,
                baseLayerBids.size(), baseLayerAsks.size(),
                virtualLayerBids.size(), virtualLayerAsks.size(),
                finalBids.size(), finalAsks.size());
            out.collect(snapshot);
        } else {
            // 即使订单簿为空，也输出快照（用于调试）
            logger.debug("[分层订单簿] 合约 {} 订单簿为空，但仍输出空快照", contractCode);
            out.collect(snapshot);
        }

        // 第五步：检查是否需要继续注册定时器
        long currentWatermark = ctx.timerService().currentWatermark();
        boolean isDataStreamEnded = currentWatermark >= Long.MAX_VALUE;

        if (isDataStreamEnded) {
            // 数据流结束，停止注册新定时器
            logger.debug("合约 {} 数据流结束，停止定时器", contractCode);
            System.out.printf("*** [定时器管理] 合约=%s 数据流结束，停止定时器\n", contractCode);
        } else {
            // 数据流未结束，继续注册下一个定时器以保持连续快照
            long nextTimerTime = timestamp + SNAPSHOT_INTERVAL_MS;
            ctx.timerService().registerEventTimeTimer(nextTimerTime);
            logger.debug("合约 {} 注册下次定时器: {}", contractCode, nextTimerTime);
            System.out.printf("*** [定时器管理-修复] 合约=%s 注册下次定时器: %d (%s)\n",
                contractCode, nextTimerTime, formatTimestamp(nextTimerTime));
        }
    }

    /**
     * 执行状态一致性检查
     */
    private void performStateConsistencyCheck(String contractCode, long timestamp) {
        try {
            int totalOrdersInState = 0;
            int cancelledOrdersInState = 0;

            for (String key : orderStateTimestamps.keys()) {
                if (key.endsWith("_CANCELLED")) {
                    cancelledOrdersInState++;
                } else {
                    totalOrdersInState++;
                }
            }

            int actualOrdersInBase = 0;
            for (SingleLegOrderEvent order : baseLayerOrders.values()) {
                if (order.getRmn_vol() > 0) {
                    actualOrdersInBase++;
                }
            }

            System.out.printf("*** [状态一致性检查] 合约=%s, 时间戳=%d (%s): 状态记录中总订单=%d, 已撤销订单=%d, 基础层实际订单=%d\n",
                contractCode, timestamp, formatTimestamp(timestamp), totalOrdersInState, cancelledOrdersInState, actualOrdersInBase);

        } catch (Exception e) {
            System.out.printf("*** [状态一致性检查错误] %s\n", e.getMessage());
        }
    }

    /**
     * 根据聚合后的买卖盘，更新缓存中的最优价格
     */
    private void updateBestPrices(Map<Double, Long> bids, Map<Double, Long> asks) throws Exception {
        Double bestBid = bids.isEmpty() ? null : bids.keySet().iterator().next();
        Double bestAsk = asks.isEmpty() ? null : asks.keySet().iterator().next();
        bestBidPrice.update(bestBid);
        bestAskPrice.update(bestAsk);
    }

    /**
     * 为虚拟层添加简化的虚拟挂单
     * 按照最优价模拟组合委托的挂单需求
     */
    private void addSmartVirtualOrdersToLayer(Map<Double, Long> virtualBids, Map<Double, Long> virtualAsks, 
                                        CombinationOrderLegInfo legInfo, CombinationOrderState combState) throws Exception {
        String buySellTag = legInfo.getBuySellTag();
        long volume = legInfo.getRemainingVol();
        
        // 获取当前合约的最优价格
        Double currentBestBid = bestBidPrice.value();
        Double currentBestAsk = bestAskPrice.value();
        
        // 最小价位，避免价格为0或负数
        final double MIN_TICK = 0.01;
        
        logger.debug("[虚拟层-简化挂单] 订单号={}, 腿={}, 方向={}, 数量={}", 
            legInfo.getOrdNbr(), legInfo.getLegNumber(), buySellTag, volume);

        if ("B".equals(buySellTag)) { // 组合订单买腿 - 应该在买盘挂单
            if (currentBestBid != null) {
                // 在最优买价上方挂一个小幅度的虚拟买单
                double virtualBidPrice = currentBestBid + MIN_TICK;
                virtualBids.merge(virtualBidPrice, volume, Long::sum);
                logger.debug("[虚拟层-买腿] 在买盘挂单: 价格={}, 数量={}", virtualBidPrice, volume);
            } else if (currentBestAsk != null) {
                // 如果没有买盘，在卖盘下方挂买单
                double virtualBidPrice = Math.max(MIN_TICK, currentBestAsk - MIN_TICK);
                virtualBids.merge(virtualBidPrice, volume, Long::sum);
                logger.debug("[虚拟层-买腿-备选] 基于卖盘挂买单: 价格={}, 数量={}", virtualBidPrice, volume);
            }
        } else if ("S".equals(buySellTag)) { // 组合订单卖腿 - 应该在卖盘挂单
            if (currentBestAsk != null) {
                // 在最优卖价下方挂一个小幅度的虚拟卖单
                double virtualAskPrice = currentBestAsk - MIN_TICK;
                if (virtualAskPrice > MIN_TICK) {
                    virtualAsks.merge(virtualAskPrice, volume, Long::sum);
                    logger.debug("[虚拟层-卖腿] 在卖盘挂单: 价格={}, 数量={}", virtualAskPrice, volume);
                }
            } else if (currentBestBid != null) {
                // 如果没有卖盘，在买盘上方挂卖单
                double virtualAskPrice = currentBestBid + MIN_TICK;
                virtualAsks.merge(virtualAskPrice, volume, Long::sum);
                logger.debug("[虚拟层-卖腿-备选] 基于买盘挂卖单: 价格={}, 数量={}", virtualAskPrice, volume);
            }
        }
    }

    public void onProcessingTime(long timestamp, OnTimerContext ctx, Collector<OrderBookSnapshot> out) throws Exception {
        String contractCode = ctx.getCurrentKey();

        System.out.printf("*** [处理时间定时器] 合约=%s, 时间戳=%d, 强制状态同步\n", contractCode, timestamp);

        // 强制触发事件时间快照生成
        long currentTime = ctx.timerService().currentProcessingTime();
        long snapshotTime = ((currentTime / SNAPSHOT_INTERVAL_MS)) * SNAPSHOT_INTERVAL_MS;

        // 生成当前时间窗口的快照
        OrderBookSnapshot snapshot = new OrderBookSnapshot(contractCode);
        snapshot.setTimestamp(snapshotTime);

        // 构建基础层订单簿
        Map<Double, Long> baseLayerBids = new TreeMap<>(Collections.reverseOrder());
        Map<Double, Long> baseLayerAsks = new TreeMap<>();

        int baseLayerOrderCount = 0;
        boolean hasTargetOrder = false;

        try {
            for (SingleLegOrderEvent order : baseLayerOrders.values()) {
                if (order.getRmn_vol() > 0) {
                    String orderNumber = order.getOrd_nbr();

                    // 检查订单状态的时序有效性
                    Long orderCreateTime = orderStateTimestamps.get(orderNumber);
                    Long orderCancelTime = orderStateTimestamps.get(orderNumber + "_CANCELLED");

                    // 判断在快照时间点，订单是否应该存在
                    boolean shouldExistAtSnapshotTime = true;

                    if (orderCreateTime != null && snapshotTime < orderCreateTime) {
                        shouldExistAtSnapshotTime = false;
                    }

                    if (orderCancelTime != null && snapshotTime >= orderCancelTime) {
                        shouldExistAtSnapshotTime = false;
                    }

                    if (shouldExistAtSnapshotTime) {
                        baseLayerOrderCount++;

                        if ("B".equals(order.getB_s_tag())) {
                            baseLayerBids.merge(order.getOrd_prc(), order.getRmn_vol(), Long::sum);
                        } else if ("S".equals(order.getB_s_tag())) {
                            baseLayerAsks.merge(order.getOrd_prc(), order.getRmn_vol(), Long::sum);
                        }
                    }
                }
            }
        } catch (Exception e) {
            System.out.printf("*** [处理时间定时器错误] %s\n", e.getMessage());
        }

        System.out.printf("*** [处理时间快照] 合约=%s, 时间戳=%d, 总订单=%d, 包含目标订单=%s\n",
            contractCode, snapshotTime, baseLayerOrderCount, hasTargetOrder);

        // 设置快照数据（使用正确的方法名）
        snapshot.setBids(baseLayerBids);
        snapshot.setAsks(baseLayerAsks);

        // 输出快照
        if (baseLayerOrderCount > 0 || hasTargetOrder) {
            out.collect(snapshot);
            System.out.printf("*** [处理时间快照输出] 合约=%s, 有数据=true, 买盘=%d档, 卖盘=%d档\n",
                contractCode, baseLayerBids.size(), baseLayerAsks.size());
        } else {
            System.out.printf("*** [处理时间快照跳过] 合约=%s, 无数据\n", contractCode);
        }
    }
}
