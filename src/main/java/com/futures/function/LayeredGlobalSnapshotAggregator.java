package com.futures.function;

import com.futures.pojo.BaseOrderBook;
import com.futures.pojo.LayeredOrderBookSnapshot;
import com.futures.pojo.OrderBookSnapshot;
import com.futures.pojo.VirtualOrderBook;
import org.apache.flink.api.common.state.MapState;
import org.apache.flink.api.common.state.MapStateDescriptor;
import org.apache.flink.api.common.typeinfo.TypeHint;
import org.apache.flink.api.common.typeinfo.TypeInformation;
import org.apache.flink.configuration.Configuration;
import org.apache.flink.streaming.api.functions.ProcessFunction;
import org.apache.flink.util.Collector;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.*;

/**
 * 分层全局快照聚合器
 * 优化的输出格式，清晰显示基础层和虚拟层的订单簿信息
 * 支持0.5秒间隔的实时输出
 */
public class LayeredGlobalSnapshotAggregator extends ProcessFunction<OrderBookSnapshot, String> {
    private static final Logger logger = LoggerFactory.getLogger(LayeredGlobalSnapshotAggregator.class);
    private static final long SNAPSHOT_INTERVAL_MS = 500; // 0.5秒间隔
    private static final long MAX_OUT_OF_ORDERNESS_MS = 250; // 最大乱序容忍时间

    private transient MapState<Long, Map<String, OrderBookSnapshot>> snapshotBuffer;
    private transient MapState<String, Long> lastSnapshotTime;
    private transient MapState<String, Long> lastOutputTime; // 记录最后输出时间

    @Override
    public void open(Configuration parameters) throws Exception {
        super.open(parameters);

        MapStateDescriptor<Long, Map<String, OrderBookSnapshot>> bufferDescriptor =
            new MapStateDescriptor<>(
                "layered-global-snapshot-buffer",
                TypeInformation.of(Long.class),
                TypeInformation.of(new TypeHint<Map<String, OrderBookSnapshot>>() {})
            );
        snapshotBuffer = getRuntimeContext().getMapState(bufferDescriptor);

        MapStateDescriptor<String, Long> timeDescriptor =
            new MapStateDescriptor<>(
                "layered-last-snapshot-time",
                TypeInformation.of(String.class),
                TypeInformation.of(Long.class)
            );
        lastSnapshotTime = getRuntimeContext().getMapState(timeDescriptor);

        MapStateDescriptor<String, Long> outputTimeDescriptor =
            new MapStateDescriptor<>(
                "layered-last-output-time",
                TypeInformation.of(String.class),
                TypeInformation.of(Long.class)
            );
        lastOutputTime = getRuntimeContext().getMapState(outputTimeDescriptor);

        logger.info("LayeredGlobalSnapshotAggregator 初始化完成");
    }

    @Override
    public void processElement(OrderBookSnapshot snapshot, Context ctx, Collector<String> out) throws Exception {
        long currentWatermark = ctx.timerService().currentWatermark();
        long eventTime = snapshot.getTimestamp();
        String contractCode = snapshot.getContract_cde();

        System.out.println("*** [分层聚合] 接收快照: 合约=" + contractCode +
                ", 事件时间=" + eventTime + " (" + formatTimestamp(eventTime) + ")" +
                ", 水位线=" + currentWatermark + " (" + formatTimestamp(currentWatermark) + ")");
        logger.info("[分层聚合] 接收快照: 合约={}, 事件时间={}, 水位线={}",
                contractCode, eventTime, currentWatermark);

        // 检查事件时间的有效性
        Long lastTime = lastSnapshotTime.get(contractCode);
        if (lastTime != null && eventTime < lastTime - MAX_OUT_OF_ORDERNESS_MS) {
            logger.warn("[分层聚合] 丢弃过期快照: 合约={}, 事件时间={}, 最后时间={}",
                    contractCode, eventTime, lastTime);
            return;
        }

        // 计算窗口结束时间（向上取整到最近的500ms边界）
        long windowEnd = ((eventTime / SNAPSHOT_INTERVAL_MS) + 1) * SNAPSHOT_INTERVAL_MS;

        Map<String, OrderBookSnapshot> buffer = snapshotBuffer.get(windowEnd);
        if (buffer == null) {
            buffer = new HashMap<>();
            snapshotBuffer.put(windowEnd, buffer);
        }

        // 只保留最新的快照（同一窗口内同一合约可能有多个快照）
        OrderBookSnapshot existing = buffer.get(contractCode);
        if (existing == null || snapshot.getTimestamp() >= existing.getTimestamp()) {
            buffer.put(contractCode, snapshot);
            lastSnapshotTime.put(contractCode, eventTime);
        }

        System.out.println("*** [分层聚合] 缓存快照到窗口: " + windowEnd +
                " (" + formatTimestamp(windowEnd) + "), 当前合约数: " + buffer.size());
        logger.debug("[分层聚合] 缓存快照到窗口: {}, 当前合约数: {}", windowEnd, buffer.size());

        // 注册事件时间定时器
        long timerTime = windowEnd + MAX_OUT_OF_ORDERNESS_MS;
        ctx.timerService().registerEventTimeTimer(timerTime);
        System.out.println("*** [分层聚合] 注册定时器: " + timerTime +
                " (" + formatTimestamp(timerTime) + "), 窗口=" + windowEnd +
                " (" + formatTimestamp(windowEnd) + ")");

        // 检查是否有足够的数据可以立即输出（用于调试）
        System.out.println("*** [分层聚合] 当前缓存状态: 窗口数=" +
                (snapshotBuffer.entries() != null ?
                    java.util.stream.StreamSupport.stream(snapshotBuffer.entries().spliterator(), false).count() : 0) +
                ", 当前窗口合约数=" + buffer.size());
    }

    @Override
    public void onTimer(long timestamp, OnTimerContext ctx, Collector<String> out) throws Exception {
        long actualWindowEnd = timestamp - MAX_OUT_OF_ORDERNESS_MS;
        long currentWatermark = ctx.timerService().currentWatermark();

        System.out.println("*** [分层聚合] 定时器触发: 窗口结束时间=" + actualWindowEnd +
                " (" + formatTimestamp(actualWindowEnd) + "), 触发时间=" + timestamp +
                " (" + formatTimestamp(timestamp) + "), 水位线=" + currentWatermark +
                " (" + formatTimestamp(currentWatermark) + ")");
        logger.info("[分层聚合] 定时器触发: 窗口结束时间={}, 触发时间={}, 水位线={}",
                actualWindowEnd, timestamp, currentWatermark);

        Map<String, OrderBookSnapshot> finalSnapshots = snapshotBuffer.get(actualWindowEnd);

        System.out.println("*** [分层聚合] 定时器处理: 窗口=" + actualWindowEnd +
                " (" + formatTimestamp(actualWindowEnd) + "), 快照数=" +
                (finalSnapshots != null ? finalSnapshots.size() : 0));

        if (finalSnapshots != null && !finalSnapshots.isEmpty()) {
            System.out.println("*** [分层聚合] 输出全局快照: 窗口时间=" + actualWindowEnd +
                    " (" + formatTimestamp(actualWindowEnd) + "), 合约数=" + finalSnapshots.size());

            // 打印所有合约信息
            for (String contract : finalSnapshots.keySet()) {
                OrderBookSnapshot snapshot = finalSnapshots.get(contract);
                System.out.println("*** [分层聚合] 包含合约: " + contract +
                        ", 买盘=" + snapshot.getBids().size() + "档, 卖盘=" + snapshot.getAsks().size() + "档");
            }

            logger.info("[分层聚合] 输出全局快照: 窗口时间={}, 合约数={}",
                    actualWindowEnd, finalSnapshots.size());

            outputLayeredGlobalSnapshot(finalSnapshots, actualWindowEnd, out);
            snapshotBuffer.remove(actualWindowEnd);
            lastOutputTime.put("GLOBAL", System.currentTimeMillis());

            // 清理过期的快照时间记录
            cleanupExpiredTimestamps(actualWindowEnd);
        } else {
            System.out.println("*** [分层聚合] 窗口 " + actualWindowEnd + " 无快照数据");
            logger.debug("[分层聚合] 窗口 {} 无快照数据", actualWindowEnd);
        }
    }

    /**
     * 输出优化的分层全局快照
     */
    private void outputLayeredGlobalSnapshot(Map<String, OrderBookSnapshot> finalSnapshots,
                                           long windowEnd, Collector<String> out) {
        StringBuilder sb = new StringBuilder();
        sb.append(String.format("\n╔══════════════════════════════════════════════════════════════════════════════════════╗\n"));
        sb.append(String.format("║                          分层期货订单簿全局快照                                      ║\n"));
        sb.append(String.format("║                     时间: %s                           ║\n", formatTimestamp(windowEnd)));
        sb.append(String.format("╠══════════════════════════════════════════════════════════════════════════════════════╣\n"));

        List<String> sortedContracts = new ArrayList<>(finalSnapshots.keySet());
        Collections.sort(sortedContracts);

        int totalContracts = sortedContracts.size();
        int activeContracts = 0;
        int baseLayerContracts = 0;
        int virtualLayerContracts = 0;

        for (String contract : sortedContracts) {
            OrderBookSnapshot snapshot = finalSnapshots.get(contract);
            boolean hasData = !snapshot.getAsks().isEmpty() || !snapshot.getBids().isEmpty();

            if (hasData) {
                activeContracts++;

                // 判断是基础层还是虚拟层（这里简化处理，实际可以通过合约名称或其他标识区分）
                if (isBaseLayerContract(contract)) {
                    baseLayerContracts++;
                    sb.append(formatBaseLayerContract(contract, snapshot));
                } else {
                    virtualLayerContracts++;
                    sb.append(formatVirtualLayerContract(contract, snapshot));
                }
            } else {
                sb.append(String.format("║ 📊 合约: %-15s │ 状态: 空订单簿                                    ║\n", contract));
            }
        }

        sb.append(String.format("╠══════════════════════════════════════════════════════════════════════════════════════╣\n"));
        sb.append(String.format("║ 📈 统计汇总:                                                                         ║\n"));
        sb.append(String.format("║    总合约数: %-3d │ 活跃合约: %-3d │ 基础层: %-3d │ 虚拟层: %-3d                ║\n",
                totalContracts, activeContracts, baseLayerContracts, virtualLayerContracts));
        sb.append(String.format("║    覆盖率: %.1f%% │ 基础层占比: %.1f%% │ 虚拟层占比: %.1f%%                        ║\n",
                totalContracts > 0 ? (activeContracts * 100.0 / totalContracts) : 0.0,
                activeContracts > 0 ? (baseLayerContracts * 100.0 / activeContracts) : 0.0,
                activeContracts > 0 ? (virtualLayerContracts * 100.0 / activeContracts) : 0.0));
        sb.append(String.format("╚══════════════════════════════════════════════════════════════════════════════════════╝\n"));

        out.collect(sb.toString());
    }

    /**
     * 格式化基础层合约信息
     */
    private String formatBaseLayerContract(String contract, OrderBookSnapshot snapshot) {
        StringBuilder sb = new StringBuilder();
        sb.append(String.format("║ 🏗️  基础层: %-12s │", contract));

        if (!snapshot.getBids().isEmpty() || !snapshot.getAsks().isEmpty()) {
            Map<Double, Long> sortedAsks = new TreeMap<>(snapshot.getAsks());
            Map<Double, Long> sortedBids = new TreeMap<>(Collections.reverseOrder());
            sortedBids.putAll(snapshot.getBids());

            // 显示最优买卖价
            if (!sortedBids.isEmpty() && !sortedAsks.isEmpty()) {
                double bestBid = sortedBids.keySet().iterator().next();
                double bestAsk = sortedAsks.keySet().iterator().next();
                long bidVol = sortedBids.get(bestBid);
                long askVol = sortedAsks.get(bestAsk);
                double spread = bestAsk - bestBid;

                sb.append(String.format(" 买一:%.2f(%d) 卖一:%.2f(%d) 价差:%.2f",
                        bestBid, bidVol, bestAsk, askVol, spread));
            } else if (!sortedBids.isEmpty()) {
                double bestBid = sortedBids.keySet().iterator().next();
                long bidVol = sortedBids.get(bestBid);
                sb.append(String.format(" 买一:%.2f(%d) 卖一:-- 价差:--", bestBid, bidVol));
            } else {
                double bestAsk = sortedAsks.keySet().iterator().next();
                long askVol = sortedAsks.get(bestAsk);
                sb.append(String.format(" 买一:-- 卖一:%.2f(%d) 价差:--", bestAsk, askVol));
            }

            sb.append(String.format(" │ 档数:%d/%d", sortedBids.size(), sortedAsks.size()));
        } else {
            sb.append(" 无挂单                                    ");
        }

        sb.append(" ║\n");
        return sb.toString();
    }

    /**
     * 格式化虚拟层合约信息
     */
    private String formatVirtualLayerContract(String contract, OrderBookSnapshot snapshot) {
        StringBuilder sb = new StringBuilder();
        sb.append(String.format("║ 🔮 虚拟层: %-12s │", contract));

        if (!snapshot.getBids().isEmpty() || !snapshot.getAsks().isEmpty()) {
            Map<Double, Long> sortedAsks = new TreeMap<>(snapshot.getAsks());
            Map<Double, Long> sortedBids = new TreeMap<>(Collections.reverseOrder());
            sortedBids.putAll(snapshot.getBids());

            // 显示最优买卖价
            if (!sortedBids.isEmpty() && !sortedAsks.isEmpty()) {
                double bestBid = sortedBids.keySet().iterator().next();
                double bestAsk = sortedAsks.keySet().iterator().next();
                long bidVol = sortedBids.get(bestBid);
                long askVol = sortedAsks.get(bestAsk);
                double spread = bestAsk - bestBid;

                sb.append(String.format(" 虚买:%.2f(%d) 虚卖:%.2f(%d) 价差:%.2f",
                        bestBid, bidVol, bestAsk, askVol, spread));
            } else if (!sortedBids.isEmpty()) {
                double bestBid = sortedBids.keySet().iterator().next();
                long bidVol = sortedBids.get(bestBid);
                sb.append(String.format(" 虚买:%.2f(%d) 虚卖:-- 价差:--", bestBid, bidVol));
            } else {
                double bestAsk = sortedAsks.keySet().iterator().next();
                long askVol = sortedAsks.get(bestAsk);
                sb.append(String.format(" 虚买:-- 虚卖:%.2f(%d) 价差:--", bestAsk, askVol));
            }

            sb.append(String.format(" │ 档数:%d/%d", sortedBids.size(), sortedAsks.size()));
        } else {
            sb.append(" 无虚拟挂单                                ");
        }

        sb.append(" ║\n");
        return sb.toString();
    }

    /**
     * 判断是否为基础层合约（简化实现）
     */
    private boolean isBaseLayerContract(String contract) {
        // 简化判断：不包含组合标识的为基础层合约
        return !contract.contains("SPD_") && !contract.contains("_");
    }

    /**
     * 格式化时间戳
     */
    private String formatTimestamp(long timestamp) {
        return LocalDateTime.ofInstant(
                java.time.Instant.ofEpochMilli(timestamp),
                ZoneId.of("Asia/Shanghai")
        ).format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss.SSS"));
    }

    /**
     * 清理过期的时间戳记录
     */
    private void cleanupExpiredTimestamps(long windowEnd) throws Exception {
        long expiredThreshold = windowEnd - 5 * 60 * 1000; // 5分钟前

        Iterator<Map.Entry<String, Long>> iterator = lastSnapshotTime.entries().iterator();
        while (iterator.hasNext()) {
            Map.Entry<String, Long> entry = iterator.next();
            if (entry.getValue() < expiredThreshold) {
                iterator.remove();
            }
        }
    }
}