<?xml version="1.0" encoding="UTF-8"?>
<Configuration status="WARN">
    <Properties>
        <Property name="LOG_HOME">logs</Property>
        <!-- 定义日志格式 -->
        <Property name="LOG_PATTERN">%d{yyyy-MM-dd HH:mm:ss.SSS} [%t] %-5level %logger{36} - %msg%n</Property>
        <!-- 定义详细日志格式 -->
        <Property name="DETAILED_PATTERN">%d{yyyy-MM-dd HH:mm:ss.SSS} [%t] %-5level [%c{1.}:%L] - %msg%n</Property>
    </Properties>

    <Appenders>
        <!-- 控制台输出 -->
        <Console name="Console" target="SYSTEM_OUT">
            <PatternLayout pattern="${LOG_PATTERN}"/>
            <ThresholdFilter level="INFO" onMatch="ACCEPT" onMismatch="DENY"/>
        </Console>

        <!-- 系统主日志 - 包含所有INFO及以上级别的日志 -->
        <File name="MainLog" fileName="${LOG_HOME}/futures-system.log">
            <PatternLayout pattern="${LOG_PATTERN}"/>
            <ThresholdFilter level="INFO" onMatch="ACCEPT" onMismatch="DENY"/>
        </File>

        <!-- 订单簿重建日志 -->
        <File name="OrderBookLog" fileName="${LOG_HOME}/orderbook.log">
            <PatternLayout pattern="${DETAILED_PATTERN}"/>
        </File>

        <!-- 盈亏计算日志 -->
        <File name="PnLLog" fileName="${LOG_HOME}/pnl-calculation.log">
            <PatternLayout pattern="${DETAILED_PATTERN}"/>
        </File>

        <!-- 交易数据处理日志 -->
        <File name="TradeLog" fileName="${LOG_HOME}/trade-processing.log">
            <PatternLayout pattern="${DETAILED_PATTERN}"/>
        </File>

        <!-- Kafka连接日志 -->
        <File name="KafkaLog" fileName="${LOG_HOME}/kafka.log">
            <PatternLayout pattern="${LOG_PATTERN}"/>
        </File>

        <!-- 异常和错误日志 -->
        <File name="ErrorLog" fileName="${LOG_HOME}/error.log">
            <PatternLayout pattern="${DETAILED_PATTERN}"/>
            <ThresholdFilter level="WARN" onMatch="ACCEPT" onMismatch="DENY"/>
        </File>

        <!-- Debug详细日志 -->
        <File name="DebugLog" fileName="${LOG_HOME}/debug.log">
            <PatternLayout pattern="${DETAILED_PATTERN}"/>
            <ThresholdFilter level="DEBUG" onMatch="ACCEPT" onMismatch="DENY"/>
        </File>

        <!-- 性能监控日志 -->
        <File name="PerformanceLog" fileName="${LOG_HOME}/performance.log">
            <PatternLayout pattern="${LOG_PATTERN}"/>
        </File>
    </Appenders>

    <Loggers>
        <!-- 订单簿重建相关日志 -->
        <Logger name="com.futures.function.OrderBookReconstructionFunction" level="DEBUG" additivity="false">
            <AppenderRef ref="OrderBookLog"/>
            <AppenderRef ref="Console"/>
            <AppenderRef ref="MainLog"/>
            <AppenderRef ref="ErrorLog"/>
        </Logger>

        <!-- 盈亏计算相关日志 -->
        <Logger name="com.futures.function.PnLCalculationFunction" level="INFO" additivity="false">
            <AppenderRef ref="PnLLog"/>
            <AppenderRef ref="MainLog"/>
            <AppenderRef ref="ErrorLog"/>
        </Logger>

        <!-- Kafka作业相关日志 -->
        <Logger name="com.futures.job.FuturesOrderBookKafkaJob" level="DEBUG" additivity="false">
            <AppenderRef ref="TradeLog"/>
            <AppenderRef ref="Console"/>
            <AppenderRef ref="MainLog"/>
        </Logger>

        <!-- 交易数据处理日志 -->
        <Logger name="com.futures.job.FuturesOrderBookJob.DebugTradeEventParser" level="INFO" additivity="false">
            <AppenderRef ref="TradeLog"/>
            <AppenderRef ref="MainLog"/>
        </Logger>

        <Logger name="com.futures.job.FuturesOrderBookKafkaJob.DebugTradeEventParser" level="DEBUG" additivity="false">
            <AppenderRef ref="TradeLog"/>
            <AppenderRef ref="Console"/>
            <AppenderRef ref="MainLog"/>
        </Logger>

        <Logger name="com.futures.job.FuturesOrderBookKafkaJob.DebugSingleLegOrderParser" level="DEBUG" additivity="false">
            <AppenderRef ref="TradeLog"/>
            <AppenderRef ref="Console"/>
            <AppenderRef ref="MainLog"/>
        </Logger>

        <Logger name="com.futures.job.FuturesOrderBookKafkaJob.DebugCombinationOrderParser" level="DEBUG" additivity="false">
            <AppenderRef ref="TradeLog"/>
            <AppenderRef ref="Console"/>
            <AppenderRef ref="MainLog"/>
        </Logger>

        <!-- Kafka相关日志 -->
        <Logger name="org.apache.kafka" level="WARN" additivity="false">
            <AppenderRef ref="KafkaLog"/>
            <AppenderRef ref="ErrorLog"/>
        </Logger>

        <Logger name="org.apache.flink.connector.kafka" level="INFO" additivity="false">
            <AppenderRef ref="KafkaLog"/>
            <AppenderRef ref="MainLog"/>
            <AppenderRef ref="ErrorLog"/>
        </Logger>

        <!-- Flink相关日志 -->
        <Logger name="org.apache.flink" level="WARN" additivity="false">
            <AppenderRef ref="MainLog"/>
            <AppenderRef ref="ErrorLog"/>
        </Logger>

        <!-- 性能监控日志 -->
        <Logger name="PERFORMANCE" level="INFO" additivity="false">
            <AppenderRef ref="PerformanceLog"/>
        </Logger>

        <!-- 业务特定日志 -->
        <Logger name="ORDERBOOK_SNAPSHOT" level="DEBUG" additivity="false">
            <AppenderRef ref="OrderBookLog"/>
            <AppenderRef ref="Console"/>
        </Logger>

        <Logger name="PNL_CALCULATION" level="INFO" additivity="false">
            <AppenderRef ref="PnLLog"/>
        </Logger>

        <Logger name="TRADE_PROCESSING" level="DEBUG" additivity="false">
            <AppenderRef ref="TradeLog"/>
            <AppenderRef ref="Console"/>
        </Logger>

        <!-- 根日志配置 -->
        <Root level="DEBUG">
            <AppenderRef ref="Console"/>
            <AppenderRef ref="MainLog"/>
            <AppenderRef ref="ErrorLog"/>
        </Root>
    </Loggers>
</Configuration>
