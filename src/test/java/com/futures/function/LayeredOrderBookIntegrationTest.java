package com.futures.function;

import com.futures.pojo.*;
import org.junit.Test;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * 分层订单簿集成测试
 * 验证修复后的水位线和定时器逻辑
 */
public class LayeredOrderBookIntegrationTest {
    private static final Logger logger = LoggerFactory.getLogger(LayeredOrderBookIntegrationTest.class);
    
    @Test
    public void testWatermarkAndTimerLogic() {
        logger.info("开始测试水位线和定时器逻辑");
        
        // 测试时间戳计算逻辑
        long currentTime = System.currentTimeMillis();
        long snapshotInterval = 500; // 0.5秒
        
        // 测试窗口计算
        long eventTime1 = currentTime;
        long windowEnd1 = ((eventTime1 / snapshotInterval) + 1) * snapshotInterval;
        
        long eventTime2 = currentTime + 300; // 0.3秒后
        long windowEnd2 = ((eventTime2 / snapshotInterval) + 1) * snapshotInterval;
        
        long eventTime3 = currentTime + 600; // 0.6秒后
        long windowEnd3 = ((eventTime3 / snapshotInterval) + 1) * snapshotInterval;
        
        System.out.println("*** 窗口计算测试:");
        System.out.println("事件时间1: " + eventTime1 + " -> 窗口结束: " + windowEnd1 + " (差值: " + (windowEnd1 - eventTime1) + "ms)");
        System.out.println("事件时间2: " + eventTime2 + " -> 窗口结束: " + windowEnd2 + " (差值: " + (windowEnd2 - eventTime2) + "ms)");
        System.out.println("事件时间3: " + eventTime3 + " -> 窗口结束: " + windowEnd3 + " (差值: " + (windowEnd3 - eventTime3) + "ms)");
        
        // 验证同一窗口内的事件应该有相同的窗口结束时间
        assert windowEnd1 == windowEnd2 : "同一窗口内的事件应该有相同的窗口结束时间";
        assert windowEnd3 > windowEnd1 : "不同窗口的结束时间应该不同";
        
        logger.info("窗口计算逻辑测试通过");
    }
    
    @Test
    public void testBaseOrderBookWithTimestamp() {
        logger.info("开始测试基础层订单簿时间戳处理");
        
        // 创建基础层订单簿
        BaseOrderBook baseBook = new BaseOrderBook("EB2504");
        baseBook.getBids().put(8095.0, 10L);
        baseBook.getAsks().put(8096.0, 5L);
        
        long currentTime = System.currentTimeMillis();
        baseBook.setTimestamp(currentTime);
        
        // 生成BBO
        BBO_Update bbo = baseBook.generateBBO();
        
        // 验证时间戳传递
        assert bbo.getTimestamp() == currentTime : "BBO时间戳应该与订单簿时间戳一致";
        assert bbo.getInstrumentId().equals("EB2504") : "BBO合约代码应该正确";
        assert bbo.getBestBid() == 8095.0 : "BBO最优买价应该正确";
        assert bbo.getBestAsk() == 8096.0 : "BBO最优卖价应该正确";
        
        System.out.println("*** 基础层订单簿测试:");
        System.out.println("订单簿: " + baseBook);
        System.out.println("BBO: " + bbo);
        
        logger.info("基础层订单簿时间戳处理测试通过");
    }
    
    @Test
    public void testVirtualOrderBookWithTimestamp() {
        logger.info("开始测试虚拟层订单簿时间戳处理");
        
        // 创建虚拟层订单簿
        VirtualOrderBook virtualBook = new VirtualOrderBook("EB2504");
        virtualBook.setSourceComboContract("SPD_EB2504_EB2505");
        virtualBook.addVirtualBid(8093.0, 3L);
        virtualBook.addVirtualAsk(8098.0, 2L);
        
        long currentTime = System.currentTimeMillis();
        virtualBook.setTimestamp(currentTime);
        
        // 验证虚拟订单簿属性
        assert virtualBook.getInstrumentId().equals("EB2504") : "虚拟订单簿合约代码应该正确";
        assert virtualBook.getTimestamp() == currentTime : "虚拟订单簿时间戳应该正确";
        assert !virtualBook.isEmpty() : "虚拟订单簿不应该为空";
        assert virtualBook.getBestVirtualBid().equals(8093.0) : "最优虚拟买价应该正确";
        assert virtualBook.getBestVirtualAsk().equals(8098.0) : "最优虚拟卖价应该正确";
        
        System.out.println("*** 虚拟层订单簿测试:");
        System.out.println("虚拟订单簿: " + virtualBook);
        
        logger.info("虚拟层订单簿时间戳处理测试通过");
    }
    
    @Test
    public void testOrderBookSnapshotConversion() {
        logger.info("开始测试订单簿快照转换");
        
        long currentTime = System.currentTimeMillis();
        
        // 创建基础层订单簿
        BaseOrderBook baseBook = new BaseOrderBook("EB2504");
        baseBook.getBids().put(8095.0, 10L);
        baseBook.getAsks().put(8096.0, 5L);
        baseBook.setTimestamp(currentTime);
        
        // 转换为OrderBookSnapshot
        OrderBookSnapshot baseSnapshot = new OrderBookSnapshot(baseBook.getInstrumentId());
        baseSnapshot.setBids(baseBook.getBids());
        baseSnapshot.setAsks(baseBook.getAsks());
        baseSnapshot.setTimestamp(baseBook.getTimestamp());
        
        // 验证转换结果
        assert baseSnapshot.getContract_cde().equals("EB2504") : "快照合约代码应该正确";
        assert baseSnapshot.getTimestamp() == currentTime : "快照时间戳应该正确";
        assert baseSnapshot.getBestBid().equals(8095.0) : "快照最优买价应该正确";
        assert baseSnapshot.getBestAsk().equals(8096.0) : "快照最优卖价应该正确";
        
        System.out.println("*** 订单簿快照转换测试:");
        System.out.println("原始订单簿: " + baseBook);
        System.out.println("转换后快照: 合约=" + baseSnapshot.getContract_cde() + 
                ", 时间戳=" + baseSnapshot.getTimestamp() + 
                ", 买一=" + baseSnapshot.getBestBid() + 
                ", 卖一=" + baseSnapshot.getBestAsk());
        
        logger.info("订单簿快照转换测试通过");
    }
    
    @Test
    public void testLayeredSnapshotMerge() {
        logger.info("开始测试分层快照合并");
        
        long currentTime = System.currentTimeMillis();
        
        // 创建基础层订单簿
        BaseOrderBook baseBook = new BaseOrderBook("EB2504");
        baseBook.getBids().put(8095.0, 10L);
        baseBook.getAsks().put(8096.0, 5L);
        baseBook.setTimestamp(currentTime);
        
        // 创建虚拟层订单簿
        VirtualOrderBook virtualBook = new VirtualOrderBook("EB2504");
        virtualBook.addVirtualBid(8093.0, 3L);
        virtualBook.addVirtualAsk(8098.0, 2L);
        virtualBook.setTimestamp(currentTime);
        
        // 合并分层快照
        LayeredOrderBookSnapshot layeredSnapshot = LayeredOrderBookSnapshot.merge(baseBook, virtualBook);
        
        // 验证合并结果
        assert layeredSnapshot.getInstrumentId().equals("EB2504") : "合并快照合约代码应该正确";
        assert layeredSnapshot.hasData() : "合并快照应该有数据";
        assert layeredSnapshot.hasMixedLayers() : "合并快照应该包含混合层";
        
        // 验证总订单簿包含所有数据
        assert layeredSnapshot.getTotalBids().containsKey(8095.0) : "总买盘应该包含基础层数据";
        assert layeredSnapshot.getTotalBids().containsKey(8093.0) : "总买盘应该包含虚拟层数据";
        assert layeredSnapshot.getTotalAsks().containsKey(8096.0) : "总卖盘应该包含基础层数据";
        assert layeredSnapshot.getTotalAsks().containsKey(8098.0) : "总卖盘应该包含虚拟层数据";
        
        System.out.println("*** 分层快照合并测试:");
        System.out.println("合并快照: " + layeredSnapshot);
        System.out.println("总买盘档数: " + layeredSnapshot.getTotalBids().size());
        System.out.println("总卖盘档数: " + layeredSnapshot.getTotalAsks().size());
        
        logger.info("分层快照合并测试通过");
    }
    
    @Test
    public void testTimestampFormatting() {
        logger.info("开始测试时间戳格式化");
        
        long currentTime = System.currentTimeMillis();
        
        // 测试时间戳格式化
        String formattedTime = java.time.LocalDateTime.ofInstant(
                java.time.Instant.ofEpochMilli(currentTime),
                java.time.ZoneId.of("Asia/Shanghai")
        ).format(java.time.format.DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss.SSS"));
        
        System.out.println("*** 时间戳格式化测试:");
        System.out.println("原始时间戳: " + currentTime);
        System.out.println("格式化时间: " + formattedTime);
        
        assert formattedTime != null && !formattedTime.isEmpty() : "格式化时间不应该为空";
        assert formattedTime.contains("2025") : "格式化时间应该包含年份";
        
        logger.info("时间戳格式化测试通过");
    }
}
