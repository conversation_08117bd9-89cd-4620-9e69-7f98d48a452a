package com.futures.function;

import com.futures.pojo.*;
import org.junit.Test;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Arrays;
import java.util.List;

/**
 * 分层订单簿架构测试
 * 验证基础层和虚拟层的功能正确性
 */
public class LayeredOrderBookTest {
    private static final Logger logger = LoggerFactory.getLogger(LayeredOrderBookTest.class);
    
    @Test
    public void testBBO_UpdateCreation() {
        logger.info("开始测试BBO_Update数据结构");

        // 测试BBO_Update的创建和基本功能
        BBO_Update bbo = new BBO_Update("EB2504", 2500.0, 2510.0, 10L, 5L, System.currentTimeMillis());

        // 验证基本属性
        assert bbo.getInstrumentId().equals("EB2504");
        assert bbo.getBestBid() == 2500.0;
        assert bbo.getBestAsk() == 2510.0;
        assert bbo.getBestBidVolume() == 10L;
        assert bbo.getBestAskVolume() == 5L;

        // 验证计算方法
        assert bbo.isValid();
        assert bbo.getSpread() == 10.0;
        assert bbo.getMidPrice() == 2505.0;

        logger.info("BBO_Update测试通过: {}", bbo);
    }
    
    @Test
    public void testBaseOrderBookCreation() {
        logger.info("开始测试基础层订单簿数据结构");

        // 测试BaseOrderBook的创建和基本功能
        BaseOrderBook baseBook = new BaseOrderBook("EB2504");

        // 添加测试数据
        baseBook.getBids().put(2500.0, 10L);
        baseBook.getBids().put(2499.0, 5L);
        baseBook.getAsks().put(2510.0, 8L);
        baseBook.getAsks().put(2511.0, 3L);

        // 验证基本功能
        assert baseBook.getInstrumentId().equals("EB2504");
        assert !baseBook.isEmpty();
        assert baseBook.getBestBid().equals(2500.0);
        assert baseBook.getBestAsk().equals(2510.0);
        assert baseBook.getBestBidVolume().equals(10L);
        assert baseBook.getBestAskVolume().equals(8L);
        assert baseBook.getSpread() == 10.0;
        assert baseBook.getBidDepth() == 2;
        assert baseBook.getAskDepth() == 2;

        // 测试BBO生成
        BBO_Update bbo = baseBook.generateBBO();
        assert bbo.getInstrumentId().equals("EB2504");
        assert bbo.getBestBid() == 2500.0;
        assert bbo.getBestAsk() == 2510.0;

        logger.info("BaseOrderBook测试通过: {}", baseBook);
    }
    
    @Test
    public void testVirtualOrderBookCreation() {
        logger.info("开始测试虚拟层订单簿数据结构");

        // 测试VirtualOrderBook的创建和基本功能
        VirtualOrderBook virtualBook = new VirtualOrderBook("EB2504");
        virtualBook.setSourceComboContract("SPD_EB2504_EB2505");

        // 添加虚拟挂单
        virtualBook.addVirtualBid(2498.0, 5L);
        virtualBook.addVirtualBid(2497.0, 3L);
        virtualBook.addVirtualAsk(2512.0, 4L);
        virtualBook.addVirtualAsk(2513.0, 2L);

        // 验证基本功能
        assert virtualBook.getInstrumentId().equals("EB2504");
        assert virtualBook.getSourceComboContract().equals("SPD_EB2504_EB2505");
        assert !virtualBook.isEmpty();
        assert virtualBook.getBestVirtualBid().equals(2498.0);
        assert virtualBook.getBestVirtualAsk().equals(2512.0);
        assert virtualBook.getBestVirtualBidVolume().equals(5L);
        assert virtualBook.getBestVirtualAskVolume().equals(4L);
        assert virtualBook.getVirtualSpread() == 14.0;
        assert virtualBook.getVirtualBidDepth() == 2;
        assert virtualBook.getVirtualAskDepth() == 2;

        logger.info("VirtualOrderBook测试通过: {}", virtualBook);
    }
    
    @Test
    public void testLayeredOrderBookSnapshotMerge() {
        logger.info("开始测试分层订单簿快照合并");

        // 创建基础层订单簿
        BaseOrderBook baseBook = new BaseOrderBook("EB2504");
        baseBook.getBids().put(2500.0, 10L);
        baseBook.getAsks().put(2510.0, 5L);

        // 创建虚拟层订单簿
        VirtualOrderBook virtualBook = new VirtualOrderBook("EB2504");
        virtualBook.addVirtualBid(2498.0, 3L);
        virtualBook.addVirtualAsk(2512.0, 2L);

        // 测试合并
        LayeredOrderBookSnapshot layeredSnapshot = LayeredOrderBookSnapshot.merge(baseBook, virtualBook);

        // 验证合并结果
        assert layeredSnapshot.getInstrumentId().equals("EB2504");
        assert layeredSnapshot.hasData();
        assert layeredSnapshot.hasMixedLayers();
        assert !layeredSnapshot.hasOnlyBaseLayer();
        assert !layeredSnapshot.hasOnlyVirtualLayer();

        // 验证总订单簿包含基础层和虚拟层的数据
        assert layeredSnapshot.getTotalBids().containsKey(2500.0); // 基础层买单
        assert layeredSnapshot.getTotalBids().containsKey(2498.0); // 虚拟层买单
        assert layeredSnapshot.getTotalAsks().containsKey(2510.0); // 基础层卖单
        assert layeredSnapshot.getTotalAsks().containsKey(2512.0); // 虚拟层卖单

        logger.info("LayeredOrderBookSnapshot测试通过: {}", layeredSnapshot);
    }
}
