package com.futures.job;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import static org.junit.jupiter.api.Assertions.*;

import java.io.BufferedReader;
import java.io.FileReader;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * Order Book Logic Test - JUnit version
 */
public class OrderBookLogicTest {

    @BeforeEach
    void setUp() {
        System.out.println("========================================");
        System.out.println("Order Book Logic Test");
        System.out.println("========================================");
    }

    @Test
    @DisplayName("Test Order Book Reconstruction Logic")
    void testOrderBookReconstructionLogic() throws Exception {
        System.out.println("\n=== Order Book Reconstruction Logic Test ===");
        
        // Simulate order book
        Map<Double, Long> bids = new TreeMap<>(Collections.reverseOrder());
        Map<Double, Long> asks = new TreeMap<>();
        
        StringBuilder jsonContent = new StringBuilder();
        try (BufferedReader reader = new BufferedReader(new FileReader("data/single_leg_orders.json"))) {
            String line;
            while ((line = reader.readLine()) != null) {
                jsonContent.append(line).append("\n");
            }
        }
        
        String content = jsonContent.toString();
        List<String> jsonObjects = extractJsonObjects(content);
        
        int processedOrders = 0;
        Map<String, Integer> statusActions = new HashMap<>();
        
        for (String jsonObj : jsonObjects) {
            try {
                String ordNbr = extractJsonValue(jsonObj, "ord_nbr");
                String ordSts = extractJsonValue(jsonObj, "ord_sts");
                String bsTag = extractJsonValue(jsonObj, "b_s_tag");
                String ordPrcStr = extractJsonValue(jsonObj, "ord_prc");
                String rmnVolStr = extractJsonValue(jsonObj, "rmn_vol");
                
                if (ordPrcStr.isEmpty() || rmnVolStr.isEmpty()) continue;
                
                double price = Double.parseDouble(ordPrcStr);
                long remainingVol = (long) Double.parseDouble(rmnVolStr);
                
                String action = processOrderStatus(ordSts, bsTag, price, remainingVol, bids, asks);
                statusActions.put(action, statusActions.getOrDefault(action, 0) + 1);
                
                if (processedOrders < 5) {
                    System.out.println(String.format("Order %d: %s, Status=%s, BuySell=%s, Price=%.2f, RemainingVol=%d -> Action: %s", 
                        processedOrders + 1, ordNbr, ordSts, bsTag, price, remainingVol, action));
                }
                processedOrders++;
                
            } catch (Exception e) {
                // Ignore parsing errors
            }
        }
        
        System.out.println(String.format("\nProcessed %d orders", processedOrders));
        System.out.println("Final order book state:");
        System.out.println(String.format("  Bid levels: %d", bids.size()));
        System.out.println(String.format("  Ask levels: %d", asks.size()));
        
        // Assertions
        assertTrue(processedOrders > 0, "Should process at least one order");
        assertTrue(statusActions.size() > 0, "Should have order status actions");
    }

    // Helper methods
    private String processOrderStatus(String orderStatus, String buySellTag, double price, long remainingVol, 
                                     Map<Double, Long> bids, Map<Double, Long> asks) {
        
        if ("1".equals(orderStatus) || "3".equals(orderStatus)) {
            if (remainingVol > 0) {
                if ("B".equals(buySellTag)) {
                    bids.put(price, remainingVol);
                    return "Added/Updated in order book (bid)";
                } else {
                    asks.put(price, remainingVol);
                    return "Added/Updated in order book (ask)";
                }
            }
            return "In queue but no remaining volume";
        } else if ("0".equals(orderStatus) || "2".equals(orderStatus) || "4".equals(orderStatus) || "5".equals(orderStatus)) {
            if ("B".equals(buySellTag)) {
                bids.remove(price);
                return "Removed from order book (bid) - " + getRemovalReason(orderStatus);
            } else {
                asks.remove(price);
                return "Removed from order book (ask) - " + getRemovalReason(orderStatus);
            }
        } else {
            return "Unknown status";
        }
    }

    private String getRemovalReason(String orderStatus) {
        switch (orderStatus) {
            case "0": return "Fully filled";
            case "2": return "Partially filled, not in queue";
            case "4": return "Unfilled, not in queue";
            case "5": return "Cancelled";
            default: return "Unknown reason";
        }
    }

    private List<String> extractJsonObjects(String content) {
        List<String> objects = new ArrayList<>();
        int braceCount = 0;
        int start = -1;
        boolean inString = false;
        boolean escaped = false;
        
        for (int i = 0; i < content.length(); i++) {
            char c = content.charAt(i);
            
            if (escaped) {
                escaped = false;
                continue;
            }
            
            if (c == '\\' && inString) {
                escaped = true;
                continue;
            }
            
            if (c == '"') {
                inString = !inString;
                continue;
            }
            
            if (!inString) {
                if (c == '{') {
                    if (braceCount == 0) {
                        start = i;
                    }
                    braceCount++;
                } else if (c == '}') {
                    braceCount--;
                    if (braceCount == 0 && start != -1) {
                        objects.add(content.substring(start, i + 1));
                        start = -1;
                    }
                }
            }
        }
        
        return objects;
    }

    private String extractJsonValue(String json, String key) {
        Pattern pattern = Pattern.compile("\"" + Pattern.quote(key) + "\"\\s*:\\s*([^,}\\]]+|\"[^\"]*\")");
        Matcher matcher = pattern.matcher(json);
        
        if (matcher.find()) {
            String value = matcher.group(1).trim();
            if (value.startsWith("\"") && value.endsWith("\"")) {
                return value.substring(1, value.length() - 1);
            }
            return value;
        }
        
        return "";
    }
}
