#!/bin/bash

# 期货系统 Docker Kafka 管理脚本

COMPOSE_FILE="docker-compose.yml"

show_usage() {
    echo "期货系统 Docker Kafka 管理脚本"
    echo ""
    echo "使用方法: $0 [命令]"
    echo ""
    echo "命令:"
    echo "  start     - 启动Kafka集群"
    echo "  stop      - 停止Kafka集群" 
    echo "  restart   - 重启Kafka集群"
    echo "  status    - 查看Kafka集群状态"
    echo "  logs      - 查看Kafka日志"
    echo "  topics    - 列出所有topic"
    echo "  create    - 创建测试topics"
    echo "  test      - 测试Kafka连接"
    echo "  clean     - 清理所有容器和数据"
    echo "  ui        - 打开Kafka UI (http://localhost:8080)"
    echo ""
}

check_docker() {
    if ! command -v docker &> /dev/null; then
        echo "错误: 未安装Docker"
        exit 1
    fi
    
    if ! command -v docker-compose &> /dev/null && ! docker compose version &> /dev/null; then
        echo "错误: 未安装Docker Compose"
        exit 1
    fi
    
    if ! docker info &> /dev/null; then
        echo "错误: Docker daemon未运行"
        exit 1
    fi
}

start_kafka() {
    echo "启动Kafka集群..."
    if docker compose version &> /dev/null; then
        docker compose -f $COMPOSE_FILE up -d
    else
        docker-compose -f $COMPOSE_FILE up -d
    fi
    
    if [ $? -eq 0 ]; then
        echo "Kafka集群启动成功！"
        echo ""
        echo "等待服务就绪..."
        sleep 10
        
        echo "服务状态:"
        docker ps --filter "name=futures-" --format "table {{.Names}}\t{{.Status}}\t{{.Ports}}"
        
        echo ""
        echo "连接信息:"
        echo "  Kafka Bootstrap Server: localhost:9092"
        echo "  Zookeeper: localhost:2181"
        echo "  Kafka UI: http://localhost:8080"
        
        # 自动创建测试topics
        echo ""
        echo "创建测试topics..."
        sleep 5
        create_topics
    else
        echo "Kafka集群启动失败！"
        exit 1
    fi
}

stop_kafka() {
    echo "停止Kafka集群..."
    if docker compose version &> /dev/null; then
        docker compose -f $COMPOSE_FILE down
    else
        docker-compose -f $COMPOSE_FILE down
    fi
    echo "Kafka集群已停止"
}

restart_kafka() {
    echo "重启Kafka集群..."
    stop_kafka
    sleep 3
    start_kafka
}

show_status() {
    echo "Kafka集群状态:"
    docker ps --filter "name=futures-" --format "table {{.Names}}\t{{.Status}}\t{{.Ports}}"
    
    echo ""
    echo "容器健康检查:"
    
    # 检查Zookeeper
    if docker exec futures-zookeeper zkCli.sh ls / &> /dev/null; then
        echo "  ✅ Zookeeper: 正常运行"
    else
        echo "  ❌ Zookeeper: 不可访问"
    fi
    
    # 检查Kafka
    if docker exec futures-kafka kafka-broker-api-versions --bootstrap-server localhost:9092 &> /dev/null; then
        echo "  ✅ Kafka: 正常运行"
    else
        echo "  ❌ Kafka: 不可访问"
    fi
}

show_logs() {
    echo "选择要查看的服务日志:"
    echo "1. Kafka"
    echo "2. Zookeeper" 
    echo "3. Kafka-UI"
    echo "4. 所有服务"
    
    read -p "请选择 (1-4): " choice
    
    case $choice in
        1)
            docker logs -f futures-kafka
            ;;
        2)
            docker logs -f futures-zookeeper
            ;;
        3)
            docker logs -f futures-kafka-ui
            ;;
        4)
            if docker compose version &> /dev/null; then
                docker compose -f $COMPOSE_FILE logs -f
            else
                docker-compose -f $COMPOSE_FILE logs -f
            fi
            ;;
        *)
            echo "无效选择"
            ;;
    esac
}

list_topics() {
    echo "列出所有Kafka topics:"
    docker exec futures-kafka kafka-topics --bootstrap-server localhost:9092 --list
}

create_topics() {
    echo "创建期货系统测试topics..."
    
    topics=(
        "singleleg_order_data_event"
        "cmb_order_data_event" 
        "trade_data_event"
    )
    
    for topic in "${topics[@]}"; do
        docker exec futures-kafka kafka-topics \
            --bootstrap-server localhost:9092 \
            --create --if-not-exists \
            --topic $topic \
            --partitions 1 \
            --replication-factor 1
        
        if [ $? -eq 0 ]; then
            echo "  ✅ Topic创建成功: $topic"
        else
            echo "  ❌ Topic创建失败: $topic"
        fi
    done
    
    echo ""
    echo "验证topics:"
    list_topics
}

test_connection() {
    echo "测试Kafka连接..."
    
    # 测试生产者
    echo "测试消息生产..."
    echo "test-message-$(date +%s)" | docker exec -i futures-kafka kafka-console-producer \
        --bootstrap-server localhost:9092 \
        --topic test-connection
    
    if [ $? -eq 0 ]; then
        echo "  ✅ 消息生产测试成功"
    else
        echo "  ❌ 消息生产测试失败"
        return 1
    fi
    
    # 测试消费者（timeout 5秒）
    echo "测试消息消费..."
    timeout 5s docker exec futures-kafka kafka-console-consumer \
        --bootstrap-server localhost:9092 \
        --topic test-connection \
        --from-beginning \
        --max-messages 1 &> /dev/null
    
    if [ $? -eq 0 ]; then
        echo "  ✅ 消息消费测试成功"
        echo "  ✅ Kafka连接测试通过！"
    else
        echo "  ❌ 消息消费测试失败"
        return 1
    fi
    
    # 清理测试topic
    docker exec futures-kafka kafka-topics \
        --bootstrap-server localhost:9092 \
        --delete --topic test-connection &> /dev/null
}

clean_kafka() {
    echo "警告: 这将删除所有Kafka容器和数据！"
    read -p "确认继续? (y/N): " -n 1 -r
    echo
    
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        echo "清理Kafka环境..."
        
        # 停止并删除容器
        if docker compose version &> /dev/null; then
            docker compose -f $COMPOSE_FILE down -v
        else
            docker-compose -f $COMPOSE_FILE down -v
        fi
        
        # 删除相关镜像（可选）
        read -p "是否同时删除Docker镜像? (y/N): " -n 1 -r
        echo
        if [[ $REPLY =~ ^[Yy]$ ]]; then
            docker rmi confluentinc/cp-kafka:7.4.0 confluentinc/cp-zookeeper:7.4.0 provectuslabs/kafka-ui:latest 2>/dev/null || true
        fi
        
        echo "清理完成"
    else
        echo "取消清理"
    fi
}

open_ui() {
    if command -v open &> /dev/null; then
        open http://localhost:8080
    elif command -v xdg-open &> /dev/null; then
        xdg-open http://localhost:8080
    else
        echo "请手动打开浏览器访问: http://localhost:8080"
    fi
}

# 主程序
main() {
    check_docker
    
    case "${1:-help}" in
        start)
            start_kafka
            ;;
        stop)
            stop_kafka
            ;;
        restart)
            restart_kafka
            ;;
        status)
            show_status
            ;;
        logs)
            show_logs
            ;;
        topics)
            list_topics
            ;;
        create)
            create_topics
            ;;
        test)
            test_connection
            ;;
        clean)
            clean_kafka
            ;;
        ui)
            open_ui
            ;;
        help|--help|-h)
            show_usage
            ;;
        *)
            echo "未知命令: $1"
            echo ""
            show_usage
            exit 1
            ;;
    esac
}

main "$@"
