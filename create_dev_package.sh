#!/bin/bash

# 期货订单簿系统 - 离线开发环境部署包生成脚本
# 使用方法: ./create_dev_package.sh

set -e

echo "🚀 开始创建离线开发环境部署包..."

# 定义目录和文件名
DEV_DEPLOY_DIR="futures-dev-offline"
TAR_FILE="${DEV_DEPLOY_DIR}.tar.gz"
LOCAL_REPO_DIR="local-repo"

# 1. 清理旧文件
echo "📦 清理旧的部署文件..."
rm -rf $DEV_DEPLOY_DIR
rm -f $TAR_FILE

# 2. 创建部署目录结构
echo "📁 创建部署目录: $DEV_DEPLOY_DIR"
mkdir -p "$DEV_DEPLOY_DIR/$LOCAL_REPO_DIR"
mkdir -p "$DEV_DEPLOY_DIR/data"
mkdir -p "$DEV_DEPLOY_DIR/logs"

# 3. 下载所有依赖到本地仓库
echo "🚚 下载所有项目依赖到本地仓库..."
# 使用 package 和 dependency:go-offline 确保所有依赖和插件都被下载
mvn package dependency:go-offline -Dmaven.repo.local="$DEV_DEPLOY_DIR/$LOCAL_REPO_DIR" -q
echo "✅ 依赖下载完成。"

# 4. 复制项目文件
echo "📋 复制项目源代码和配置文件..."
cp -r src "$DEV_DEPLOY_DIR/"
cp pom.xml "$DEV_DEPLOY_DIR/"
cp -r data/* "$DEV_DEPLOY_DIR/data/"

# 5. 创建离线构建脚本
echo "📝 创建离线构建脚本 (build.sh)..."
cat > "$DEV_DEPLOY_DIR/build.sh" << 'EOF'
#!/bin/bash

# 离线编译脚本

set -e

echo "🚀 开始在离线环境编译项目..."
echo "   使用本地仓库: ./local-repo"

# 使用 -o (offline) 参数和本地仓库地址进行编译
mvn clean package -o -Dmaven.repo.local=./local-repo

echo "✅ 编译完成！"
echo "   新的JAR包位于: target/futures-orderbook-rebuild-1.0.0.jar"
EOF

# 6. 创建离线运行脚本
echo "📝 创建离线运行脚本 (run.sh)..."
cat > "$DEV_DEPLOY_DIR/run.sh" << 'EOF'
#!/bin/bash

# 离线运行脚本

set -e

JAR_FILE="target/futures-orderbook-rebuild-1.0.0.jar"

if [ ! -f "$JAR_FILE" ]; then
    echo "❌ 错误: 未找到JAR文件: $JAR_FILE"
    echo "   请先运行 ./build.sh 进行编译。"
    exit 1
fi

echo "🚀 启动新编译的系统..."

# JVM参数
JVM_OPTS="-Xms1g -Xmx2g"
JVM_OPTS="$JVM_OPTS --add-opens java.base/java.util=ALL-UNNAMED"
JVM_OPTS="$JVM_OPTS --add-opens java.base/java.lang=ALL-UNNAMED"

java $JVM_OPTS -jar $JAR_FILE
EOF

# 7. 创建部署说明
echo "📝 创建部署说明文档..."
cat > "$DEV_DEPLOY_DIR/README-dev.md" << 'EOF'
# 离线开发环境部署指南

本部署包包含了完整的开发环境，允许您在没有网络连接的服务器上修改代码、重新编译和运行。

## 部署步骤

1.  **上传并解压**
    ```bash
    # 上传 futures-dev-offline.tar.gz 到服务器
    tar -xzf futures-dev-offline.tar.gz
    cd futures-dev-offline
    ```

2.  **修改代码**
    您可以直接修改 `src/` 目录下的Java源代码。
    ```bash
    # 例如，编辑主作业文件
    vim src/main/java/com/futures/job/FuturesOrderBookJob.java
    ```

3.  **重新编译**
    修改代码后，使用 `build.sh` 脚本进行编译。此脚本会使用包内自带的 `local-repo` 依赖库，完全无需联网。
    ```bash
    ./build.sh
    ```
    编译成功后，新的JAR包会生成在 `target/` 目录下。

4.  **运行新程序**
    使用 `run.sh` 脚本启动最新编译的JAR包。
    ```bash
    ./run.sh
    ```

## 脚本说明

-   `build.sh`: 在离线模式下，使用本地依赖重新编译整个项目。
-   `run.sh`: 运行 `target/` 目录下最新编译的JAR文件。
-   `local-repo/`: 包含了项目所需的所有Maven依赖包。
-   `src/`: 项目的完整Java源代码。
-   `pom.xml`: Maven项目配置文件。

## 注意事项

-   请确保服务器上已安装 **Maven** 和 **Java 11+**。
-   任何 `pom.xml` 的修改（如增加新依赖）都需要在有网络的环境下重新生成部署包，因为新的依赖需要从互联网下载。
EOF

# 8. 设置脚本执行权限
chmod +x "$DEV_DEPLOY_DIR/build.sh"
chmod +x "$DEV_DEPLOY_DIR/run.sh"

# 9. 创建压缩包
echo "🗜️ 创建最终的压缩部署包..."
tar -czf $TAR_FILE $DEV_DEPLOY_DIR/

# 10. 完成
PACKAGE_SIZE=$(du -h $TAR_FILE | cut -f1)
echo ""
echo "✅ 离线开发部署包创建完成!"
echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"
echo "📦 包文件: $TAR_FILE"
echo "📏 包大小: $PACKAGE_SIZE"
echo ""
echo "🚀 部署流程:"
echo "1. 上传 $TAR_FILE 到服务器并解压"
echo "2. 修改 src/ 目录下的代码"
echo "3. 运行 ./build.sh 重新编译"
echo "4. 运行 ./run.sh 启动新程序"
echo ""
echo "📚 详细说明请查看解压后的 README-dev.md 文件"
echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"
